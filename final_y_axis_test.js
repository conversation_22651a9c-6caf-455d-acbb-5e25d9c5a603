// Y轴功能最终测试脚本
// 在浏览器控制台中运行此脚本来验证Y轴功能

console.log('🧪 开始Y轴功能最终测试...');

// 测试1: 检查Y轴元素是否存在
function testYAxisElements() {
    console.log('\n📋 测试1: Y轴元素检查');
    
    const yMainHeaders = document.querySelectorAll('tbody th:first-child');
    const ySubHeaders = document.querySelectorAll('tbody th:nth-child(2)');
    
    console.log(`主分类标签数量: ${yMainHeaders.length}`);
    console.log(`子分类标签数量: ${ySubHeaders.length}`);
    
    if (yMainHeaders.length > 0 || ySubHeaders.length > 0) {
        console.log('✅ Y轴元素检查通过');
        return true;
    } else {
        console.log('❌ Y轴元素检查失败');
        return false;
    }
}

// 测试2: 检查Sticky定位
function testStickyPositioning() {
    console.log('\n📌 测试2: Sticky定位检查');
    
    const yHeaders = document.querySelectorAll('tbody th:first-child, tbody th:nth-child(2)');
    let stickyCount = 0;
    let positionErrors = [];
    
    yHeaders.forEach((header, index) => {
        const styles = window.getComputedStyle(header);
        const position = styles.position;
        const left = styles.left;
        const top = styles.top;
        const zIndex = styles.zIndex;
        
        console.log(`标签 ${index + 1}:`, {
            position,
            left,
            top,
            zIndex,
            cellIndex: header.cellIndex
        });
        
        if (position === 'sticky') {
            stickyCount++;
        } else {
            positionErrors.push(`标签${index + 1}: position=${position}`);
        }
        
        // 检查left值
        if (header.cellIndex === 0 && left !== '0px') {
            positionErrors.push(`主分类标签${index + 1}: left=${left} (应为0px)`);
        }
        if (header.cellIndex === 1 && left !== '120px') {
            positionErrors.push(`子分类标签${index + 1}: left=${left} (应为120px)`);
        }
        
        // 检查top值
        if (top !== '108px') {
            positionErrors.push(`标签${index + 1}: top=${top} (应为108px)`);
        }
    });
    
    console.log(`Sticky定位标签数量: ${stickyCount}/${yHeaders.length}`);
    
    if (positionErrors.length === 0) {
        console.log('✅ Sticky定位检查通过');
        return true;
    } else {
        console.log('❌ Sticky定位检查失败:');
        positionErrors.forEach(error => console.log(`  - ${error}`));
        return false;
    }
}

// 测试3: 检查滚动行为
function testScrollBehavior() {
    console.log('\n🔄 测试3: 滚动行为检查');
    
    const productCanvas = document.getElementById('productCanvas');
    if (!productCanvas) {
        console.log('❌ 未找到产品画布容器');
        return false;
    }
    
    // 记录原始滚动位置
    const originalScrollLeft = productCanvas.scrollLeft;
    const originalScrollTop = productCanvas.scrollTop;
    
    console.log(`原始滚动位置: left=${originalScrollLeft}, top=${originalScrollTop}`);
    
    // 测试水平滚动
    productCanvas.scrollLeft = 200;
    console.log('执行水平滚动到200px...');
    
    setTimeout(() => {
        const yHeaders = document.querySelectorAll('tbody th:first-child, tbody th:nth-child(2)');
        let horizontalStickiness = true;
        let positionErrors = [];
        
        yHeaders.forEach((header, index) => {
            const rect = header.getBoundingClientRect();
            const canvasRect = productCanvas.getBoundingClientRect();
            
            const expectedLeft = header.cellIndex === 0 ? canvasRect.left : canvasRect.left + 120;
            const actualLeft = rect.left;
            const tolerance = 5; // 5px容差
            
            if (Math.abs(actualLeft - expectedLeft) > tolerance) {
                horizontalStickiness = false;
                positionErrors.push(`标签${index + 1}: 期望left=${expectedLeft}, 实际left=${actualLeft}`);
            }
        });
        
        // 恢复原始滚动位置
        productCanvas.scrollLeft = originalScrollLeft;
        productCanvas.scrollTop = originalScrollTop;
        
        if (horizontalStickiness) {
            console.log('✅ 滚动行为检查通过');
        } else {
            console.log('❌ 滚动行为检查失败:');
            positionErrors.forEach(error => console.log(`  - ${error}`));
        }
        
        return horizontalStickiness;
    }, 100);
}

// 测试4: 检查初始化函数
function testInitializationFunctions() {
    console.log('\n🚀 测试4: 初始化函数检查');
    
    const functions = [
        'initializeYAxis',
        'setupYAxisScrollListener',
        'processYAxisTextReverse',
        'ensureYAxisStyles',
        'validateYAxisPosition'
    ];
    
    let availableFunctions = 0;
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName}: 可用`);
            availableFunctions++;
        } else {
            console.log(`❌ ${funcName}: 不可用`);
        }
    });
    
    console.log(`可用函数: ${availableFunctions}/${functions.length}`);
    
    // 测试主初始化函数
    if (typeof window.initializeYAxis === 'function') {
        try {
            console.log('测试调用 initializeYAxis()...');
            window.initializeYAxis();
            console.log('✅ initializeYAxis() 调用成功');
        } catch (error) {
            console.log(`❌ initializeYAxis() 调用失败: ${error.message}`);
        }
    }
    
    return availableFunctions === functions.length;
}

// 测试5: 检查样式应用
function testStyleApplication() {
    console.log('\n🎨 测试5: 样式应用检查');
    
    const yMainHeaders = document.querySelectorAll('tbody th:first-child');
    const ySubHeaders = document.querySelectorAll('tbody th:nth-child(2)');
    
    let styleErrors = [];
    
    // 检查主分类样式
    yMainHeaders.forEach((header, index) => {
        const styles = window.getComputedStyle(header);
        
        if (!header.classList.contains('y-main-category-header')) {
            styleErrors.push(`主分类标签${index + 1}: 缺少y-main-category-header类`);
        }
        
        if (styles.writingMode !== 'vertical-rl') {
            styleErrors.push(`主分类标签${index + 1}: writing-mode=${styles.writingMode} (应为vertical-rl)`);
        }
        
        if (styles.textOrientation !== 'upright') {
            styleErrors.push(`主分类标签${index + 1}: text-orientation=${styles.textOrientation} (应为upright)`);
        }
    });
    
    // 检查子分类样式
    ySubHeaders.forEach((header, index) => {
        const styles = window.getComputedStyle(header);
        
        if (!header.classList.contains('y-sub-category-header')) {
            styleErrors.push(`子分类标签${index + 1}: 缺少y-sub-category-header类`);
        }
        
        if (styles.writingMode !== 'vertical-rl') {
            styleErrors.push(`子分类标签${index + 1}: writing-mode=${styles.writingMode} (应为vertical-rl)`);
        }
    });
    
    if (styleErrors.length === 0) {
        console.log('✅ 样式应用检查通过');
        return true;
    } else {
        console.log('❌ 样式应用检查失败:');
        styleErrors.forEach(error => console.log(`  - ${error}`));
        return false;
    }
}

// 运行所有测试
function runAllTests() {
    console.log('🧪 开始运行所有Y轴测试...');
    console.log('=' * 50);
    
    const tests = [
        { name: 'Y轴元素检查', func: testYAxisElements },
        { name: 'Sticky定位检查', func: testStickyPositioning },
        { name: '初始化函数检查', func: testInitializationFunctions },
        { name: '样式应用检查', func: testStyleApplication }
    ];
    
    let passedTests = 0;
    
    tests.forEach(test => {
        try {
            const result = test.func();
            if (result) {
                passedTests++;
            }
        } catch (error) {
            console.log(`❌ ${test.name} 执行出错: ${error.message}`);
        }
    });
    
    // 滚动测试需要异步执行
    testScrollBehavior();
    
    console.log('\n' + '=' * 50);
    console.log('📊 测试结果总结');
    console.log('=' * 50);
    console.log(`通过测试: ${passedTests}/${tests.length}`);
    console.log(`成功率: ${(passedTests/tests.length*100).toFixed(1)}%`);
    
    if (passedTests === tests.length) {
        console.log('\n🎉 所有Y轴测试通过！');
        console.log('✅ Y轴重写成功完成');
    } else {
        console.log(`\n⚠️ 还有 ${tests.length - passedTests} 项测试未通过`);
    }
    
    console.log('=' * 50);
}

// 自动运行测试
runAllTests();
