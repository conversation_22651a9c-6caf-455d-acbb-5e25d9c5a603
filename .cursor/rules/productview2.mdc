---
description: 
globs: 
alwaysApply: false
---
1.技术栈统一 (Frameworks & Libraries):
前端框架: 严格使用 React (v18+) 进行开发。
UI 组件库: 优先且主要使用 Ant Design (AntD v5+) 组件。若有 AntD 无法满足的特殊组件需求，需团队讨论并记录。
构建工具: 使用 Vite 作为项目构建和开发服务器。
语言: 主要使用 TypeScript (TS) 进行编码，充分利用其类型系统。.tsx 用于组件，.ts 用于工具函数和类型定义。
2.代码风格与格式化 (Code Style & Formatting):
代码格式化: 强制使用 Prettier 进行代码自动格式化。配置文件 (.prettierrc) 将定义统一规则（例如：使用单引号、行尾分号、tab宽度2个空格等）。
代码规范检查 (Linting): 强制使用 ESLint 进行代码规范检查。配置文件 (eslint.config.js 或 .eslintrc.js) 将集成推荐规则集 (如 eslint-plugin-react, eslint-plugin-react-hooks, @typescript-eslint/eslint-plugin)。
缩进: 统一使用 空格 (2个空格) 进行缩进，而非 Tab。
3.命名规范 (Naming Conventions):
组件: PascalCase (如 ProductCard.tsx, NoteModal.tsx)。
函数/变量: camelCase (如 handleSaveNote, isLoadingData)。
常量: UPPER_SNAKE_CASE (如 MAX_PRODUCTS_DISPLAY)。
CSS 类名 (若使用): BEM (Block Element Modifier) 风格或 kebab-case，并推荐使用 CSS Modules 或 Styled Components 来避免全局污染。
类型/接口: PascalCase，并建议以 I 前缀 (如 IProduct, INoteModalProps) 或 Type / Props / State 后缀。
4.组件设计与状态管理 (Component Design & State Management):
组件拆分: 鼓励将UI拆分为小型、可复用、单一职责的组件。考虑遵循原子设计（Atomic Design）或类似的分层思想（如展示组件 vs 容器组件）。
状态管理:
组件内部状态优先使用 React Hooks (useState, useReducer)。
跨组件状态共享，优先考虑 React Context API。
对于更复杂或全局的状态管理，若有必要，团队讨论引入如 Zustand 或 Redux Toolkit，但初期以轻量级方案为主。
5.API 交互与数据处理 (API Interaction & Data Handling):
API 请求: 统一封装 API 请求函数（例如，使用 fetch 或轻量级库如 axios）。请求封装应包含统一的错误处理、loading状态管理、以及可能的请求/响应拦截。
类型定义: 所有 API 的请求参数和响应数据都必须有明确的 TypeScript 类型定义。
6.代码注释与文档 (Comments & Documentation):
复杂逻辑: 对复杂的业务逻辑、算法或非常规实现必须添加清晰的注释。
组件 Props: 对组件的 Props 使用 JSDoc 或 TS 类型注释进行说明。
README: 保持 README.md 文件更新，包含项目启动、构建、部署说明以及重要的架构决策。
7.Git 工作流与提交规范 (Git Workflow & Commit Messages):
分支模型: 推荐使用 Git Flow 或 GitHub Flow 等标准化分支模型（例如：main 主分支，develop 开发分支，feature/xxx 特性分支，fix/xxx 修复分支）。
提交信息: 遵循 Conventional Commits 规范 (例如：feat: add new login button, fix: resolve issue with data fetching)。这有助于自动化生成Changelog。
8.测试 (Testing):
（初期可选，但推荐）对核心工具函数和重要业务逻辑组件编写单元测试（例如使用 Jest / Vitest 和 React Testing Library）。
目标是逐步提高测试覆盖率，确保代码质量和重构安全性。
9.依赖管理 (Dependency Management):
谨慎添加新的第三方依赖。添加前需进行评估和团队讨论，考虑其维护性、大小和安全性。
定期审查并更新依赖版本，关注安全漏洞。
10.国际化与可访问性 (i18n & a11y - 视项目需求):
（如果未来有需求）预留国际化 (i18n) 的接口和结构。
开发时关注基本的可访问性 (a11y) 原则，如使用语义化HTML标签，确保键盘导航等。
这些规则旨在提高代码质量、团队协作效率和项目的可维护性。在项目初期明确这些规则，并借助工具强制执行，能为后续开发打下良好基础。
