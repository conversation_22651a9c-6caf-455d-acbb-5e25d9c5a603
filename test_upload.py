#!/usr/bin/env python3
"""
测试上传功能的脚本
用于验证优化后的分片上传是否能正常处理大文件
"""

import csv
import os
import random
import string
from datetime import datetime

def generate_test_csv(filename, rows=100000, cols=10):
    """生成测试用的大CSV文件"""
    print(f"生成测试CSV文件: {filename}")
    print(f"行数: {rows:,}, 列数: {cols}")
    
    # 生成列名
    headers = [f'column_{i+1}' for i in range(cols)]
    headers[0] = 'product_name'  # 产品名称列
    headers[1] = 'image_url'     # 图片URL列
    headers[2] = 'price'         # 价格列
    
    start_time = datetime.now()
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        
        # 写入表头
        writer.writerow(headers)
        
        # 生成测试数据
        for i in range(rows):
            row = []
            for j, header in enumerate(headers):
                if header == 'product_name':
                    row.append(f'产品_{i+1:06d}')
                elif header == 'image_url':
                    row.append(f'https://example.com/images/product_{i+1}.jpg')
                elif header == 'price':
                    row.append(f'{random.uniform(10, 1000):.2f}')
                else:
                    # 生成随机字符串
                    length = random.randint(5, 20)
                    row.append(''.join(random.choices(string.ascii_letters + string.digits + ' ', k=length)))
            
            writer.writerow(row)
            
            # 每10000行显示一次进度
            if (i + 1) % 10000 == 0:
                elapsed = datetime.now() - start_time
                print(f"已生成 {i+1:,} 行 ({(i+1)/rows*100:.1f}%) - 耗时: {elapsed}")
    
    file_size = os.path.getsize(filename)
    total_time = datetime.now() - start_time
    
    print(f"\n测试文件生成完成:")
    print(f"文件名: {filename}")
    print(f"文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
    print(f"总行数: {rows:,} (包含表头)")
    print(f"总列数: {cols}")
    print(f"生成耗时: {total_time}")
    
    return filename, file_size

def create_test_files():
    """创建不同大小的测试文件"""
    test_files = []
    
    # 小文件 (约1MB)
    filename, size = generate_test_csv('test_small.csv', rows=1000, cols=10)
    test_files.append(('小文件测试', filename, size))
    
    # 中等文件 (约10MB)
    filename, size = generate_test_csv('test_medium.csv', rows=10000, cols=15)
    test_files.append(('中等文件测试', filename, size))
    
    # 大文件 (约100MB)
    filename, size = generate_test_csv('test_large.csv', rows=100000, cols=20)
    test_files.append(('大文件测试', filename, size))
    
    return test_files

def main():
    print("=" * 60)
    print("ProductView2 文件上传功能测试")
    print("=" * 60)
    
    try:
        test_files = create_test_files()
        
        print("\n" + "=" * 60)
        print("测试文件生成完成:")
        print("=" * 60)
        
        for desc, filename, size in test_files:
            print(f"{desc}:")
            print(f"  文件名: {filename}")
            print(f"  大小: {size:,} 字节 ({size/1024/1024:.2f} MB)")
            print()
        
        print("测试说明:")
        print("1. 使用浏览器访问管理后台的添加数据源页面")
        print("2. 选择'上传本地CSV文件'")
        print("3. 依次上传生成的测试文件")
        print("4. 观察上传过程中的进度显示、速度显示和内存使用情况")
        print("5. 验证大文件上传是否不再卡死")
        
        print("\n预期结果:")
        print("- 上传过程显示详细进度信息（分片数、速度、剩余时间）")
        print("- 浏览器不会卡死或无响应")
        print("- 大文件能够成功上传并正确解析CSV列名")
        print("- 上传失败时能正确重试")
        print("- 可以正常暂停和恢复上传")
        
    except Exception as e:
        print(f"生成测试文件时出错: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main()) 