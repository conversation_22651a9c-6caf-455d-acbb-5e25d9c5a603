# ProductView2 大文件上传优化说明

## 概述

本次优化针对大型CSV文件上传时浏览器卡死的问题，对上传模块进行了全面重写和优化。新的上传系统能够稳定处理大文件上传，提供更好的用户体验和错误处理。

## 主要优化内容

### 1. 前端优化

#### 分片上传引擎重写 (`app/static/js/chunk-upload.js`)
- **内存优化**: 分片大小从1MB减少到512KB，减少内存压力
- **并发控制**: 最大并发数降低到2，避免资源争夺
- **错误处理**: 增强的重试机制，支持指数退避
- **进度监控**: 实时显示上传速度、剩余时间、失败分片数
- **网络优化**: 30秒请求超时，自动取消过期请求
- **状态管理**: 完善的暂停/恢复/取消机制

#### 用户界面优化
- **详细进度**: 显示分片进度、上传速度、剩余时间
- **文件验证**: 文件大小限制(1GB)、文件名长度检查
- **状态反馈**: 清晰的状态提示和错误信息
- **视觉优化**: 更友好的进度条和状态显示

### 2. 后端优化

#### 分片处理优化 (`app/routes.py`)
- **参数验证**: 全面的输入参数验证和安全检查
- **文件大小控制**: 1GB文件大小限制，2MB分片大小限制
- **错误恢复**: 支持断点续传和分片重传
- **内存管理**: 使用64KB缓冲进行文件合并，避免大内存占用
- **CSV验证**: 智能CSV格式验证，大文件采样验证

#### 安全性增强
- **文件ID验证**: 严格的文件ID格式检查
- **分片数量限制**: 防止恶意上传过多分片
- **临时文件管理**: 自动清理24小时以上的临时文件
- **错误日志**: 详细的错误记录和监控

### 3. 性能优化

#### 资源使用优化
- **内存控制**: 流式文件处理，避免一次性加载大文件
- **磁盘I/O**: 缓冲读写，减少磁盘访问次数
- **网络优化**: 减少并发连接，降低服务器压力
- **CPU使用**: 分批处理，避免长时间阻塞

#### 用户体验优化
- **响应速度**: 浏览器界面始终保持响应
- **进度反馈**: 实时更新上传进度和统计信息
- **错误处理**: 友好的错误提示和自动重试
- **操作控制**: 支持暂停、恢复、取消操作

## 使用方法

### 1. 上传大文件

1. 登录管理后台
2. 进入"数据源管理" -> "添加新数据源"
3. 选择"上传本地CSV文件"
4. 点击选择文件，选择CSV文件
5. 点击"开始上传"按钮
6. 监控上传进度，可随时暂停/恢复/取消

### 2. 进度信息解读

上传过程中会显示以下信息：
- **进度百分比**: 整体上传完成度
- **分片进度**: 已上传分片数/总分片数
- **上传速度**: 实时上传速度 (KB/s, MB/s)
- **剩余时间**: 预估完成时间
- **文件大小**: 已上传大小/总文件大小
- **失败分片**: 上传失败的分片数量

### 3. 错误处理

- **网络错误**: 自动重试，最多3次
- **分片失败**: 显示失败分片数，自动重试
- **文件格式错误**: 上传前和合并后都会验证CSV格式
- **空间不足**: 服务器磁盘空间检查

## 技术规格

### 限制参数
- **最大文件大小**: 1GB
- **最大分片大小**: 2MB
- **最大分片数**: 10000
- **分片大小**: 512KB (前端配置)
- **并发上传数**: 2
- **请求超时**: 30秒
- **最大重试次数**: 3次

### 支持的文件格式
- CSV文件 (.csv)
- UTF-8编码 (推荐)
- UTF-8-BOM编码 (自动处理)

## 测试方法

使用提供的测试脚本生成测试文件：

```bash
python test_upload.py
```

该脚本会生成三个不同大小的测试文件：
- `test_small.csv` (约1MB, 1000行)
- `test_medium.csv` (约10MB, 10000行)  
- `test_large.csv` (约100MB, 100000行)

## 故障排除

### 常见问题

1. **上传速度慢**
   - 检查网络连接
   - 考虑在网络较好时上传大文件

2. **上传失败**
   - 查看浏览器控制台错误信息
   - 检查服务器磁盘空间
   - 验证CSV文件格式

3. **浏览器卡死**
   - 刷新页面重新开始
   - 检查浏览器版本是否过旧
   - 关闭其他占用内存的标签页

4. **文件合并失败**
   - 检查服务器临时目录权限
   - 确认所有分片都已上传完成
   - 检查服务器日志

### 监控和日志

- 服务器端错误记录在应用日志中
- 浏览器端错误可在开发者工具控制台查看
- 上传统计信息实时显示在界面上

## 版本历史

### v2.0 (当前版本)
- 重写分片上传引擎
- 优化内存和性能
- 增强错误处理和用户体验
- 添加详细进度显示
- 支持大文件上传(最大1GB)

### v1.0 (原始版本)
- 基础分片上传功能
- 存在大文件卡死问题
- 简单的进度显示

## 开发注意事项

如需修改上传配置，可以调整以下参数：

### 前端配置 (chunk-upload.js)
```javascript
chunkSize: 512 * 1024,  // 分片大小
concurrency: 2,          // 并发数
maxRetries: 3,           // 最大重试
retryDelay: 1000         // 重试延迟
```

### 后端配置 (routes.py)
```python
MAX_CONTENT_LENGTH = 1024 * 1024 * 1024  # 最大文件大小
CHUNK_SIZE_LIMIT = 2 * 1024 * 1024       # 分片大小限制
MAX_CHUNKS = 10000                        # 最大分片数
```

## 总结

本次优化显著改善了大文件上传的用户体验：
- **稳定性**: 不再出现浏览器卡死问题
- **性能**: 内存使用更合理，上传更稳定
- **用户体验**: 详细的进度信息和控制选项
- **可靠性**: 强化的错误处理和重试机制

大文件上传现在可以安全、稳定地处理，用户可以放心上传较大的CSV数据文件。 