<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Y轴功能测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .test-title { font-weight: bold; color: #333; margin-bottom: 10px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 3px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { padding: 8px 16px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Y轴功能测试页面</h1>
    
    <div class="test-section">
        <div class="test-title">1. Y轴元素检测</div>
        <button onclick="testYAxisElements()">检测Y轴元素</button>
        <div id="yAxisElementsResult"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">2. Sticky定位测试</div>
        <button onclick="testStickyPositioning()">测试Sticky定位</button>
        <div id="stickyPositioningResult"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">3. 滚动行为测试</div>
        <button onclick="testScrollBehavior()">测试滚动行为</button>
        <div id="scrollBehaviorResult"></div>
    </div>
    
    <div class="test-section">
        <div class="test-title">4. Y轴初始化测试</div>
        <button onclick="testYAxisInitialization()">测试Y轴初始化</button>
        <div id="yAxisInitResult"></div>
    </div>

    <script>
        // 测试Y轴元素
        function testYAxisElements() {
            const resultDiv = document.getElementById('yAxisElementsResult');
            
            // 在父窗口中查找Y轴元素
            const parentWindow = window.parent || window.opener || window;
            const parentDoc = parentWindow.document;
            
            const yMainHeaders = parentDoc.querySelectorAll('tbody th:first-child');
            const ySubHeaders = parentDoc.querySelectorAll('tbody th:nth-child(2)');
            
            let result = `<div class="test-result info">
                找到 ${yMainHeaders.length} 个主分类标签<br>
                找到 ${ySubHeaders.length} 个子分类标签
            </div>`;
            
            if (yMainHeaders.length > 0 || ySubHeaders.length > 0) {
                result += `<div class="test-result success">✅ Y轴元素检测成功</div>`;
            } else {
                result += `<div class="test-result error">❌ 未找到Y轴元素</div>`;
            }
            
            resultDiv.innerHTML = result;
        }
        
        // 测试Sticky定位
        function testStickyPositioning() {
            const resultDiv = document.getElementById('stickyPositioningResult');
            
            const parentWindow = window.parent || window.opener || window;
            const parentDoc = parentWindow.document;
            
            const yHeaders = parentDoc.querySelectorAll('tbody th:first-child, tbody th:nth-child(2)');
            let stickyCount = 0;
            let issues = [];
            
            yHeaders.forEach((header, index) => {
                const styles = parentWindow.getComputedStyle(header);
                const position = styles.position;
                const left = styles.left;
                const zIndex = styles.zIndex;
                
                if (position === 'sticky') {
                    stickyCount++;
                } else {
                    issues.push(`标签 ${index + 1}: position=${position}`);
                }
                
                if (header.cellIndex === 0 && left !== '0px') {
                    issues.push(`主分类标签 ${index + 1}: left=${left} (应为0px)`);
                }
                
                if (header.cellIndex === 1 && left !== '120px') {
                    issues.push(`子分类标签 ${index + 1}: left=${left} (应为120px)`);
                }
            });
            
            let result = `<div class="test-result info">
                检查了 ${yHeaders.length} 个Y轴标签<br>
                其中 ${stickyCount} 个使用sticky定位
            </div>`;
            
            if (issues.length === 0) {
                result += `<div class="test-result success">✅ Sticky定位测试通过</div>`;
            } else {
                result += `<div class="test-result error">❌ 发现问题:<br>${issues.join('<br>')}</div>`;
            }
            
            resultDiv.innerHTML = result;
        }
        
        // 测试滚动行为
        function testScrollBehavior() {
            const resultDiv = document.getElementById('scrollBehaviorResult');
            
            const parentWindow = window.parent || window.opener || window;
            const parentDoc = parentWindow.document;
            
            const productCanvas = parentDoc.getElementById('productCanvas');
            
            if (!productCanvas) {
                resultDiv.innerHTML = `<div class="test-result error">❌ 未找到产品画布容器</div>`;
                return;
            }
            
            // 模拟滚动测试
            const originalScrollLeft = productCanvas.scrollLeft;
            const originalScrollTop = productCanvas.scrollTop;
            
            // 水平滚动测试
            productCanvas.scrollLeft = 100;
            setTimeout(() => {
                const yHeaders = parentDoc.querySelectorAll('tbody th:first-child, tbody th:nth-child(2)');
                let horizontalStickiness = true;
                
                yHeaders.forEach(header => {
                    const rect = header.getBoundingClientRect();
                    const canvasRect = productCanvas.getBoundingClientRect();
                    
                    // 检查Y轴标签是否保持在左侧固定位置
                    if (header.cellIndex === 0 && Math.abs(rect.left - canvasRect.left) > 5) {
                        horizontalStickiness = false;
                    }
                    if (header.cellIndex === 1 && Math.abs(rect.left - canvasRect.left - 120) > 5) {
                        horizontalStickiness = false;
                    }
                });
                
                // 恢复原始滚动位置
                productCanvas.scrollLeft = originalScrollLeft;
                productCanvas.scrollTop = originalScrollTop;
                
                let result = `<div class="test-result info">滚动测试完成</div>`;
                
                if (horizontalStickiness) {
                    result += `<div class="test-result success">✅ 水平滚动时Y轴标签保持固定</div>`;
                } else {
                    result += `<div class="test-result error">❌ 水平滚动时Y轴标签位置异常</div>`;
                }
                
                resultDiv.innerHTML = result;
            }, 100);
        }
        
        // 测试Y轴初始化
        function testYAxisInitialization() {
            const resultDiv = document.getElementById('yAxisInitResult');
            
            const parentWindow = window.parent || window.opener || window;
            
            if (typeof parentWindow.initializeYAxis === 'function') {
                try {
                    parentWindow.initializeYAxis();
                    resultDiv.innerHTML = `<div class="test-result success">✅ Y轴初始化函数调用成功</div>`;
                } catch (error) {
                    resultDiv.innerHTML = `<div class="test-result error">❌ Y轴初始化失败: ${error.message}</div>`;
                }
            } else {
                resultDiv.innerHTML = `<div class="test-result error">❌ 未找到Y轴初始化函数</div>`;
            }
        }
        
        // 页面加载时自动运行基本测试
        window.onload = function() {
            setTimeout(() => {
                testYAxisElements();
                testStickyPositioning();
            }, 1000);
        };
    </script>
</body>
</html>
