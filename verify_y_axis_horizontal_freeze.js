// Y轴水平冻结验证脚本
// 在浏览器控制台中运行此脚本来验证Y轴水平冻结功能

console.log('🧊 开始验证Y轴水平冻结功能...');

function verifyYAxisHorizontalFreeze() {
    console.log('\n📌 验证Y轴水平冻结定位');
    
    // 查找所有Y轴标签
    const yMainHeaders = document.querySelectorAll('tbody th:first-child');
    const ySubHeaders = document.querySelectorAll('tbody th:nth-child(2)');
    const allYHeaders = [...yMainHeaders, ...ySubHeaders];
    
    console.log(`找到 ${yMainHeaders.length} 个主分类标签`);
    console.log(`找到 ${ySubHeaders.length} 个子分类标签`);
    console.log(`总计 ${allYHeaders.length} 个Y轴标签`);
    
    if (allYHeaders.length === 0) {
        console.log('❌ 未找到Y轴标签');
        return false;
    }
    
    let stickyCount = 0;
    let horizontalFreezeIssues = [];
    
    allYHeaders.forEach((header, index) => {
        const styles = window.getComputedStyle(header);
        const position = styles.position;
        const left = styles.left;
        const top = styles.top;
        const zIndex = styles.zIndex;
        
        console.log(`Y轴标签 ${index + 1}:`, {
            element: header.textContent.trim().substring(0, 10) + '...',
            position: position,
            left: left,
            top: top,
            zIndex: zIndex,
            cellIndex: header.cellIndex
        });
        
        // 检查是否为sticky定位（水平冻结需要）
        if (position === 'sticky') {
            stickyCount++;
        } else {
            horizontalFreezeIssues.push(`标签 ${index + 1}: position=${position} (应为sticky)`);
        }
        
        // 检查left值（水平冻结的关键）
        if (header.cellIndex === 0 && left !== '0px') {
            horizontalFreezeIssues.push(`主分类标签 ${index + 1}: left=${left} (应为0px)`);
        }
        if (header.cellIndex === 1 && left !== '120px') {
            horizontalFreezeIssues.push(`子分类标签 ${index + 1}: left=${left} (应为120px)`);
        }
        
        // 检查top值（应该为auto，不固定垂直位置）
        if (top !== 'auto' && top !== '0px') {
            horizontalFreezeIssues.push(`标签 ${index + 1}: top=${top} (应为auto，保持垂直自然滚动)`);
        }
        
        // 检查z-index
        const expectedZIndex = header.cellIndex === 0 ? '100' : '99';
        if (zIndex !== expectedZIndex) {
            horizontalFreezeIssues.push(`标签 ${index + 1}: z-index=${zIndex} (应为${expectedZIndex})`);
        }
    });
    
    console.log(`\n📊 验证结果:`);
    console.log(`Sticky定位标签: ${stickyCount}/${allYHeaders.length}`);
    
    if (horizontalFreezeIssues.length === 0) {
        console.log('✅ Y轴水平冻结验证通过');
        console.log('✅ 所有Y轴标签都正确配置了水平冻结');
        return true;
    } else {
        console.log('❌ Y轴水平冻结验证失败:');
        horizontalFreezeIssues.forEach(issue => console.log(`  - ${issue}`));
        return false;
    }
}

function testHorizontalScrollBehavior() {
    console.log('\n🔄 测试水平滚动行为');
    
    const productCanvas = document.getElementById('productCanvas');
    if (!productCanvas) {
        console.log('❌ 未找到产品画布容器');
        return false;
    }
    
    // 记录原始滚动位置
    const originalScrollLeft = productCanvas.scrollLeft;
    const originalScrollTop = productCanvas.scrollTop;
    
    console.log(`原始滚动位置: left=${originalScrollLeft}, top=${originalScrollTop}`);
    
    // 测试水平滚动
    console.log('执行水平滚动测试...');
    productCanvas.scrollLeft = 300;
    
    setTimeout(() => {
        const yHeaders = document.querySelectorAll('tbody th:first-child, tbody th:nth-child(2)');
        let horizontalFreezeWorking = true;
        let scrollIssues = [];
        
        yHeaders.forEach((header, index) => {
            const rect = header.getBoundingClientRect();
            const canvasRect = productCanvas.getBoundingClientRect();
            
            // 检查Y轴标签是否保持在预期的水平位置
            const expectedLeft = header.cellIndex === 0 ? canvasRect.left : canvasRect.left + 120;
            const actualLeft = rect.left;
            const tolerance = 5; // 5px容差
            
            if (Math.abs(actualLeft - expectedLeft) > tolerance) {
                horizontalFreezeWorking = false;
                scrollIssues.push(`标签${index + 1}: 期望left=${expectedLeft.toFixed(1)}, 实际left=${actualLeft.toFixed(1)}`);
            }
        });
        
        // 恢复原始滚动位置
        productCanvas.scrollLeft = originalScrollLeft;
        productCanvas.scrollTop = originalScrollTop;
        
        if (horizontalFreezeWorking) {
            console.log('✅ 水平滚动测试通过');
            console.log('✅ Y轴标签在水平滚动时保持固定位置');
        } else {
            console.log('❌ 水平滚动测试失败:');
            scrollIssues.forEach(issue => console.log(`  - ${issue}`));
        }
        
        return horizontalFreezeWorking;
    }, 200);
}

function testVerticalScrollBehavior() {
    console.log('\n📜 测试垂直滚动行为');
    
    const productCanvas = document.getElementById('productCanvas');
    if (!productCanvas) {
        console.log('❌ 未找到产品画布容器');
        return false;
    }
    
    // 记录原始滚动位置
    const originalScrollLeft = productCanvas.scrollLeft;
    const originalScrollTop = productCanvas.scrollTop;
    
    console.log(`原始滚动位置: left=${originalScrollLeft}, top=${originalScrollTop}`);
    
    // 测试垂直滚动
    console.log('执行垂直滚动测试...');
    productCanvas.scrollTop = 200;
    
    setTimeout(() => {
        const yHeaders = document.querySelectorAll('tbody th:first-child, tbody th:nth-child(2)');
        let verticalScrollWorking = true;
        let scrollIssues = [];
        
        yHeaders.forEach((header, index) => {
            const rect = header.getBoundingClientRect();
            const canvasRect = productCanvas.getBoundingClientRect();
            
            // 检查Y轴标签是否跟随垂直滚动
            // 在垂直滚动时，Y轴标签应该移动（不固定垂直位置）
            const headerTop = rect.top;
            const canvasTop = canvasRect.top;
            
            // 如果标签始终固定在容器顶部，说明垂直滚动有问题
            if (Math.abs(headerTop - canvasTop) < 5) {
                verticalScrollWorking = false;
                scrollIssues.push(`标签${index + 1}: 垂直位置固定，未跟随滚动`);
            }
        });
        
        // 恢复原始滚动位置
        productCanvas.scrollLeft = originalScrollLeft;
        productCanvas.scrollTop = originalScrollTop;
        
        if (verticalScrollWorking) {
            console.log('✅ 垂直滚动测试通过');
            console.log('✅ Y轴标签在垂直滚动时正常跟随');
        } else {
            console.log('❌ 垂直滚动测试失败:');
            scrollIssues.forEach(issue => console.log(`  - ${issue}`));
        }
        
        return verticalScrollWorking;
    }, 200);
}

function verifyYAxisFunctions() {
    console.log('\n🔧 验证Y轴水平冻结函数');
    
    const functions = [
        'initializeYAxis',
        'setupYAxisScrollListener',
        'ensureYAxisHorizontalFreeze',
        'validateYAxisHorizontalFreeze'
    ];
    
    let availableFunctions = 0;
    
    functions.forEach(funcName => {
        if (typeof window[funcName] === 'function') {
            console.log(`✅ ${funcName}: 可用`);
            availableFunctions++;
        } else {
            console.log(`❌ ${funcName}: 不可用`);
        }
    });
    
    console.log(`可用函数: ${availableFunctions}/${functions.length}`);
    
    // 测试主初始化函数
    if (typeof window.initializeYAxis === 'function') {
        try {
            console.log('测试调用 initializeYAxis()...');
            window.initializeYAxis();
            console.log('✅ initializeYAxis() 调用成功');
        } catch (error) {
            console.log(`❌ initializeYAxis() 调用失败: ${error.message}`);
        }
    }
    
    return availableFunctions === functions.length;
}

// 运行所有验证
function runAllVerifications() {
    console.log('🧪 开始运行Y轴水平冻结验证...');
    console.log('=' * 50);
    
    const verifications = [
        { name: 'Y轴水平冻结定位', func: verifyYAxisHorizontalFreeze },
        { name: 'Y轴水平冻结函数', func: verifyYAxisFunctions }
    ];
    
    let passedVerifications = 0;
    
    verifications.forEach(verification => {
        try {
            const result = verification.func();
            if (result !== false) {
                passedVerifications++;
            }
        } catch (error) {
            console.log(`❌ ${verification.name} 验证出错: ${error.message}`);
        }
    });
    
    // 异步测试
    setTimeout(() => {
        testHorizontalScrollBehavior();
    }, 500);
    
    setTimeout(() => {
        testVerticalScrollBehavior();
    }, 1000);
    
    console.log('\n' + '=' * 50);
    console.log('📊 验证结果总结');
    console.log('=' * 50);
    console.log(`通过验证: ${passedVerifications}/${verifications.length}`);
    console.log(`成功率: ${(passedVerifications/verifications.length*100).toFixed(1)}%`);
    
    if (passedVerifications === verifications.length) {
        console.log('\n🎉 Y轴水平冻结验证通过！');
        console.log('✅ Y轴水平冻结功能已正确实现');
        console.log('✅ 水平滚动时Y轴标签保持固定');
        console.log('✅ 垂直滚动时Y轴标签自然跟随');
    } else {
        console.log(`\n⚠️ 还有 ${verifications.length - passedVerifications} 项验证未通过`);
    }
    
    console.log('=' * 50);
}

// 自动运行验证
runAllVerifications();
