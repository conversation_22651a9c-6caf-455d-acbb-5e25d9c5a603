{"habit_0db14949-906a-4fbd-bfbf-79439d543a77": {"id": "habit_0db14949-906a-4fbd-bfbf-79439d543a77", "user_id": "admin1", "name": "测试1", "description": "测试用的", "axisConfig": {"xAxis": "", "yAxis": ""}, "sortConfig": {"column": "", "direction": "asc"}, "filterConfig": {"minPrice": "123123123123", "maxPrice": "123123123"}, "displayConfig": {"cardLayout": "compact"}, "created_at": "2025-05-12T06:47:53.072066+00:00", "updated_at": "2025-05-12T06:48:28.419947+00:00", "use_count": 34, "last_used_at": "2025-05-14T03:47:45.925180+00:00"}, "habit_e912d9c9-befe-4891-b6eb-4c3472a372f0": {"id": "habit_e912d9c9-befe-4891-b6eb-4c3472a372f0", "user_id": "admin1", "name": "测试2", "description": "测试用的2", "axisConfig": {"xAxis": "品牌", "yAxis": "价格带"}, "sortConfig": {"column": "年_老客人数", "direction": "desc"}, "filterConfig": {"minPrice": "1222", "maxPrice": ""}, "displayConfig": {"cardLayout": "compact"}, "created_at": "2025-05-12T07:01:27.454312+00:00", "updated_at": "2025-05-12T07:01:27.454330+00:00", "use_count": 11, "last_used_at": "2025-05-13T06:33:44.629718+00:00"}, "habit_a484f2c9-9e59-4e72-8678-a0fa6f40759b": {"id": "habit_a484f2c9-9e59-4e72-8678-a0fa6f40759b", "user_id": "admin1", "name": "1测试用的1", "description": "1测试用的1", "axisConfig": {"xAxis": "品牌", "yAxis": "价格带"}, "sortConfig": {"column": "销量", "direction": "asc"}, "filterConfig": {"minPrice": "100", "maxPrice": "9999"}, "displayConfig": {"cardLayout": "compact"}, "created_at": "2025-05-12T07:37:31.224602+00:00", "updated_at": "2025-05-12T07:37:56.088702+00:00", "use_count": 59, "last_used_at": "2025-05-14T06:34:02.087362+00:00"}}