/* ProductView2 UI增强样式 */

/* 全局字体优化 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 改进的焦点状态 */
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 改进的按钮样式 */
.btn, button {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    letter-spacing: 0.025em;
}

.btn:hover, button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 改进的输入框样式 */
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
    border: 1px solid #d1d5db;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background-color: #ffffff;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="password"]:focus,
textarea:focus,
select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 改进的卡片阴影层次 */
.card-shadow-sm {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.card-shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.card-shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 改进的文字层次 */
.text-heading {
    color: #111827;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.text-subheading {
    color: #374151;
    font-weight: 500;
}

.text-body {
    color: #4b5563;
    line-height: 1.6;
}

.text-caption {
    color: #6b7280;
    font-size: 0.875rem;
}

/* 改进的状态颜色 */
.status-success {
    color: #059669;
    background-color: #d1fae5;
    border-color: #a7f3d0;
}

.status-warning {
    color: #d97706;
    background-color: #fef3c7;
    border-color: #fde68a;
}

.status-error {
    color: #dc2626;
    background-color: #fee2e2;
    border-color: #fecaca;
}

.status-info {
    color: #2563eb;
    background-color: #dbeafe;
    border-color: #bfdbfe;
}

/* 改进的间距工具类 */
.spacing-xs { margin: 4px; }
.spacing-sm { margin: 8px; }
.spacing-md { margin: 16px; }
.spacing-lg { margin: 24px; }
.spacing-xl { margin: 32px; }

.padding-xs { padding: 4px; }
.padding-sm { padding: 8px; }
.padding-md { padding: 16px; }
.padding-lg { padding: 24px; }
.padding-xl { padding: 32px; }

/* 改进的网格工具类 */
.grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 16px;
}

/* 改进的Flexbox工具类 */
.flex-center {
    display: flex;
    align-items: center;
    justify-content: center;
}

.flex-between {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.flex-start {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

/* 改进的动画 */
.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
    animation: slideUp 0.3s ease-out;
}

.scale-in {
    animation: scaleIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 改进的响应式断点 */
@media (max-width: 640px) {
    .responsive-hide-sm { display: none; }
    .responsive-text-sm { font-size: 0.875rem; }
}

@media (max-width: 768px) {
    .responsive-hide-md { display: none; }
    .responsive-text-md { font-size: 0.875rem; }
}

@media (max-width: 1024px) {
    .responsive-hide-lg { display: none; }
    .responsive-text-lg { font-size: 0.875rem; }
}

/* 改进的可访问性 */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 改进的加载状态 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f4f6;
    border-radius: 50%;
    border-top-color: #3b82f6;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 改进的工具提示 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #1f2937;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.75rem;
    white-space: nowrap;
    z-index: 1000;
    opacity: 1;
    animation: fadeIn 0.2s ease-in-out;
}

/* 改进的滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}
