/**
 * 简化的文件上传工具
 * 替代复杂的分片上传，提供简单高效的上传功能
 */
class SimpleUploader {
    constructor(options = {}) {
        this.maxFileSize = options.maxFileSize || 100 * 1024 * 1024; // 100MB
        this.allowedTypes = options.allowedTypes || ['text/csv', 'application/vnd.ms-excel'];
        this.uploadUrl = options.uploadUrl || '/admin/upload_csv';
        
        // 回调函数
        this.onProgress = options.onProgress || function() {};
        this.onSuccess = options.onSuccess || function() {};
        this.onError = options.onError || function() {};
        this.onStart = options.onStart || function() {};
        this.onComplete = options.onComplete || function() {};
        
        this.isUploading = false;
        this.currentRequest = null;
    }
    
    /**
     * 验证文件
     */
    validateFile(file) {
        if (!file) {
            throw new Error('请选择文件');
        }
        
        if (file.size > this.maxFileSize) {
            throw new Error(`文件大小超过限制 (${this.formatFileSize(this.maxFileSize)})`);
        }
        
        // 检查文件扩展名
        const fileName = file.name.toLowerCase();
        if (!fileName.endsWith('.csv')) {
            throw new Error('只允许上传CSV文件');
        }
        
        return true;
    }
    
    /**
     * 上传文件
     */
    async upload(file) {
        try {
            // 检查是否已有上传进行中
            if (this.isUploading) {
                throw new Error('已有文件正在上传中，请等待完成');
            }
            
            // 验证文件
            this.validateFile(file);
            
            // 开始上传
            this.isUploading = true;
            this.onStart(file);
            
            // 创建FormData
            const formData = new FormData();
            formData.append('csv_file', file);
            
            // 创建XMLHttpRequest以支持进度监控
            const xhr = new XMLHttpRequest();
            this.currentRequest = xhr;
            
            // 设置进度监听
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    const progress = (event.loaded / event.total) * 100;
                    this.onProgress({
                        loaded: event.loaded,
                        total: event.total,
                        progress: Math.round(progress),
                        speed: this.calculateSpeed(event.loaded)
                    });
                }
            });
            
            // 设置完成监听
            xhr.addEventListener('load', () => {
                this.isUploading = false;
                this.currentRequest = null;
                
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            this.onSuccess(response);
                        } else {
                            this.onError(response.error || '上传失败');
                        }
                    } catch (e) {
                        this.onError('服务器响应格式错误');
                    }
                } else {
                    this.onError(`上传失败 (HTTP ${xhr.status})`);
                }
                
                this.onComplete();
            });
            
            // 设置错误监听
            xhr.addEventListener('error', () => {
                this.isUploading = false;
                this.currentRequest = null;
                this.onError('网络错误，上传失败');
                this.onComplete();
            });
            
            // 设置超时监听
            xhr.addEventListener('timeout', () => {
                this.isUploading = false;
                this.currentRequest = null;
                this.onError('上传超时，请重试');
                this.onComplete();
            });
            
            // 配置请求
            xhr.open('POST', this.uploadUrl);
            xhr.timeout = 300000; // 5分钟超时
            
            // 发送请求
            xhr.send(formData);
            
        } catch (error) {
            this.isUploading = false;
            this.currentRequest = null;
            this.onError(error.message);
            this.onComplete();
        }
    }
    
    /**
     * 取消上传
     */
    cancel() {
        if (this.currentRequest) {
            this.currentRequest.abort();
            this.currentRequest = null;
        }
        this.isUploading = false;
        this.onComplete();
    }
    
    /**
     * 计算上传速度
     */
    calculateSpeed(loaded) {
        const now = Date.now();
        if (!this.startTime) {
            this.startTime = now;
            this.lastLoaded = 0;
            return 0;
        }
        
        const timeDiff = (now - this.startTime) / 1000; // 秒
        const loadedDiff = loaded - this.lastLoaded;
        
        if (timeDiff > 0) {
            return Math.round(loadedDiff / timeDiff); // 字节/秒
        }
        
        return 0;
    }
    
    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 格式化速度
     */
    formatSpeed(bytesPerSecond) {
        return this.formatFileSize(bytesPerSecond) + '/s';
    }
}

/**
 * 简化的上传UI管理器
 */
class SimpleUploadUI {
    constructor(uploaderId, options = {}) {
        this.uploader = new SimpleUploader(options);
        this.uploaderId = uploaderId;
        this.fileInput = null;
        this.progressContainer = null;
        this.progressBar = null;
        this.statusText = null;
        this.cancelButton = null;
        
        this.initUI();
        this.bindEvents();
    }
    
    initUI() {
        const container = document.getElementById(this.uploaderId);
        if (!container) {
            console.error(`Upload container ${this.uploaderId} not found`);
            return;
        }
        
        // 创建文件输入
        this.fileInput = container.querySelector('input[type="file"]');
        if (!this.fileInput) {
            this.fileInput = document.createElement('input');
            this.fileInput.type = 'file';
            this.fileInput.accept = '.csv';
            container.appendChild(this.fileInput);
        }
        
        // 创建进度显示区域
        this.progressContainer = container.querySelector('.upload-progress');
        if (!this.progressContainer) {
            this.progressContainer = document.createElement('div');
            this.progressContainer.className = 'upload-progress';
            this.progressContainer.style.display = 'none';
            container.appendChild(this.progressContainer);
        }
        
        // 创建进度条
        this.progressBar = this.progressContainer.querySelector('.progress-bar');
        if (!this.progressBar) {
            this.progressBar = document.createElement('div');
            this.progressBar.className = 'progress-bar';
            this.progressBar.style.cssText = `
                width: 100%;
                height: 20px;
                background-color: #f0f0f0;
                border-radius: 10px;
                overflow: hidden;
                margin: 10px 0;
            `;
            
            const progressFill = document.createElement('div');
            progressFill.className = 'progress-fill';
            progressFill.style.cssText = `
                height: 100%;
                background-color: #4CAF50;
                width: 0%;
                transition: width 0.3s ease;
            `;
            
            this.progressBar.appendChild(progressFill);
            this.progressContainer.appendChild(this.progressBar);
        }
        
        // 创建状态文本
        this.statusText = this.progressContainer.querySelector('.status-text');
        if (!this.statusText) {
            this.statusText = document.createElement('div');
            this.statusText.className = 'status-text';
            this.progressContainer.appendChild(this.statusText);
        }
        
        // 创建取消按钮
        this.cancelButton = this.progressContainer.querySelector('.cancel-button');
        if (!this.cancelButton) {
            this.cancelButton = document.createElement('button');
            this.cancelButton.className = 'cancel-button';
            this.cancelButton.textContent = '取消上传';
            this.cancelButton.style.cssText = `
                margin-top: 10px;
                padding: 5px 15px;
                background-color: #f44336;
                color: white;
                border: none;
                border-radius: 3px;
                cursor: pointer;
            `;
            this.progressContainer.appendChild(this.cancelButton);
        }
    }
    
    bindEvents() {
        // 文件选择事件
        if (this.fileInput) {
            this.fileInput.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file) {
                    this.startUpload(file);
                }
            });
        }
        
        // 取消按钮事件
        if (this.cancelButton) {
            this.cancelButton.addEventListener('click', () => {
                this.uploader.cancel();
            });
        }
        
        // 绑定上传器事件
        this.uploader.onStart = (file) => {
            this.showProgress();
            this.updateStatus(`开始上传: ${file.name}`);
        };
        
        this.uploader.onProgress = (data) => {
            this.updateProgress(data.progress);
            this.updateStatus(`上传中: ${data.progress}% (${this.uploader.formatSpeed(data.speed)})`);
        };
        
        this.uploader.onSuccess = (response) => {
            this.updateProgress(100);
            this.updateStatus('上传成功！');
            setTimeout(() => {
                this.hideProgress();
                // 可以在这里触发页面刷新或其他操作
                if (typeof window.onUploadSuccess === 'function') {
                    window.onUploadSuccess(response);
                }
            }, 2000);
        };
        
        this.uploader.onError = (error) => {
            this.updateStatus(`上传失败: ${error}`, 'error');
        };
        
        this.uploader.onComplete = () => {
            if (this.cancelButton) {
                this.cancelButton.style.display = 'none';
            }
        };
    }
    
    startUpload(file) {
        this.uploader.upload(file);
    }
    
    showProgress() {
        if (this.progressContainer) {
            this.progressContainer.style.display = 'block';
        }
        if (this.cancelButton) {
            this.cancelButton.style.display = 'inline-block';
        }
    }
    
    hideProgress() {
        if (this.progressContainer) {
            this.progressContainer.style.display = 'none';
        }
        // 重置文件输入
        if (this.fileInput) {
            this.fileInput.value = '';
        }
    }
    
    updateProgress(percent) {
        const progressFill = this.progressBar?.querySelector('.progress-fill');
        if (progressFill) {
            progressFill.style.width = `${percent}%`;
        }
    }
    
    updateStatus(text, type = 'info') {
        if (this.statusText) {
            this.statusText.textContent = text;
            this.statusText.className = `status-text ${type}`;
        }
    }
}
