"""
统一配置管理
集中管理所有配置项，支持环境变量覆盖
"""
import os
from typing import Dict, Any


class Config:
    """基础配置类"""
    
    # Flask配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'your_very_secret_and_hard_to_guess_key')
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # 文件上传配置
    MAX_CONTENT_LENGTH = int(os.environ.get('MAX_CONTENT_LENGTH', 500 * 1024 * 1024))  # 500MB
    UPLOAD_EXTENSIONS = ['.csv']
    
    # 路径配置
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    UPLOAD_FOLDER_CSV = os.path.join(BASE_DIR, 'uploads', 'csv')
    UPLOAD_FOLDER_TEMP = os.path.join(BASE_DIR, 'uploads', 'temp')
    USER_DATA_FILE = os.path.join(BASE_DIR, 'users.json')
    DATASOURCE_DATA_FILE = os.path.join(BASE_DIR, 'datasources.json')
    
    # 缓存配置
    CACHE_TTL = int(os.environ.get('CACHE_TTL', 300))  # 5分钟
    ENABLE_CACHE = os.environ.get('ENABLE_CACHE', 'True').lower() == 'true'
    
    # 分片上传配置
    CHUNK_SIZE = int(os.environ.get('CHUNK_SIZE', 1024 * 1024))  # 1MB
    MAX_CHUNKS = int(os.environ.get('MAX_CHUNKS', 10000))
    CHUNK_SIZE_LIMIT = int(os.environ.get('CHUNK_SIZE_LIMIT', 2 * 1024 * 1024))  # 2MB
    
    # 性能配置
    MAX_PRODUCTS_PER_PAGE = int(os.environ.get('MAX_PRODUCTS_PER_PAGE', 1000))
    ENABLE_PAGINATION = os.environ.get('ENABLE_PAGINATION', 'False').lower() == 'true'
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.path.join(BASE_DIR, 'app.log')
    
    @classmethod
    def init_app(cls, app):
        """初始化Flask应用配置"""
        # 确保上传目录存在
        os.makedirs(cls.UPLOAD_FOLDER_CSV, exist_ok=True)
        os.makedirs(cls.UPLOAD_FOLDER_TEMP, exist_ok=True)
        
        # 设置Flask配置
        app.config.update({
            'SECRET_KEY': cls.SECRET_KEY,
            'MAX_CONTENT_LENGTH': cls.MAX_CONTENT_LENGTH,
            'UPLOAD_FOLDER_CSV': cls.UPLOAD_FOLDER_CSV,
            'UPLOAD_FOLDER_TEMP': cls.UPLOAD_FOLDER_TEMP,
            'CHUNK_SIZE': cls.CHUNK_SIZE,
            'SEND_FILE_MAX_AGE_DEFAULT': 0,
            'TEMPLATES_AUTO_RELOAD': True,
        })
        
        # 模板自动重载
        app.jinja_env.auto_reload = True


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    CACHE_TTL = 60  # 开发环境缓存时间短一些


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    CACHE_TTL = 600  # 生产环境缓存时间长一些
    MAX_CONTENT_LENGTH = 2 * 1024 * 1024 * 1024  # 2GB


class TestingConfig(Config):
    """测试环境配置"""
    TESTING = True
    CACHE_TTL = 0  # 测试环境不使用缓存


# 配置映射
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}


def get_config(config_name: str = None) -> Config:
    """获取配置对象"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')
    
    return config.get(config_name, DevelopmentConfig)


# 常用配置常量
class Constants:
    """常用常量定义"""
    
    # 文件类型
    ALLOWED_EXTENSIONS_CSV = {'csv'}
    
    # 用户角色
    ROLE_ADMIN = 'admin'
    ROLE_USER = 'user'
    
    # 数据源类型
    DATASOURCE_TYPE_CSV = 'csv_file'
    DATASOURCE_TYPE_FEISHU = 'feishu_lark_sheets_url'
    
    # 缓存键前缀
    CACHE_PREFIX_CSV = 'csv_data_'
    CACHE_PREFIX_GROUPED = 'grouped_'
    CACHE_PREFIX_FILTERED = 'filtered_'
    
    # 默认值
    DEFAULT_EMPTY_VALUE = '(空值)'
    DEFAULT_PAGE_SIZE = 50
    
    # 性能限制
    MAX_PRODUCTS_FOR_GROUPING = 10000
    MAX_FILTER_VALUES = 1000
