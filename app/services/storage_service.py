"""
存储服务抽象层
支持JSON文件和SQLite数据库，便于后续升级
"""
import json
import sqlite3
import os
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from datetime import datetime


class StorageInterface(ABC):
    """存储接口抽象类"""
    
    @abstractmethod
    def load_users(self) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def save_users(self, users: Dict[str, Any]) -> None:
        pass
    
    @abstractmethod
    def load_datasources(self) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def save_datasources(self, datasources: Dict[str, Any]) -> None:
        pass
    
    @abstractmethod
    def load_user_habits(self, user_id: str) -> List[Dict[str, Any]]:
        pass
    
    @abstractmethod
    def save_user_habits(self, user_id: str, habits: List[Dict[str, Any]]) -> None:
        pass


class JSONStorage(StorageInterface):
    """JSON文件存储实现"""
    
    def __init__(self, base_path: str):
        self.base_path = base_path
        self.users_file = os.path.join(base_path, 'users.json')
        self.datasources_file = os.path.join(base_path, 'datasources.json')
        self.habits_file = os.path.join(base_path, 'habits.json')
    
    def load_users(self) -> Dict[str, Any]:
        """加载用户数据"""
        if not os.path.exists(self.users_file):
            # 创建默认管理员用户
            from werkzeug.security import generate_password_hash
            default_users = {
                'admin1': {
                    'password_hash': generate_password_hash('adminpass'),
                    'role': 'admin',
                    'name': '默认管理员'
                }
            }
            self.save_users(default_users)
            return default_users
        
        try:
            with open(self.users_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}
    
    def save_users(self, users: Dict[str, Any]) -> None:
        """保存用户数据"""
        with open(self.users_file, 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=4)
    
    def load_datasources(self) -> Dict[str, Any]:
        """加载数据源配置"""
        if not os.path.exists(self.datasources_file):
            self.save_datasources({})
            return {}
        
        try:
            with open(self.datasources_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            return {}
    
    def save_datasources(self, datasources: Dict[str, Any]) -> None:
        """保存数据源配置"""
        with open(self.datasources_file, 'w', encoding='utf-8') as f:
            json.dump(datasources, f, ensure_ascii=False, indent=4)
    
    def load_user_habits(self, user_id: str) -> List[Dict[str, Any]]:
        """加载用户习惯"""
        if not os.path.exists(self.habits_file):
            return []
        
        try:
            with open(self.habits_file, 'r', encoding='utf-8') as f:
                all_habits = json.load(f)
                return all_habits.get(user_id, [])
        except (json.JSONDecodeError, FileNotFoundError):
            return []
    
    def save_user_habits(self, user_id: str, habits: List[Dict[str, Any]]) -> None:
        """保存用户习惯"""
        all_habits = {}
        if os.path.exists(self.habits_file):
            try:
                with open(self.habits_file, 'r', encoding='utf-8') as f:
                    all_habits = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                pass
        
        all_habits[user_id] = habits
        
        with open(self.habits_file, 'w', encoding='utf-8') as f:
            json.dump(all_habits, f, ensure_ascii=False, indent=4)


class SQLiteStorage(StorageInterface):
    """SQLite数据库存储实现（可选升级）"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self._init_database()
    
    def _init_database(self):
        """初始化数据库表"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    username TEXT PRIMARY KEY,
                    password_hash TEXT NOT NULL,
                    role TEXT NOT NULL,
                    name TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS datasources (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL,
                    config TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.execute('''
                CREATE TABLE IF NOT EXISTS user_habits (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    config TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
    
    def load_users(self) -> Dict[str, Any]:
        """加载用户数据"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('SELECT * FROM users')
            users = {}
            
            for row in cursor:
                users[row['username']] = {
                    'password_hash': row['password_hash'],
                    'role': row['role'],
                    'name': row['name']
                }
            
            return users
    
    def save_users(self, users: Dict[str, Any]) -> None:
        """保存用户数据"""
        with sqlite3.connect(self.db_path) as conn:
            # 清空现有数据
            conn.execute('DELETE FROM users')
            
            # 插入新数据
            for username, user_data in users.items():
                conn.execute('''
                    INSERT INTO users (username, password_hash, role, name)
                    VALUES (?, ?, ?, ?)
                ''', (username, user_data['password_hash'], 
                     user_data['role'], user_data['name']))
            
            conn.commit()
    
    def load_datasources(self) -> Dict[str, Any]:
        """加载数据源配置"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('SELECT * FROM datasources')
            datasources = {}
            
            for row in cursor:
                datasources[row['id']] = json.loads(row['config'])
                datasources[row['id']]['name'] = row['name']
                datasources[row['id']]['type'] = row['type']
            
            return datasources
    
    def save_datasources(self, datasources: Dict[str, Any]) -> None:
        """保存数据源配置"""
        with sqlite3.connect(self.db_path) as conn:
            # 清空现有数据
            conn.execute('DELETE FROM datasources')
            
            # 插入新数据
            for ds_id, ds_data in datasources.items():
                config = dict(ds_data)
                name = config.pop('name', '')
                ds_type = config.pop('type', '')
                
                conn.execute('''
                    INSERT OR REPLACE INTO datasources (id, name, type, config, updated_at)
                    VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
                ''', (ds_id, name, ds_type, json.dumps(config)))
            
            conn.commit()
    
    def load_user_habits(self, user_id: str) -> List[Dict[str, Any]]:
        """加载用户习惯"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(
                'SELECT * FROM user_habits WHERE user_id = ? ORDER BY created_at DESC',
                (user_id,)
            )
            
            habits = []
            for row in cursor:
                habit = json.loads(row['config'])
                habit['id'] = row['id']
                habit['name'] = row['name']
                habits.append(habit)
            
            return habits
    
    def save_user_habits(self, user_id: str, habits: List[Dict[str, Any]]) -> None:
        """保存用户习惯"""
        with sqlite3.connect(self.db_path) as conn:
            # 删除用户现有习惯
            conn.execute('DELETE FROM user_habits WHERE user_id = ?', (user_id,))
            
            # 插入新习惯
            for habit in habits:
                config = dict(habit)
                name = config.pop('name', '')
                config.pop('id', None)  # 移除ID，让数据库自动生成
                
                conn.execute('''
                    INSERT INTO user_habits (user_id, name, config)
                    VALUES (?, ?, ?)
                ''', (user_id, name, json.dumps(config)))
            
            conn.commit()


# 存储服务工厂
def create_storage_service(storage_type: str = 'json', **kwargs) -> StorageInterface:
    """创建存储服务实例"""
    if storage_type == 'json':
        base_path = kwargs.get('base_path', '.')
        return JSONStorage(base_path)
    elif storage_type == 'sqlite':
        db_path = kwargs.get('db_path', 'productview2.db')
        return SQLiteStorage(db_path)
    else:
        raise ValueError(f"不支持的存储类型: {storage_type}")
