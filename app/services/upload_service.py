"""
简化的文件上传服务
移除复杂的分片上传，提供简单高效的文件上传功能
"""
import os
import uuid
import csv
from typing import Tuple, List, Optional
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage


class UploadService:
    """文件上传服务"""
    
    def __init__(self, upload_folder: str, max_file_size: int = 100 * 1024 * 1024):
        self.upload_folder = upload_folder
        self.max_file_size = max_file_size
        self.allowed_extensions = {'csv'}
        
        # 确保上传目录存在
        os.makedirs(upload_folder, exist_ok=True)
    
    def is_allowed_file(self, filename: str) -> bool:
        """检查文件扩展名是否允许"""
        return ('.' in filename and 
                filename.rsplit('.', 1)[1].lower() in self.allowed_extensions)
    
    def validate_file(self, file: FileStorage) -> Tuple[bool, Optional[str]]:
        """验证上传文件"""
        if not file or not file.filename:
            return False, "没有选择文件"
        
        if not self.is_allowed_file(file.filename):
            return False, "只允许上传CSV文件"
        
        # 检查文件大小（如果可能）
        if hasattr(file, 'content_length') and file.content_length:
            if file.content_length > self.max_file_size:
                return False, f"文件大小超过限制 ({self.max_file_size // (1024*1024)}MB)"
        
        return True, None
    
    def save_file(self, file: FileStorage) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        保存上传的文件
        返回: (成功标志, 错误信息, 文件路径)
        """
        # 验证文件
        is_valid, error_msg = self.validate_file(file)
        if not is_valid:
            return False, error_msg, None
        
        try:
            # 生成安全的文件名
            filename = secure_filename(file.filename)
            unique_filename = f"{uuid.uuid4().hex}_{filename}"
            file_path = os.path.join(self.upload_folder, unique_filename)
            
            # 保存文件
            file.save(file_path)
            
            # 验证CSV格式
            is_valid_csv, csv_error = self.validate_csv_file(file_path)
            if not is_valid_csv:
                # 删除无效文件
                try:
                    os.remove(file_path)
                except:
                    pass
                return False, csv_error, None
            
            return True, None, file_path
            
        except Exception as e:
            return False, f"保存文件时出错: {str(e)}", None
    
    def validate_csv_file(self, file_path: str) -> Tuple[bool, Optional[str]]:
        """验证CSV文件格式"""
        try:
            with open(file_path, 'r', encoding='utf-8-sig') as csvfile:
                # 尝试读取前几行来验证格式
                reader = csv.reader(csvfile)
                
                # 检查是否有表头
                headers = next(reader, None)
                if not headers:
                    return False, "CSV文件为空或格式不正确"
                
                # 检查表头是否有效
                if len(headers) < 2:
                    return False, "CSV文件至少需要2列数据"
                
                # 尝试读取几行数据验证格式
                row_count = 0
                for row in reader:
                    row_count += 1
                    if row_count >= 5:  # 只检查前5行
                        break
                    
                    if len(row) != len(headers):
                        return False, f"第{row_count + 1}行数据列数与表头不匹配"
                
                return True, None
                
        except UnicodeDecodeError:
            return False, "文件编码格式不正确，请使用UTF-8编码"
        except Exception as e:
            return False, f"验证CSV文件时出错: {str(e)}"
    
    def get_csv_headers(self, file_path: str) -> Tuple[bool, Optional[str], List[str]]:
        """
        获取CSV文件的表头
        返回: (成功标志, 错误信息, 表头列表)
        """
        try:
            with open(file_path, 'r', encoding='utf-8-sig') as csvfile:
                reader = csv.reader(csvfile)
                headers = next(reader, [])
                return True, None, headers
        except Exception as e:
            return False, f"读取CSV表头时出错: {str(e)}", []
    
    def get_csv_sample_data(self, file_path: str, max_rows: int = 5) -> Tuple[bool, Optional[str], List[dict]]:
        """
        获取CSV文件的示例数据
        返回: (成功标志, 错误信息, 示例数据)
        """
        try:
            sample_data = []
            with open(file_path, 'r', encoding='utf-8-sig') as csvfile:
                reader = csv.DictReader(csvfile)
                
                for i, row in enumerate(reader):
                    if i >= max_rows:
                        break
                    sample_data.append(dict(row))
            
            return True, None, sample_data
        except Exception as e:
            return False, f"读取CSV示例数据时出错: {str(e)}", []
    
    def delete_file(self, file_path: str) -> bool:
        """删除文件"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False
    
    def cleanup_old_files(self, max_age_hours: int = 24) -> int:
        """清理旧文件"""
        import time
        
        cleaned_count = 0
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        try:
            for filename in os.listdir(self.upload_folder):
                file_path = os.path.join(self.upload_folder, filename)
                
                if os.path.isfile(file_path):
                    file_age = current_time - os.path.getmtime(file_path)
                    
                    if file_age > max_age_seconds:
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                        except Exception:
                            pass
        except Exception:
            pass
        
        return cleaned_count


class SimpleUploadHandler:
    """简化的上传处理器，替代复杂的分片上传"""
    
    def __init__(self, upload_service: UploadService):
        self.upload_service = upload_service
    
    def handle_upload(self, file: FileStorage) -> dict:
        """
        处理文件上传
        返回标准化的响应格式
        """
        try:
            success, error, file_path = self.upload_service.save_file(file)
            
            if not success:
                return {
                    'success': False,
                    'error': error
                }
            
            # 获取文件信息
            filename = os.path.basename(file_path)
            success, error, headers = self.upload_service.get_csv_headers(file_path)
            
            if not success:
                # 清理文件
                self.upload_service.delete_file(file_path)
                return {
                    'success': False,
                    'error': error
                }
            
            return {
                'success': True,
                'filename': filename,
                'file_path': file_path,
                'headers': headers,
                'message': '文件上传成功'
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"上传处理失败: {str(e)}"
            }
