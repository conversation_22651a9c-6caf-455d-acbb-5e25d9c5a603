"""
数据服务层 - 统一数据处理逻辑
优化性能，减少代码冗余
"""
import json
import csv
import os
import time
from typing import List, Dict, Any, Optional, Tuple
from functools import lru_cache
from datetime import datetime, timedelta


class DataCache:
    """简单的内存缓存实现"""
    
    def __init__(self, default_ttl: int = 300):  # 5分钟默认过期时间
        self._cache = {}
        self._timestamps = {}
        self.default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if key not in self._cache:
            return None
        
        # 检查是否过期
        if time.time() - self._timestamps[key] > self.default_ttl:
            self.delete(key)
            return None
        
        return self._cache[key]
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存数据"""
        self._cache[key] = value
        self._timestamps[key] = time.time()
    
    def delete(self, key: str) -> None:
        """删除缓存数据"""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)
    
    def clear(self) -> None:
        """清空所有缓存"""
        self._cache.clear()
        self._timestamps.clear()


class DataService:
    """统一的数据服务类"""
    
    def __init__(self, cache_ttl: int = 300):
        self.cache = DataCache(cache_ttl)
        self._price_cache = {}
    
    @lru_cache(maxsize=1000)
    def parse_price(self, price_str: str) -> Optional[float]:
        """优化的价格解析函数，使用LRU缓存"""
        if not price_str:
            return None
        
        try:
            # 清理价格字符串
            clean_str = str(price_str).replace('￥', '').replace('¥', '').replace(',', '')
            clean_str = clean_str.replace('元', '').replace('$', '').replace(' ', '')
            
            if clean_str and clean_str.replace('.', '').replace('-', '').isdigit():
                price = float(clean_str)
                return price if price >= 0 else None
            
            return None
        except (ValueError, TypeError):
            return None
    
    def load_csv_data(self, file_path: str, use_cache: bool = True) -> Tuple[List[Dict], List[str], Optional[str]]:
        """加载CSV数据，支持缓存"""
        cache_key = f"csv_data_{file_path}_{os.path.getmtime(file_path)}"
        
        if use_cache:
            cached_data = self.cache.get(cache_key)
            if cached_data:
                return cached_data
        
        try:
            data = []
            headers = []
            
            with open(file_path, mode='r', encoding='utf-8-sig') as csvfile:
                reader = csv.DictReader(csvfile)
                headers = reader.fieldnames or []
                
                for row in reader:
                    # 预处理价格字段（排除价格带等分类字段）
                    processed_row = {}
                    for key, value in row.items():
                        # 只对纯价格字段进行数字解析，排除价格带、价格区间等分类字段
                        if ('价格' in key or 'price' in key.lower()) and '带' not in key and '区间' not in key and '范围' not in key:
                            processed_row[key] = self.parse_price(value)
                        else:
                            processed_row[key] = value
                    data.append(processed_row)
            
            result = (data, headers, None)
            
            if use_cache:
                self.cache.set(cache_key, result)
            
            return result
            
        except Exception as e:
            return [], [], f"加载CSV文件失败: {str(e)}"
    
    def apply_filters(self, data: List[Dict], filters: Dict[str, Any]) -> List[Dict]:
        """优化的数据筛选"""
        if not filters or not data:
            return data
        
        # 预编译筛选条件以提高性能
        compiled_filters = self._compile_filters(filters)
        
        filtered_data = []
        for item in data:
            if self._item_matches_filters(item, compiled_filters):
                filtered_data.append(item)
        
        return filtered_data
    
    def _compile_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """预编译筛选条件"""
        compiled = {}
        
        for col_name, filter_config in filters.items():
            if filter_config.get('type') == 'category':
                compiled[col_name] = {
                    'type': 'category',
                    'values': set(filter_config.get('values', []))  # 使用set提高查找性能
                }
            elif filter_config.get('type') == 'range':
                compiled[col_name] = {
                    'type': 'range',
                    'min': float(filter_config['min']) if filter_config.get('min') is not None else None,
                    'max': float(filter_config['max']) if filter_config.get('max') is not None else None
                }
        
        return compiled
    
    def _item_matches_filters(self, item: Dict, compiled_filters: Dict[str, Any]) -> bool:
        """检查单个项目是否匹配筛选条件"""
        for col_name, filter_config in compiled_filters.items():
            if filter_config['type'] == 'category':
                item_value = item.get(col_name, '')
                if item_value not in filter_config['values']:
                    if not (item_value in ['', None] and '' in filter_config['values']):
                        return False
            
            elif filter_config['type'] == 'range':
                raw_value = item.get(col_name, '')
                converted_value = self.parse_price(raw_value)
                
                if converted_value is not None:
                    if (filter_config['min'] is not None and 
                        converted_value < filter_config['min']):
                        return False
                    if (filter_config['max'] is not None and 
                        converted_value > filter_config['max']):
                        return False
        
        return True
    
    def group_products_by_axes(self, data: List[Dict], x_axis: str, y_axis: str, 
                              x_secondary: Optional[str] = None, 
                              y_secondary: Optional[str] = None) -> Tuple[Dict, List, List]:
        """优化的产品分组逻辑"""
        cache_key = f"grouped_{hash(str(data))}_{x_axis}_{y_axis}_{x_secondary}_{y_secondary}"
        
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result
        
        grouped_data = {}
        x_values = set()
        y_values = set()
        
        for item in data:
            # 构建轴值
            x_val = self._build_axis_value(item, x_axis, x_secondary)
            y_val = self._build_axis_value(item, y_axis, y_secondary)

            # 跳过空值
            if x_val is None or y_val is None:
                continue

            x_values.add(x_val)
            y_values.add(y_val)

            # 分组数据
            if x_val not in grouped_data:
                grouped_data[x_val] = {}
            if y_val not in grouped_data[x_val]:
                grouped_data[x_val][y_val] = []

            grouped_data[x_val][y_val].append(item)
        
        # 智能排序
        x_sorted = self._smart_sort(list(x_values))
        y_sorted = self._smart_sort(list(y_values))
        
        result = (grouped_data, x_sorted, y_sorted)
        self.cache.set(cache_key, result)
        
        return result
    
    def _build_axis_value(self, item: Dict, primary: str, secondary: Optional[str] = None) -> Optional[str]:
        """构建轴值"""
        primary_val = item.get(primary)
        if primary_val is None or primary_val == '':
            return None  # 返回None表示空值，会被过滤掉
        else:
            primary_val = str(primary_val)

        if not secondary:
            return primary_val

        secondary_val = item.get(secondary)
        if secondary_val is None or secondary_val == '':
            return primary_val  # 副分类为空时，只返回主分类
        else:
            secondary_val = str(secondary_val)

        return f"{primary_val} | {secondary_val}"
    
    def _smart_sort(self, values: List[str]) -> List[str]:
        """智能排序：数字优先，然后字符串（不再处理空值）"""
        numbers = []
        strings = []

        for val in set(values):
            try:
                num_val = float(val)
                numbers.append((num_val, val))
            except (ValueError, TypeError):
                strings.append(val)

        numbers.sort(key=lambda x: x[0])
        strings.sort()

        return [item[1] for item in numbers] + strings
    
    def aggregate_products(self, data: List[Dict], index_column: str, 
                          price_column: Optional[str] = None) -> List[Dict]:
        """产品聚合处理"""
        if not index_column:
            return data
        
        aggregated = {}
        
        for item in data:
            index_val = item.get(index_column)
            if index_val is None:
                continue
            
            if index_val not in aggregated:
                aggregated[index_val] = dict(item)
                if price_column:
                    aggregated[index_val]['_prices'] = []
            
            if price_column and price_column in item:
                price = self.parse_price(item[price_column])
                if price is not None:
                    aggregated[index_val]['_prices'].append(price)
        
        # 计算平均价格
        result = []
        for index_val, product in aggregated.items():
            if price_column and '_prices' in product:
                prices = product['_prices']
                if prices:
                    avg_price = sum(prices) / len(prices)
                    product[price_column] = f"{avg_price:.2f}"
                del product['_prices']
            result.append(product)
        
        return result


# 全局数据服务实例
data_service = DataService()
