"""
性能监控和分析工具
"""
import time
import functools
import logging
from typing import Callable, Any
from datetime import datetime


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.logger = logging.getLogger(__name__)
    
    def timing_decorator(self, func_name: str = None):
        """函数执行时间装饰器"""
        def decorator(func: Callable) -> Callable:
            name = func_name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    end_time = time.time()
                    execution_time = end_time - start_time
                    self.record_timing(name, execution_time)
            
            return wrapper
        return decorator
    
    def record_timing(self, operation: str, duration: float):
        """记录操作耗时"""
        if operation not in self.metrics:
            self.metrics[operation] = {
                'count': 0,
                'total_time': 0,
                'min_time': float('inf'),
                'max_time': 0,
                'last_execution': None
            }
        
        metric = self.metrics[operation]
        metric['count'] += 1
        metric['total_time'] += duration
        metric['min_time'] = min(metric['min_time'], duration)
        metric['max_time'] = max(metric['max_time'], duration)
        metric['last_execution'] = datetime.now()
        
        # 记录慢查询
        if duration > 1.0:  # 超过1秒的操作
            self.logger.warning(f"慢操作检测: {operation} 耗时 {duration:.2f}秒")
    
    def get_metrics(self) -> dict:
        """获取性能指标"""
        result = {}
        for operation, metric in self.metrics.items():
            avg_time = metric['total_time'] / metric['count'] if metric['count'] > 0 else 0
            result[operation] = {
                'count': metric['count'],
                'avg_time': round(avg_time, 4),
                'min_time': round(metric['min_time'], 4),
                'max_time': round(metric['max_time'], 4),
                'total_time': round(metric['total_time'], 4),
                'last_execution': metric['last_execution'].isoformat() if metric['last_execution'] else None
            }
        return result
    
    def reset_metrics(self):
        """重置性能指标"""
        self.metrics.clear()


class MemoryProfiler:
    """内存使用分析器"""
    
    @staticmethod
    def get_memory_usage():
        """获取当前内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            return {
                'rss': memory_info.rss,  # 物理内存
                'vms': memory_info.vms,  # 虚拟内存
                'percent': process.memory_percent(),  # 内存使用百分比
                'available': psutil.virtual_memory().available
            }
        except ImportError:
            return {'error': 'psutil not available'}
    
    @staticmethod
    def memory_usage_decorator(func: Callable) -> Callable:
        """内存使用监控装饰器"""
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            before = MemoryProfiler.get_memory_usage()
            result = func(*args, **kwargs)
            after = MemoryProfiler.get_memory_usage()
            
            if 'error' not in before and 'error' not in after:
                memory_diff = after['rss'] - before['rss']
                if memory_diff > 10 * 1024 * 1024:  # 超过10MB
                    logging.warning(f"函数 {func.__name__} 内存使用增加 {memory_diff / 1024 / 1024:.2f}MB")
            
            return result
        return wrapper


class QueryOptimizer:
    """查询优化建议器"""
    
    @staticmethod
    def analyze_data_access_pattern(data_size: int, filter_count: int, group_count: int) -> dict:
        """分析数据访问模式并提供优化建议"""
        suggestions = []
        
        # 数据量建议
        if data_size > 10000:
            suggestions.append("数据量较大，建议启用分页功能")
        
        if data_size > 50000:
            suggestions.append("数据量很大，建议考虑使用数据库存储")
        
        # 筛选建议
        if filter_count > 5:
            suggestions.append("筛选条件较多，建议优化筛选逻辑")
        
        # 分组建议
        if group_count > 1000:
            suggestions.append("分组数量较多，可能影响渲染性能")
        
        return {
            'data_size': data_size,
            'filter_count': filter_count,
            'group_count': group_count,
            'suggestions': suggestions,
            'recommended_actions': QueryOptimizer._get_recommended_actions(data_size, filter_count, group_count)
        }
    
    @staticmethod
    def _get_recommended_actions(data_size: int, filter_count: int, group_count: int) -> list:
        """获取推荐的优化操作"""
        actions = []
        
        if data_size > 10000:
            actions.append("enable_pagination")
        
        if data_size > 50000:
            actions.append("consider_database")
        
        if filter_count > 3:
            actions.append("optimize_filters")
        
        if group_count > 500:
            actions.append("limit_grouping")
        
        return actions


# 全局性能监控实例
performance_monitor = PerformanceMonitor()

# 常用装饰器
def monitor_performance(operation_name: str = None):
    """性能监控装饰器"""
    return performance_monitor.timing_decorator(operation_name)

def monitor_memory(func: Callable) -> Callable:
    """内存监控装饰器"""
    return MemoryProfiler.memory_usage_decorator(func)


# 性能分析上下文管理器
class PerformanceContext:
    """性能分析上下文管理器"""
    
    def __init__(self, operation_name: str):
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            performance_monitor.record_timing(self.operation_name, duration)
