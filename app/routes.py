# This is where we will define our web   pages (routes)
import os
import json # 导入json模块
import uuid # 导入uuid模块
from datetime import datetime, timezone # 导入datetime模块
from flask import render_template, request, redirect, url_for, flash, session, current_app, jsonify, Response
from werkzeug.security import generate_password_hash, check_password_hash # 导入密码处理工具
from werkzeug.utils import secure_filename # 导入 secure_filename
from functools import wraps # 导入wraps，用于创建装饰器
from app import app
import csv # 导入csv模块
import requests # 导入requests模块用于图片代理
import base64 # 导入base64模块
from urllib.parse import urlparse # 导入URL解析模块

# 导入新的服务层
from app.services.data_service import data_service
from app.services.upload_service import SimpleUploadHandler
from app.utils.performance import monitor_performance, PerformanceContext

# --- 配置 --- #
USER_DATA_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'users.json')
DATASOURCE_DATA_FILE = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'datasources.json')
UPLOAD_FOLDER_CSV = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads', 'csv')
UPLOAD_FOLDER_TEMP = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'uploads', 'temp')
ALLOWED_EXTENSIONS_CSV = {'csv'}

# 确保上传文件夹存在
os.makedirs(UPLOAD_FOLDER_CSV, exist_ok=True)
os.makedirs(UPLOAD_FOLDER_TEMP, exist_ok=True)

app.config['UPLOAD_FOLDER_CSV'] = UPLOAD_FOLDER_CSV
app.config['UPLOAD_FOLDER_TEMP'] = UPLOAD_FOLDER_TEMP
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB 最大文件大小
app.config['CHUNK_SIZE'] = 1024 * 1024  # 1MB 分片大小

# --- 数据分组函数 --- #

def smart_sort(values):
    """智能排序：数字优先，然后字符串（不再处理空值）"""
    unique_values = list(set(values))

    # 分离不同类型的值
    numbers = []
    strings = []

    for val in unique_values:
        # 尝试转换为数字
        try:
            num_val = float(val)
            numbers.append((num_val, val))
        except (ValueError, TypeError):
            strings.append(val)

    # 排序各类别
    numbers.sort(key=lambda x: x[0])  # 按数值排序
    strings.sort()  # 按字符串排序

    # 组合结果：数字 + 字符串
    result = [item[1] for item in numbers] + strings
    return result

def clean_value(value):
    """清理和标准化值"""
    if value is None or value == '':
        return None  # 返回None表示空值，后续会被过滤掉
    else:
        # 处理数字类型（包括价格）
        if isinstance(value, (int, float)):
            return str(value)
        return str(value).strip()

def create_single_classification_groups(product_data, x_axis, y_axis):
    """创建单级分类的分组数据"""
    # 1. 提取并清理所有X轴和Y轴的值
    x_values_raw = []
    y_values_raw = []

    for item in product_data:
        x_val = clean_value(item.get(x_axis))
        y_val = clean_value(item.get(y_axis))
        # 只添加非空值
        if x_val is not None:
            x_values_raw.append(x_val)
        if y_val is not None:
            y_values_raw.append(y_val)

    # 2. 获取唯一值并智能排序
    x_axis_values = smart_sort(x_values_raw)
    y_axis_values = smart_sort(y_values_raw)

    current_app.logger.info(f"X轴值({len(x_axis_values)}): {x_axis_values[:5]}...")
    current_app.logger.info(f"Y轴值({len(y_axis_values)}): {y_axis_values[:5]}...")

    # 3. 初始化分组数据结构
    grouped_data = {}
    for x_val in x_axis_values:
        grouped_data[x_val] = {}
        for y_val in y_axis_values:
            grouped_data[x_val][y_val] = []

    # 4. 将产品数据分配到对应的组
    grouped_count = 0
    skipped_count = 0
    for item in product_data:
        x_val = clean_value(item.get(x_axis))
        y_val = clean_value(item.get(y_axis))

        # 跳过空值产品
        if x_val is None or y_val is None:
            skipped_count += 1
            continue

        # 确保值存在于分组中
        if x_val in grouped_data and y_val in grouped_data[x_val]:
            grouped_data[x_val][y_val].append(item)
            grouped_count += 1
        else:
            current_app.logger.warning(f"无法分组产品：X={x_val}, Y={y_val} (不在预期的轴值中)")

    current_app.logger.info(f"分组完成，共分组 {grouped_count} 个产品，跳过 {skipped_count} 个空值产品")

    return grouped_data, x_axis_values, y_axis_values

# 旧的二级分类函数已移除，使用新的垂直层级布局函数

# --- 辅助函数：检查文件扩展名 --- #
def allowed_file_csv(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS_CSV

# 加载用户数据
def load_users():
    if not os.path.exists(USER_DATA_FILE):
        # 文件不存在，创建并写入默认管理员用户
        default_admin_password = generate_password_hash('adminpass')
        default_users = {
            'admin1': {
                'password_hash': default_admin_password,
                'role': 'admin',
                'name': '默认管理员'
            }
        }
        save_users(default_users) # 保存到文件
        return default_users
    try:
        with open(USER_DATA_FILE, 'r', encoding='utf-8') as f:
            users = json.load(f)
            # 确保密码是哈希过的 (兼容可能存在的旧格式或手动编辑的文件)
            # 这一步对于全新开始的项目，且密码总是通过generate_password_hash写入则不是严格必须
            # 但作为健壮性考虑加上，确保旧数据也能被正确处理（如果旧数据密码字段不是password_hash）
            # for username, data in users.items():
            #     if 'password' in data and 'password_hash' not in data:
            #         users[username]['password_hash'] = generate_password_hash(data.pop('password'))
            #         users[username].pop('password', None) # 移除明文密码字段
            return users
    except (json.JSONDecodeError, FileNotFoundError):
        # 文件损坏或再次未找到（理论上首次已处理），返回一个空的或者默认的结构以避免程序崩溃
        # 也可以选择抛出异常，让应用启动失败，提示管理员检查
        flash("用户数据文件损坏或丢失，已尝试重置为默认管理员。请检查 users.json 文件。", "error")
        default_admin_password = generate_password_hash('adminpass')
        default_users = {
            'admin1': {
                'password_hash': default_admin_password,
                'role': 'admin',
                'name': '默认管理员'
            }
        }
        save_users(default_users) # 尝试修复
        return default_users

# 保存用户数据
def save_users(users_data):
    with open(USER_DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(users_data, f, ensure_ascii=False, indent=4)

# 初始化用户数据 - 现在从文件加载
TEMP_USERS = load_users()

# --- Helper function to get user habits ---
def get_user_habits(user_id):
    if not user_id:
        return []
    users = load_users()
    user_data = users.get(user_id)
    if user_data:
        return user_data.get('habits', [])
    return []

# --- 数据源数据加载/保存 --- #
def load_datasources():
    if not os.path.exists(DATASOURCE_DATA_FILE):
        save_datasources({}) # 创建一个空的JSON对象文件
        return {}
    try:
        with open(DATASOURCE_DATA_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        flash("数据源文件损坏或丢失，已重置为空。请检查 datasources.json 文件。", "error")
        save_datasources({})
        return {}

def save_datasources(datasources_data):
    with open(DATASOURCE_DATA_FILE, 'w', encoding='utf-8') as f:
        json.dump(datasources_data, f, ensure_ascii=False, indent=4)

DATASOURCES = load_datasources() # 初始化全局数据源变量

# 管理员访问权限装饰器
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if session.get('user_role') != 'admin':
            flash('此页面需要管理员权限才能访问。', 'error')
            return redirect(url_for('login', next=request.url))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/')
@app.route('/index')
def index():
    user_name = session.get('user_name')
    user_role = session.get('user_role')
    user_id = session.get('user_id')

    available_datasources = load_datasources()
    selected_datasource_id = request.args.get('selected_datasource_id')
    selected_datasource_info = None
    product_data = []
    data_error_message = None
    csv_headers = [] # Initialize headers list
    grouped_data = {}
    x_axis_values = []
    y_axis_values = []
    selected_x_axis = request.args.get('selected_x_axis')
    selected_y_axis = request.args.get('selected_y_axis')
    # 二级分类支持
    selected_x_axis_secondary = request.args.get('selected_x_axis_secondary')
    selected_y_axis_secondary = request.args.get('selected_y_axis_secondary')
    user_habits = [] # Initialize user_habits
    applied_filters = None # 初始化筛选参数变量

    # 获取URL中的筛选参数
    filters_param = request.args.get('filters')
    if filters_param:
        try:
            applied_filters = json.loads(filters_param)
        except json.JSONDecodeError:
            flash("筛选参数格式错误，已忽略筛选条件", "warning")
            applied_filters = None

    if user_id: # If user is logged in, load their habits
        user_habits = get_user_habits(user_id)

    if selected_datasource_id and selected_datasource_id in available_datasources:
        selected_datasource_info = available_datasources[selected_datasource_id]
        
        if selected_datasource_info['type'] == 'local_csv':
            if 'filename' in selected_datasource_info['details']:
                csv_filename = selected_datasource_info['details']['filename']
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER_CSV'], csv_filename)

                # 使用新的数据服务加载CSV数据（带缓存）
                with PerformanceContext("load_csv_data"):
                    try:
                        if not os.path.exists(file_path):
                            data_error_message = f"错误：找不到CSV文件 {csv_filename}。请联系管理员检查数据源配置。"
                        else:
                            # 使用缓存的数据服务
                            product_data_raw, csv_headers, load_error = data_service.load_csv_data(file_path, use_cache=True)

                            if load_error:
                                data_error_message = load_error
                            elif not csv_headers:
                                data_error_message = "错误：CSV文件似乎没有表头行。"
                            elif not product_data_raw:
                                flash("提示：CSV文件包含表头，但没有数据行。", "info")
                                product_data = []
                            else:
                                # 使用数据服务进行聚合处理
                                index_col = selected_datasource_info.get('index_column')
                                price_col = selected_datasource_info.get('product_price_column')

                                if index_col:
                                    product_data = data_service.aggregate_products(
                                        product_data_raw, index_col, price_col
                                    )
                                else:
                                    product_data = product_data_raw

                                # 应用筛选条件（使用新的数据服务）
                                if applied_filters and applied_filters.get('columns') and product_data:
                                    with PerformanceContext("apply_filters"):
                                        try:
                                            original_count = len(product_data)
                                            product_data = data_service.apply_filters(product_data, applied_filters['columns'])
                                            filtered_count = len(product_data)
                                            if filtered_count < original_count:
                                                flash(f"筛选后显示 {filtered_count} / {original_count} 条数据", "info")
                                        except Exception as filter_error:
                                            flash(f"应用筛选时出错: {filter_error}", "warning")
                    except FileNotFoundError:
                        data_error_message = f"错误：CSV文件 {csv_filename} 未找到。"
                    except UnicodeDecodeError:
                        data_error_message = f"错误：无法以UTF-8编码解析CSV文件 {csv_filename}。请确保文件是UTF-8编码。"
                    except Exception as e:
                        data_error_message = f"加载CSV数据时发生未知错误: {e}"
            else:
                data_error_message = "错误：此CSV数据源配置不完整（缺少文件名信息）。"
        
        # 移除了飞书数据源支持

        # ---- 使用新的数据分组服务 ----
        if selected_x_axis and selected_y_axis and csv_headers and not data_error_message:
            # 检查主分类字段是否存在
            if selected_x_axis in csv_headers and selected_y_axis in csv_headers:
                # 检查二级分类字段是否存在（如果选择了的话）
                secondary_fields_valid = True
                if selected_x_axis_secondary and selected_x_axis_secondary not in csv_headers:
                    secondary_fields_valid = False
                if selected_y_axis_secondary and selected_y_axis_secondary not in csv_headers:
                    secondary_fields_valid = False

                if secondary_fields_valid:
                    try:
                        # 判断是否使用二级分类
                        use_secondary_classification = bool(selected_x_axis_secondary or selected_y_axis_secondary)

                        if use_secondary_classification:
                            # 使用二级分类函数进行分组
                            with PerformanceContext("create_secondary_classification_groups"):
                                grouped_data, x_axis_values, y_axis_values, x_axis_hierarchy, y_axis_hierarchy = create_secondary_classification_groups(
                                    product_data, selected_x_axis, selected_y_axis,
                                    selected_x_axis_secondary, selected_y_axis_secondary
                                )
                        else:
                            # 使用简单分类进行分组
                            with PerformanceContext("create_single_classification_groups"):
                                grouped_data, x_axis_values, y_axis_values = create_single_classification_groups(
                                    product_data, selected_x_axis, selected_y_axis
                                )
                                # 初始化层次结构（保持兼容性）
                                x_axis_hierarchy = {}
                                y_axis_hierarchy = {}

                            current_app.logger.info(f"数据分组完成：X轴={len(x_axis_values)}个值, Y轴={len(y_axis_values)}个值, 总产品={len(product_data)}个")

                    except Exception as e:
                        current_app.logger.error(f"数据分组出错: {str(e)}", exc_info=True)
                        data_error_message = f"进行数据分组时出错: {e}"
                        grouped_data = {}
                        x_axis_values = []
                        y_axis_values = []
            else:
                data_error_message = "错误：选择的X轴或Y轴字段在数据中不存在。"
                selected_x_axis = None # Reset selection if invalid
                selected_y_axis = None
                grouped_data = {}
                x_axis_values = []
                y_axis_values = []

    return render_template('index.html',
                           user_name=user_name,
                           user_role=user_role,
                           available_datasources=available_datasources,
                           selected_datasource_id=selected_datasource_id,
                           selected_datasource_info=selected_datasource_info,
                           product_data=product_data,
                           data_error_message=data_error_message,
                           csv_headers=csv_headers, # Ensure headers are passed
                           selected_x_axis=selected_x_axis,
                           selected_y_axis=selected_y_axis,
                           selected_x_axis_secondary=selected_x_axis_secondary,
                           selected_y_axis_secondary=selected_y_axis_secondary,
                           x_axis_values=x_axis_values,
                           y_axis_values=y_axis_values,
                           grouped_data=grouped_data,
                           x_axis_hierarchy=locals().get('x_axis_hierarchy', {}),
                           y_axis_hierarchy=locals().get('y_axis_hierarchy', {}),
                           use_secondary_classification=locals().get('use_secondary_classification', False),
                           user_habits=user_habits,
                           applied_filters=applied_filters) # 传递筛选参数到模板

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')
        login_type = request.form.get('login_type')

        user_data = TEMP_USERS.get(username) # TEMP_USERS 现在是从文件加载的

        if user_data and check_password_hash(user_data['password_hash'], password):
            can_login = False
            if login_type == 'admin' and user_data['role'] == 'admin':
                can_login = True
                session['user_role'] = 'admin'
            elif login_type == 'user':
                can_login = True
                session['user_role'] = user_data['role']
            
            if can_login:
                session['user_id'] = username
                session['user_name'] = user_data['name']
                flash(f'{user_data["name"]} ({session["user_role"]}) 欢迎回来!', 'success')
                next_page = request.args.get('next')
                if next_page:
                    return redirect(next_page)
                return redirect(url_for('index'))
            else:
                flash('你尝试登录的身份与账号角色不符。', 'error')
        else:
            flash('账号或密码错误，请重试。', 'error')
        
        return redirect(url_for('login'))

    return render_template('login.html')

@app.route('/logout')
def logout():
    session.pop('user_id', None)
    session.pop('user_name', None)
    session.pop('user_role', None)
    flash('你已成功退出登录。', 'info')
    return redirect(url_for('index'))

# ---- 管理员后台路由 ----
@app.route('/admin/')
@app.route('/admin/dashboard')
@admin_required
def admin_dashboard():
    return render_template('admin/dashboard.html')

@app.route('/admin/users')
@admin_required
def admin_users():
    # 为了确保users.html总是显示最新的用户列表（特别是添加用户后），
    # 我们可以选择在这里重新加载一次，或者依赖于TEMP_USERS在其他地方被正确更新和保存。
    # 考虑到我们添加用户后会保存并重定向，此时TEMP_USERS应该已经是新的了。
    # 但如果担心TEMP_USERS在内存中的状态和文件不一致（比如多实例或复杂场景），这里可以改为 users = load_users()
    current_users = load_users() # 确保每次都从文件加载最新列表展示
    return render_template('admin/users.html', users=current_users)

@app.route('/admin/users/add', methods=['GET', 'POST'])
@admin_required
def admin_add_user():
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        confirm_password = request.form.get('confirm_password', '')
        role = request.form.get('role', 'user')
        display_name = username

        errors = False
        if not username:
            flash('用户名不能为空。', 'error')
            errors = True
        if not password:
            flash('密码不能为空。', 'error')
            errors = True
        if password != confirm_password:
            flash('两次输入的密码不一致。', 'error')
            errors = True
        
        current_users_for_check = load_users() 
        if username in current_users_for_check:
            flash(f'用户名 "{username}" 已存在，请使用其他用户名。', 'error')
            errors = True

        if not errors:
            hashed_password = generate_password_hash(password)
            current_users_for_check[username] = {
                'password_hash': hashed_password,
                'role': role,
                'name': display_name 
            }
            save_users(current_users_for_check)
            global TEMP_USERS
            TEMP_USERS = current_users_for_check

            flash(f'用户 "{username}" ({role}) 创建成功！', 'success')
            return redirect(url_for('admin_users'))
        
        return render_template('admin/add_user.html', 
                               username=username, 
                               role=role) 

    return render_template('admin/add_user.html')

# ---- 数据源管理路由 ----
@app.route('/admin/datasources')
@admin_required
def admin_datasources():
    current_datasources = load_datasources()
    return render_template('admin/datasources.html', datasources=current_datasources)

@app.route('/admin/datasources/add', methods=['GET', 'POST'])
@admin_required
def admin_add_datasource():
    """添加新数据源"""
    announcement_enabled_val = False  # 默认值
    csv_columns = []  # 初始化为空列表
    
    if request.method == 'POST':
        # 获取表单数据
        name = request.form.get('name', '').strip()
        datasource_type = request.form.get('type', '').strip()
        image_column_name = request.form.get('image_column_name', '').strip()
        index_column = request.form.get('index_column', '').strip()
        product_price_column = request.form.get('product_price_column', '').strip()
        announcement = request.form.get('announcement', '').strip()
        # 处理布尔值
        announcement_enabled = request.form.get('announcement_enabled') == 'true'
        
        errors = False
        
        # 验证必填字段
        if not name:
            flash('数据源名称不能为空!', 'error')
            errors = True
            
        if not datasource_type:
            flash('请选择数据源类型!', 'error')
            errors = True
            
        # 根据数据源类型处理对应的详细信息
        datasource_details = {}
        if datasource_type == 'local_csv':
            # 检查是否使用分片上传
            uploaded_filename = request.form.get('uploaded_filename', '').strip()
            
            if uploaded_filename:
                # 使用分片上传的文件
                file_path = os.path.join(current_app.config['UPLOAD_FOLDER_CSV'], uploaded_filename)
                if not os.path.exists(file_path):
                    flash('上传的文件不存在，请重新上传', 'error')
                    errors = True
                else:
                    datasource_details['filename'] = uploaded_filename
                    try:
                        # 读取CSV文件的列名（第一行）
                        with open(file_path, mode='r', encoding='utf-8-sig') as csvfile:
                            reader = csv.reader(csvfile)
                            header = next(reader, [])  # 读取CSV的第一行（列名）
                            csv_columns = header
                    except Exception as e:
                        flash(f'读取CSV文件列名时出错: {e}', 'error')
                        errors = True
            else:
                # 传统文件上传方式（保留兼容性）
                if 'csv_file' not in request.files:
                    flash('没有选择CSV文件', 'error')
                    errors = True
                else:
                    csv_file = request.files['csv_file']
                    if csv_file.filename == '':
                        flash('没有选择CSV文件', 'error')
                        errors = True
                    elif not csv_file.filename.lower().endswith('.csv'):
                        flash('只允许上传CSV文件，请重新选择', 'error')
                        errors = True
                    else:
                        # 处理和保存CSV文件
                        filename = secure_filename(csv_file.filename)
                        # 添加唯一标识符，防止文件名冲突
                        unique_filename = f"{uuid.uuid4().hex}_{filename}"
                        file_path = os.path.join(current_app.config['UPLOAD_FOLDER_CSV'], unique_filename)
                        
                        try:
                            # 确保上传目录存在
                            os.makedirs(current_app.config['UPLOAD_FOLDER_CSV'], exist_ok=True)
                            csv_file.save(file_path)
                            datasource_details['filename'] = unique_filename
                            
                            # 读取CSV文件的列名（第一行）
                            with open(file_path, mode='r', encoding='utf-8-sig') as csvfile:
                                reader = csv.reader(csvfile)
                                header = next(reader, [])  # 读取CSV的第一行（列名）
                                csv_columns = header
                        except Exception as e:
                            flash(f'保存CSV文件或读取列名时出错: {e}', 'error')
                            errors = True
        
        # 如果没有错误，保存数据源信息
        if not errors:
            # 加载现有数据源
            current_datasources = load_datasources()
            
            # 生成新数据源的唯一ID
            datasource_id = str(uuid.uuid4())
            
            # 创建数据源字典
            new_datasource = {
                'name': name,
                'type': datasource_type,
                'details': datasource_details,
                'image_column_name': image_column_name if image_column_name else None,
                'index_column': index_column if index_column else None,
                'product_price_column': product_price_column if product_price_column else None,
                'announcement': announcement,
                'announcement_enabled': announcement_enabled,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat()
            }
            
            # 添加可用坐标轴配置
            if datasource_type == 'local_csv' and csv_columns:
                # 为CSV文件添加可用坐标轴配置
                valid_columns = [col for col in csv_columns if col and col.strip()]
                new_datasource['key_columns_config'] = {
                    'available_x_axes': valid_columns,
                    'available_y_axes': valid_columns
                }
            
            # 添加到现有数据源
            current_datasources[datasource_id] = new_datasource
            
            # 保存数据源
            save_datasources(current_datasources)
            # 更新全局变量
            global DATASOURCES
            DATASOURCES = current_datasources
            
            flash(f'数据源 "{name}" 添加成功!', 'success')
            return redirect(url_for('admin_datasources'))
    
    # GET请求，展示添加数据源的表单
    return render_template('admin/add_datasource.html', 
                           announcement_enabled_val=announcement_enabled_val,
                           csv_columns=csv_columns)

@app.route('/admin/datasources/delete/<datasource_id>', methods=['GET']) # Changed to GET for simplicity, POST is safer
@admin_required
def admin_delete_datasource(datasource_id):
    current_datasources = load_datasources()
    datasource_to_delete = current_datasources.get(datasource_id)

    if not datasource_to_delete:
        flash('错误：找不到要删除的数据源。', 'error')
        return redirect(url_for('admin_datasources'))

    datasource_name = datasource_to_delete.get('name', datasource_id) # For flash message

    # If it's a local CSV, try to delete the associated file
    if datasource_to_delete.get('type') == 'local_csv':
        filename_to_delete = datasource_to_delete.get('details', {}).get('filename')
        if filename_to_delete:
            file_path_to_delete = os.path.join(current_app.config['UPLOAD_FOLDER_CSV'], filename_to_delete)
            try:
                if os.path.exists(file_path_to_delete):
                    os.remove(file_path_to_delete)
                    flash(f'关联的CSV文件 {filename_to_delete} 已删除。', 'info')
                else:
                    flash(f'关联的CSV文件 {filename_to_delete} 未找到，可能已被手动删除。', 'warning')
            except Exception as e:
                flash(f'删除关联的CSV文件 {filename_to_delete} 时出错: {e}。请手动检查。', 'error')
                # Decide if you want to proceed with deleting the datasource entry even if file deletion fails
                # For now, we proceed.

    # Delete the datasource entry from the dictionary
    del current_datasources[datasource_id]
    save_datasources(current_datasources) # Save the updated data
    
    global DATASOURCES # Update global variable
    DATASOURCES = current_datasources
    
    flash(f'数据源 "{datasource_name}" 已成功删除。', 'success')
    return redirect(url_for('admin_datasources'))

@app.route('/admin/datasources/update_csv/<datasource_id>', methods=['GET', 'POST'])
@admin_required
def admin_update_datasource_csv(datasource_id):
    current_datasources = load_datasources()
    if datasource_id not in current_datasources or current_datasources[datasource_id]['type'] != 'local_csv':
        flash('无效的数据源ID或该数据源不是CSV类型。', 'error')
        return redirect(url_for('admin_datasources'))

    datasource = current_datasources[datasource_id]

    if request.method == 'POST':
        new_name = request.form.get('name', datasource['name']).strip()
        if not new_name:
            flash('数据源名称不能为空。', 'error')
            # 重新渲染表单，但保留已有的数据源信息和ID
            return render_template('admin/update_datasource_csv.html', datasource=datasource, datasource_id=datasource_id)

        # 检查是否使用分片上传
        uploaded_filename = request.form.get('uploaded_filename', '').strip()
        
        if uploaded_filename:
            # 使用分片上传的文件
            new_file_path = os.path.join(current_app.config['UPLOAD_FOLDER_CSV'], uploaded_filename)
            if not os.path.exists(new_file_path):
                flash('上传的文件不存在，请重新上传。', 'error')
                return render_template('admin/update_datasource_csv.html', datasource=datasource, datasource_id=datasource_id)
            
            unique_filename = uploaded_filename
        else:
            # 传统文件上传方式（保留兼容性）
            if 'csv_file' not in request.files:
                flash('没有选择新的CSV文件进行更新。', 'error')
                return render_template('admin/update_datasource_csv.html', datasource=datasource, datasource_id=datasource_id)
            
            new_csv_file = request.files['csv_file']

            if new_csv_file.filename == '':
                flash('没有选择新的CSV文件进行更新。', 'error')
                return render_template('admin/update_datasource_csv.html', datasource=datasource, datasource_id=datasource_id)

            if not new_csv_file.filename.lower().endswith('.csv'):
                flash('只允许上传CSV文件，请重新选择。', 'error')
                return render_template('admin/update_datasource_csv.html', datasource=datasource, datasource_id=datasource_id)

            # 2. 保存新的CSV文件（传统上传方式）
            filename = secure_filename(new_csv_file.filename)
            unique_filename = f"{uuid.uuid4().hex}_{filename}"
            new_file_path = os.path.join(current_app.config['UPLOAD_FOLDER_CSV'], unique_filename)
            
            try:
                os.makedirs(current_app.config['UPLOAD_FOLDER_CSV'], exist_ok=True)
                new_csv_file.save(new_file_path)
            except Exception as e:
                flash(f'保存新CSV文件时出错: {e}', 'error')
                # 如果保存新文件失败，则不应继续更新数据库记录，并尝试恢复（如果可能）或提示用户
                # 此处简单返回，不更新记录
                return render_template('admin/update_datasource_csv.html', datasource=datasource, datasource_id=datasource_id)

        # 1. 删除旧的CSV文件（在新文件准备好后）
        old_filename = datasource.get('details', {}).get('filename')
        if old_filename and old_filename != unique_filename:  # 避免删除同一个文件
            old_file_path = os.path.join(current_app.config['UPLOAD_FOLDER_CSV'], old_filename)
            try:
                if os.path.exists(old_file_path):
                    os.remove(old_file_path)
                    flash(f'旧CSV文件 {old_filename} 已被删除。', 'info')
            except Exception as e:
                flash(f'删除旧CSV文件 {old_filename} 时出错: {e}。请手动检查。', 'error')
                # 即使删除旧文件失败，也继续更新记录

        # 3. 更新datasource记录
        datasource['name'] = new_name
        datasource['details']['filename'] = unique_filename # 更新文件名
        # datasource['details']['original_filename'] = filename # 如果需要保留原始文件名
        datasource['updated_at'] = datetime.now(timezone.utc).isoformat()

        # 4. 列名兼容性检查 (核心逻辑)
        new_csv_columns = []
        try:
            with open(new_file_path, mode='r', encoding='utf-8-sig') as csvfile:
                reader = csv.reader(csvfile)
                header = next(reader, [])
                new_csv_columns = [col.strip() for col in header if col.strip()] # 获取并清理列名
        except Exception as e:
            flash(f'读取新CSV文件列名时出错: {e}。请稍后编辑数据源以确认列配置。', 'warning')
        
        lost_columns_messages = []
        def check_and_update_column(col_key, col_name_user_friendly):
            current_col_val = datasource.get(col_key)
            if current_col_val and current_col_val not in new_csv_columns:
                lost_columns_messages.append(f'先前配置的 "{col_name_user_friendly}" ({current_col_val}) 在新文件中不存在，已被清除。')
                datasource[col_key] = None # 清除配置
            # 如果原先没有配置，或者配置的列仍然存在，则保持不变或用户可以在编辑页面调整

        check_and_update_column('index_column', '索引列')
        check_and_update_column('image_column_name', '图片列')
        check_and_update_column('product_price_column', '产品价格列')

        if lost_columns_messages:
            for msg in lost_columns_messages:
                flash(msg, 'warning')
            flash('由于部分列名在新文件中无法找到，相关的列配置已被清除。请检查并前往"编辑"页面重新配置这些列。 ', 'warning')

        # 5. 保存更新后的datasources.json
        save_datasources(current_datasources)
        global DATASOURCES
        DATASOURCES = current_datasources

        flash(f'数据源 "{new_name}" 的CSV文件已成功更新！', 'success')
        return redirect(url_for('admin_datasources'))

    # GET请求
    return render_template('admin/update_datasource_csv.html', datasource=datasource, datasource_id=datasource_id)

# 实现编辑数据源路由
@app.route('/admin/datasources/edit/<datasource_id>', methods=['GET', 'POST'])
@admin_required
def admin_edit_datasource(datasource_id):
    current_datasources = load_datasources()
    
    if datasource_id not in current_datasources:
        flash('数据源不存在或已被删除', 'error')
        return redirect(url_for('admin_datasources'))
    
    datasource = current_datasources[datasource_id]
    csv_columns = []
    
    # 如果是CSV文件，读取列名供图片列选择
    if datasource['type'] == 'local_csv' and 'filename' in datasource.get('details', {}):
        filename = datasource['details']['filename']
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER_CSV'], filename)
        try:
            if os.path.exists(file_path):
                with open(file_path, mode='r', encoding='utf-8-sig') as csvfile:
                    reader = csv.reader(csvfile)
                    header = next(reader, [])  # 读取CSV的第一行（列名）
                    csv_columns = header
        except Exception as e:
            flash(f'读取CSV文件列名时出错: {e}', 'error')
    
    if request.method == 'POST':
        name = request.form.get('name', '').strip()
        announcement = request.form.get('announcement', '').strip()
        announcement_enabled = request.form.get('announcement_enabled') == 'true'
        image_column_name = request.form.get('image_column_name', '')
        index_column = request.form.get('index_column', '')
        product_price_column = request.form.get('product_price_column', '')

        # Get selected available X and Y axes
        selected_x_axes = request.form.getlist('available_x_axes')
        selected_y_axes = request.form.getlist('available_y_axes')
        
        errors = False
        if not name:
            flash('数据源名称不能为空', 'error')
            errors = True
            
        if not errors:
            # 更新数据源信息
            datasource['name'] = name
            datasource['announcement'] = announcement
            datasource['announcement_enabled'] = announcement_enabled
            datasource['image_column_name'] = image_column_name if image_column_name else None
            
            if datasource['type'] == 'local_csv':
                datasource['index_column'] = index_column if index_column else None
                datasource['product_price_column'] = product_price_column if product_price_column else None
                # Save key_columns_config for CSV types
                datasource['key_columns_config'] = {
                    'available_x_axes': selected_x_axes,
                    'available_y_axes': selected_y_axes
                }
            else:
                datasource['index_column'] = None
                datasource['product_price_column'] = None
                datasource.pop('key_columns_config', None) # Remove if not CSV type

            datasource['updated_at'] = datetime.now(timezone.utc).isoformat()
            
            # --- DEBUG PRINT --- 
            print(f"DEBUG: About to save datasource ID: {datasource_id}")
            if datasource['type'] == 'local_csv':
                print(f"DEBUG: key_columns_config for {datasource_id}: {datasource.get('key_columns_config')}")
            else:
                print(f"DEBUG: Datasource {datasource_id} is not CSV, key_columns_config: {datasource.get('key_columns_config')}")
            # --- END DEBUG PRINT ---

            # 保存更新后的数据
            save_datasources(current_datasources)
            global DATASOURCES
            DATASOURCES = current_datasources
            flash(f'数据源 "{name}" 更新成功！', 'success')
            return redirect(url_for('admin_datasources'))
    
    return render_template('admin/edit_datasource.html', 
                          datasource=datasource,
                          datasource_id=datasource_id,
                          csv_columns=csv_columns) 

# --- Endpoint to save user habits ---
@app.route('/save_habit', methods=['POST'])
def save_habit():
    user_id = session.get('user_id')
    if not user_id:
        return json.dumps({'success': False, 'message': '用户未登录'}), 401, {'ContentType':'application/json'}

    try:
        habit_data_from_request = request.get_json()
        if not habit_data_from_request:
            return json.dumps({'success': False, 'message': '请求中没有提供习惯数据'}), 400, {'ContentType':'application/json'}

        habit_id = habit_data_from_request.get('id')
        habit_name = habit_data_from_request.get('name')
        habit_description = habit_data_from_request.get('description', '') # Optional
        habit_settings = habit_data_from_request.get('settings', {}) # Should contain xAxis, yAxis etc.

        if not habit_name:
            return json.dumps({'success': False, 'message': '习惯名称不能为空'}), 400, {'ContentType':'application/json'}

        users_data = load_users()
        current_user_data = users_data.get(user_id)

        if not current_user_data:
            # This case should ideally not happen if user_id is in session and valid
            return json.dumps({'success': False, 'message': '找不到用户数据'}), 404, {'ContentType':'application/json'}

        if 'habits' not in current_user_data:
            current_user_data['habits'] = []

        if habit_id: # Editing existing habit
            habit_found = False
            for i, habit in enumerate(current_user_data['habits']):
                if habit.get('id') == habit_id:
                    current_user_data['habits'][i]['name'] = habit_name
                    current_user_data['habits'][i]['description'] = habit_description
                    current_user_data['habits'][i]['settings'] = habit_settings
                    # Update timestamp or other meta if needed
                    current_user_data['habits'][i]['updated_at'] = datetime.now(timezone.utc).isoformat()
                    habit_found = True
                    saved_habit = current_user_data['habits'][i]
                    break
            if not habit_found:
                return json.dumps({'success': False, 'message': '未找到要更新的习惯ID'}), 404, {'ContentType':'application/json'}
        else: # Creating new habit
            habit_id = uuid.uuid4().hex # Generate a new unique ID
            new_habit = {
                'id': habit_id,
                'name': habit_name,
                'description': habit_description,
                'settings': habit_settings,
                'created_at': datetime.now(timezone.utc).isoformat(),
                'updated_at': datetime.now(timezone.utc).isoformat()
            }
            current_user_data['habits'].append(new_habit)
            saved_habit = new_habit
        
        save_users(users_data)
        return json.dumps({'success': True, 'message': '习惯已保存', 'habit': saved_habit}), 200, {'ContentType':'application/json'}

    except Exception as e:
        current_app.logger.error(f"Error saving habit: {e}") # Log the error
        return json.dumps({'success': False, 'message': f'保存习惯时发生服务器错误: {str(e)}'}), 500, {'ContentType':'application/json'} 

# --- Endpoint to delete user habits ---
@app.route('/delete_habit', methods=['POST'])
def delete_habit():
    """删除习惯"""
    try:
        data = request.get_json()
        if not data:
            return json.dumps({'success': False, 'message': '请求中没有提供数据'}), 400, {'ContentType':'application/json'}
        
        habit_id = data.get('habit_id')
        if not habit_id:
            return json.dumps({'success': False, 'message': '缺少习惯ID'}), 400, {'ContentType':'application/json'}
        
        user_id = session.get('user_id')
        if not user_id:
            return json.dumps({'success': False, 'message': '用户未登录'}), 401, {'ContentType':'application/json'}
        
        # 加载用户数据
        users = load_users()
        if user_id not in users:
            return json.dumps({'success': False, 'message': '用户不存在'}), 404, {'ContentType':'application/json'}
        
        user_data = users[user_id]
        habits = user_data.get('habits', [])
        
        # 查找并删除习惯
        habit_found = False
        for i, habit in enumerate(habits):
            if habit.get('id') == habit_id:
                habits.pop(i)
                habit_found = True
                break
        
        if not habit_found:
            return json.dumps({'success': False, 'message': '习惯不存在'}), 404, {'ContentType':'application/json'}
        
        # 保存用户数据
        users[user_id]['habits'] = habits
        save_users(users)
        
        # 更新全局变量
        global TEMP_USERS
        TEMP_USERS = users
        
        return json.dumps({'success': True, 'message': '习惯已成功删除'}), 200, {'ContentType':'application/json'}
        
    except Exception as e:
        current_app.logger.error(f"Error deleting habit: {e}") # 记录错误
        return json.dumps({'success': False, 'message': f'删除习惯时发生服务器错误: {str(e)}'}), 500, {'ContentType':'application/json'} 

# 飞书相关功能已移除
# 飞书相关路由和函数已移除

# ---- 简化的文件上传路由 ----
@app.route('/admin/upload_csv', methods=['POST'])
@admin_required
def admin_upload_csv():
    """简化的CSV文件上传处理"""
    try:
        if 'csv_file' not in request.files:
            return jsonify({'success': False, 'error': '没有选择文件'})

        file = request.files['csv_file']
        if not file or not file.filename:
            return jsonify({'success': False, 'error': '没有选择文件'})

        # 使用简化的上传服务
        from app.services.upload_service import UploadService, SimpleUploadHandler
        upload_service = UploadService(
            upload_folder=current_app.config.get('UPLOAD_FOLDER_CSV', 'uploads/csv'),
            max_file_size=current_app.config.get('MAX_CONTENT_LENGTH', 100 * 1024 * 1024)
        )
        upload_handler = SimpleUploadHandler(upload_service)
        result = upload_handler.handle_upload(file)

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"文件上传错误: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'上传失败: {str(e)}'})

# ---- 移除复杂的分片上传路由 ----
# 以下路由已被简化上传替代：
# - /upload_chunk
# - /cancel_upload
# - /check_upload_status
# 分片上传相关代码已移除，使用简化上传替代
# 分片上传验证和处理代码已移除
# 分片保存和合并逻辑已移除

# finalize_upload 函数已移除

# find_completed_file 函数已移除
# find_completed_file 函数的剩余部分已移除

# merge_chunks 函数已移除
# merge_chunks 函数的剩余部分已移除
# merge_chunks 函数的结尾和 cancel_upload 路由已移除
# cancel_upload 和 check_upload_status 路由已移除
# check_upload_status 路由的剩余部分已移除

@app.route('/admin/get_csv_headers', methods=['POST'])
@admin_required
def get_csv_headers():
    """获取已上传CSV文件的列头信息（简化版）"""
    try:
        data = request.get_json()
        filename = data.get('filename', '').strip()

        if not filename:
            return jsonify({'success': False, 'error': '缺少文件名'})

        upload_dir = current_app.config['UPLOAD_FOLDER_CSV']
        file_path = os.path.join(upload_dir, filename)

        if not os.path.exists(file_path):
            return jsonify({'success': False, 'error': '文件不存在'})

        # 使用上传服务获取CSV表头
        from app.services.upload_service import UploadService
        upload_service = UploadService(
            upload_folder=current_app.config.get('UPLOAD_FOLDER_CSV', 'uploads/csv')
        )
        success, error, headers = upload_service.get_csv_headers(file_path)

        if not success:
            return jsonify({'success': False, 'error': error})

        return jsonify({
            'success': True,
            'headers': headers,
            'message': f'成功获取 {len(headers)} 个列头'
        })
    except Exception as e:
        current_app.logger.error(f"获取CSV列头错误: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'获取列头时发生错误: {str(e)}'})

# smart_price_convert 函数已移除，使用 data_service.parse_price 替代
# 旧的价格转换和筛选函数已移除，使用 data_service 替代
# 旧的临时文件清理代码已移除，分片上传功能已简化

def reorganize_data_for_vertical_layout(grouped_data, x_axis_hierarchy, y_axis_hierarchy):
    """
    为垂直层级布局重新组织数据
    将同一主分类下的所有子分类数据合并
    """
    try:
        reorganized_data = {}

        # 重新组织X轴数据：按主分类分组
        for x_primary in x_axis_hierarchy.keys():
            reorganized_data[x_primary] = {}

            # 重新组织Y轴数据：按主分类分组
            for y_primary in y_axis_hierarchy.keys():
                reorganized_data[x_primary][y_primary] = []

                # 收集该主分类组合下的所有产品
                for x_secondary in x_axis_hierarchy[x_primary]:
                    x_combined = f"{x_primary} | {x_secondary}" if x_secondary else x_primary

                    for y_secondary in y_axis_hierarchy[y_primary]:
                        y_combined = f"{y_primary} | {y_secondary}" if y_secondary else y_primary

                        # 从原始分组数据中获取产品
                        if x_combined in grouped_data and y_combined in grouped_data[x_combined]:
                            products = grouped_data[x_combined][y_combined]
                            reorganized_data[x_primary][y_primary].extend(products)

        return reorganized_data

    except Exception as e:
        current_app.logger.error(f"重新组织数据失败: {str(e)}")
        return grouped_data  # 返回原始数据作为后备

def create_secondary_classification_groups(product_data, x_axis, y_axis, x_secondary, y_secondary):
    """
    创建二级分类分组（保持原有逻辑）
    """
    try:
        grouped_data = {}
        x_axis_hierarchy = {}
        y_axis_hierarchy = {}

        # 构建层次结构
        for item in product_data:
            # X轴层次结构
            x_primary_val = clean_value(item.get(x_axis))
            x_secondary_val = clean_value(item.get(x_secondary)) if x_secondary else None

            # 跳过空的主分类值
            if x_primary_val is None:
                continue

            if x_primary_val not in x_axis_hierarchy:
                x_axis_hierarchy[x_primary_val] = []
            # 只有当X轴副分类存在且有非空值时才添加
            if x_secondary and x_secondary_val is not None and x_secondary_val not in x_axis_hierarchy[x_primary_val]:
                x_axis_hierarchy[x_primary_val].append(x_secondary_val)
            elif not x_secondary:
                # 如果没有选择X轴副分类，确保每个主分类都有一个空占位符
                if '' not in x_axis_hierarchy[x_primary_val]:
                    x_axis_hierarchy[x_primary_val].append('')

            # Y轴层次结构
            y_primary_val = clean_value(item.get(y_axis))
            y_secondary_val = clean_value(item.get(y_secondary)) if y_secondary else None

            # 跳过空的主分类值
            if y_primary_val is None:
                continue

            if y_primary_val not in y_axis_hierarchy:
                y_axis_hierarchy[y_primary_val] = []
            # 只有当Y轴副分类存在且有非空值时才添加
            if y_secondary and y_secondary_val is not None and y_secondary_val not in y_axis_hierarchy[y_primary_val]:
                y_axis_hierarchy[y_primary_val].append(y_secondary_val)
            elif not y_secondary:
                # 如果没有选择Y轴副分类，确保每个主分类都有一个空占位符
                if '' not in y_axis_hierarchy[y_primary_val]:
                    y_axis_hierarchy[y_primary_val].append('')

        # 数据分组 - 在层次结构构建完成后进行
        for item in product_data:
            x_primary_val = clean_value(item.get(x_axis))
            x_secondary_val = clean_value(item.get(x_secondary)) if x_secondary else None
            y_primary_val = clean_value(item.get(y_axis))
            y_secondary_val = clean_value(item.get(y_secondary)) if y_secondary else None

            # 跳过空值产品
            if x_primary_val is None or y_primary_val is None:
                continue

            # 构建组合值
            x_combined = f"{x_primary_val} | {x_secondary_val}" if x_secondary and x_secondary_val is not None else x_primary_val
            y_combined = f"{y_primary_val} | {y_secondary_val}" if y_secondary and y_secondary_val is not None else y_primary_val

            if x_combined not in grouped_data:
                grouped_data[x_combined] = {}
            if y_combined not in grouped_data[x_combined]:
                grouped_data[x_combined][y_combined] = []

            grouped_data[x_combined][y_combined].append(item)

        # 生成轴值列表
        x_axis_values = []
        for x_primary in x_axis_hierarchy.keys():
            for x_secondary_val in x_axis_hierarchy[x_primary]:
                x_combined = f"{x_primary} | {x_secondary_val}" if x_secondary_val and x_secondary_val != '' else x_primary
                x_axis_values.append(x_combined)

        y_axis_values = []
        for y_primary in y_axis_hierarchy.keys():
            for y_secondary_val in y_axis_hierarchy[y_primary]:
                y_combined = f"{y_primary} | {y_secondary_val}" if y_secondary_val and y_secondary_val != '' else y_primary
                y_axis_values.append(y_combined)

        return grouped_data, x_axis_values, y_axis_values, x_axis_hierarchy, y_axis_hierarchy

    except Exception as e:
        current_app.logger.error(f"创建二级分类分组失败: {str(e)}")
        return {}, [], [], {}, {}

# ---- 性能监控路由 ----
@app.route('/admin/performance_metrics')
@admin_required
def performance_metrics():
    """查看性能监控指标"""
    try:
        from app.utils.performance import performance_monitor, MemoryProfiler

        metrics = performance_monitor.get_metrics()
        memory_info = MemoryProfiler.get_memory_usage()

        # 缓存统计
        cache_stats = {
            'cache_size': len(data_service.cache._cache),
            'cache_hits': getattr(data_service.cache, '_hits', 0),
            'cache_misses': getattr(data_service.cache, '_misses', 0)
        }

        return render_template('admin/performance.html',
                             metrics=metrics,
                             memory_info=memory_info,
                             cache_stats=cache_stats)
    except Exception as e:
        flash(f"获取性能指标失败: {str(e)}", "error")
        return redirect(url_for('admin_dashboard'))

@app.route('/admin/clear_cache', methods=['POST'])
@admin_required
def clear_cache():
    """清空缓存"""
    try:
        data_service.cache.clear()
        flash("缓存已清空", "success")
    except Exception as e:
        flash(f"清空缓存失败: {str(e)}", "error")

    return redirect(url_for('performance_metrics'))

# --- 图片代理路由 - 解决跨域问题 --- #

@app.route('/proxy_image')
def proxy_image():
    """图片代理路由 - 将外部图片转换为本地可访问的图片"""
    try:
        # 获取图片URL
        image_url = request.args.get('url')
        if not image_url:
            return Response("Missing image URL", status=400)

        # 验证URL格式
        parsed_url = urlparse(image_url)
        if not parsed_url.scheme or not parsed_url.netloc:
            return Response("Invalid image URL", status=400)

        # 设置请求头，模拟浏览器请求
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Referer': parsed_url.scheme + '://' + parsed_url.netloc + '/'
        }

        # 请求图片，增加重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                response = requests.get(
                    image_url,
                    headers=headers,
                    timeout=15,  # 增加超时时间
                    stream=True,
                    allow_redirects=True,
                    verify=False  # 忽略SSL证书验证
                )
                response.raise_for_status()
                break  # 成功则跳出重试循环
            except requests.exceptions.RequestException as e:
                if attempt == max_retries - 1:  # 最后一次重试
                    raise e
                # 等待后重试
                import time
                time.sleep(0.5 * (attempt + 1))
        # 检查内容类型
        content_type = response.headers.get('Content-Type', 'image/jpeg')
        if not content_type.startswith('image/'):
            # 尝试从URL推断图片类型
            if any(ext in image_url.lower() for ext in ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']):
                content_type = 'image/jpeg'  # 默认为jpeg
            else:
                return Response("Not an image", status=400)

        # 增加文件大小限制（最大10MB）
        max_size = 10 * 1024 * 1024  # 10MB
        content_length = response.headers.get('Content-Length')
        if content_length and int(content_length) > max_size:
            return Response("Image too large", status=413)

        # 读取图片数据
        image_data = b''
        for chunk in response.iter_content(chunk_size=8192):
            image_data += chunk
            if len(image_data) > max_size:
                return Response("Image too large", status=413)

        # 返回图片数据
        return Response(
            image_data,
            mimetype=content_type,
            headers={
                'Cache-Control': 'public, max-age=3600',  # 缓存1小时
                'Access-Control-Allow-Origin': '*',  # 允许跨域
                'Content-Length': str(len(image_data))
            }
        )

    except requests.exceptions.Timeout:
        return Response("Image request timeout", status=408)
    except requests.exceptions.RequestException as e:
        return Response(f"Failed to fetch image: {str(e)}", status=502)
    except Exception as e:
        return Response(f"Server error: {str(e)}", status=500)

