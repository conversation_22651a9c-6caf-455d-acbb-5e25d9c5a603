<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}productview2{% endblock %}</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; background-color: #f0f2f5; color: #333; display: flex; flex-direction: column; min-height: 100vh; }
        .user-navbar {
            background-color: #1a73e8;
            padding: 12px 24px;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.15);
            position: fixed; /* 改为fixed确保始终在顶部 */
            top: 0;
            left: 0;
            right: 0;
            width: 100%;
            z-index: 1050; /* 高于侧边栏和页面内容 */
            box-sizing: border-box; /* 确保padding不会影响宽度计算 */
        }
        .user-navbar .nav-brand a { 
            color: white; 
            text-decoration: none; 
            font-weight: 600; 
            font-size: 1.6em; 
            display: flex;
            align-items: center;
        }
        .user-navbar .nav-brand a:before {
            content: ""; /* 可以用SVG或图片替换 */
            display: inline-block;
            width: 28px;
            height: 28px;
            background-color: white; /* 简单占位 */
            border-radius: 50%;
            margin-right: 10px;
            /* background-image: url('logo.svg'); */ /* 示例 */
            background-size: cover;
        }
        .user-navbar .nav-links {
            display: flex;
            align-items: center;
        }
        .user-navbar .nav-links a, .user-navbar .nav-links span, .user-navbar .nav-links button {
            color: white; 
            text-decoration: none; 
            margin-left: 18px; 
            font-size: 0.95em;
            transition: all 0.2s ease;
        }
        .user-navbar .nav-links a:hover { 
            color: rgba(255, 255, 255, 0.85);
        }
        .user-navbar .nav-links form { 
            display: inline-flex; 
            align-items: center;
            margin-left: 15px; 
        }
        .user-navbar .nav-links label {
            margin-right: 8px;
            font-size: 0.9em;
        }
        .user-navbar .nav-links select {
            padding: 6px 10px;
            border-radius: 6px;
            border: none;
            background-color: rgba(255, 255, 255, 0.15);
            color: white;
            cursor: pointer;
            font-size: 0.9em;
            min-width: 180px;
            transition: all 0.3s ease;
        }
        .user-navbar .nav-links select:hover {
            background-color: rgba(255, 255, 255, 0.25);
        }
        .user-navbar .nav-links select option {
            background-color: #fff;
            color: #333;
        }
        .user-navbar .nav-links button { 
            background-color: #0069d9; 
            border-color: #0062cc; 
            padding: 6px 12px;
            border-radius: 4px;
            font-weight: 500;
        }
        .user-navbar .nav-links .welcome-text {
            display: flex;
            align-items: center;
        }
        .user-navbar .nav-links .welcome-text:before {
            content: "👤"; /* 更换为SVG图标效果更佳 */
            margin-right: 5px;
            font-size: 1.1em;
        }
        .user-navbar .nav-links .admin-link {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: 500;
        }
        .user-navbar .nav-links .admin-link:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }
        .user-navbar .nav-links .logout-link {
            color: rgba(255, 255, 255, 0.9);
        }
        
        /* 数据源和轴选择器的特定样式 */
        .datasource-axis-container {
            background-color: rgba(255, 255, 255, 0.15);
            border-radius: 10px;
            padding: 10px 15px;
            margin-right: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .datasource-section, .axis-section {
            display: inline-flex;
            align-items: center;
            padding: 0 15px;
        }
        
        .datasource-section {
            padding-left: 0;
            border-right: 1px solid rgba(255, 255, 255, 0.4);
            margin-right: 15px;
        }
        
        .axis-section {
            padding-right: 0;
        }
        
        .datasource-axis-container form {
            margin: 0 !important;
        }
        
        .datasource-axis-container select {
            background-color: rgba(255, 255, 255, 0.25) !important;
            border: 1px solid rgba(255, 255, 255, 0.4) !important;
            color: white !important;
            font-size: 13px !important;
            border-radius: 6px !important;
            padding: 6px 10px !important;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        .datasource-axis-container select:hover {
            background-color: rgba(255, 255, 255, 0.35) !important;
            border-color: rgba(255, 255, 255, 0.5) !important;
            transform: translateY(-1px);
        }
        
        .datasource-axis-container select:focus {
            outline: none;
            background-color: rgba(255, 255, 255, 0.4) !important;
            border-color: rgba(255, 255, 255, 0.6) !important;
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
        }
        
        .datasource-axis-container label {
            font-size: 13px !important;
            font-weight: 600;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            white-space: nowrap;
        }
        
        /* 响应式调整 */
        @media (max-width: 1200px) {
            .datasource-axis-container {
                flex-direction: column;
                gap: 8px !important;
            }
            
            .datasource-axis-container select {
                min-width: 80px !important;
            }
        }
        
        @media (max-width: 900px) {
            .user-navbar .nav-links {
                flex-wrap: wrap;
                gap: 8px;
            }
            
            .datasource-axis-container {
                order: -1; /* 将数据源和轴选择器移到第一行 */
                width: 100%;
                justify-content: flex-start;
            }
        }

        /* 页面主容器 */
        .page-wrapper {
            display: flex;
            flex-grow: 1; /* 使其填满导航栏下方的剩余空间 */
            margin-top: 70px; /* 为固定导航栏留出空间 */
            position: relative; /* 添加相对定位，作为侧边栏的定位参考 */
        }

        /* 左侧边栏样式 */
        .sidebar {
            width: 280px;
            background-color: #fff;
            padding: 20px;
            border-right: 1px solid #e0e0e0;
            box-shadow: 2px 0 5px rgba(0,0,0,0.05);
            overflow-y: auto;
            height: calc(100vh - 70px);
            position: fixed;
            top: 70px;
            left: 0;
            z-index: 100;
            transition: left 0.3s ease;
            background-image: linear-gradient(to bottom, #fcfcfc, #ffffff);
        }



        /* 确保侧边栏在小屏幕上也能正确显示 */
        @media (max-width: 768px) {
            .sidebar {
                width: 260px; /* 在小屏幕上稍微缩小宽度 */
                height: calc(100vh - 80px); /* 小屏幕上导航栏可能更高 */
                top: 80px;
            }

            .sidebar.collapsed {
                left: -300px; /* 调整收起位置 */
            }

            .main-content-wrapper {
                margin-left: 300px; /* 调整内容区域边距 */
                width: calc(100% - 300px);
            }

            .sidebar.collapsed ~ .main-content-wrapper {
                margin-left: 0;
                width: 100%;
            }
        }
        
        /* 主内容区域 - 重写 */
        .main-content-wrapper {
            flex-grow: 1;
            padding: 20px;
            background-color: #f0f2f5;
            margin-left: 320px; /* 等于侧边栏宽度+padding */
            transition: margin-left 0.3s ease;
            width: calc(100% - 320px); /* 确保宽度计算正确 */
        }
        
        /* 侧边栏收起时的内容区域 */
        .sidebar.collapsed ~ .main-content-wrapper {
            margin-left: 0;
            width: 100%;
        }

        /* 原来的user-container，现在作为主内容的内层容器 */
        .user-container { /* 原来的user-container，现在作为主内容的内层容器 */
            margin: 0 auto; /* 移除原有的上下边距，由main-content-wrapper控制 */
            padding: 0; /* 移除原有的padding，由main-content-wrapper控制 */
            background-color: #fff; 
            border-radius: 12px; 
            box-shadow: 0 0 20px rgba(0,0,0,0.08); 
            width: 100%; /* 宽度由父级控制 */
            max-width: none; /* 移除最大宽度限制，使其填满 */
        }
        .content-area { margin-top: 0; padding: 20px; } /* Removed top margin, padding for content */

        /* Flash messages 和 Modal Styles 保持不变 */
        .flash-messages { list-style: none; padding: 0; margin: 0 0 20px 0; }
        .flash-messages li { padding: 12px; border-radius: 6px; margin-bottom: 12px; text-align: center; }
        .flash-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .flash-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .flash-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .modal-overlay {
            display: none; 
            position: fixed; 
            z-index: 1060; 
            left: 0;
            top: 0;
            width: 100%; 
            height: 100%; 
            overflow: auto; 
            background-color: rgba(0,0,0,0.5); 
            align-items: center; /* Vertically center content */
            justify-content: center; /* Horizontally center content */
        }
        .modal-overlay.active {
            display: flex; /* Use flex to center content */
        }
        .modal-content {
            background-color: #fefefe;
            margin: auto; /* Auto margins with flex centering */
            padding: 25px;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
            position: relative;
            display: flex; 
            flex-direction: column;
            width: 90%;
            max-height: 90vh; /* Max height for scroll */
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            margin-bottom: 20px;
        }
        .modal-header h4 {
            margin: 0;
            font-size: 1.3em;
            color: #333; 
        }
        .modal-close-btn {
            background: none;
            border: none;
            font-size: 1.8em;
            font-weight: bold;
            color: #888;
            cursor: pointer;
            padding: 0 5px;
            line-height: 1;
        }
        .modal-close-btn:hover { color: #333; }
        .modal-body {
            flex-grow: 1; 
            overflow-y: auto; /* Scroll if content exceeds max-height */
            margin-bottom: 20px;
        }
        .modal-body p { word-wrap: break-word; white-space: pre-wrap; }
        .modal-footer {
            text-align: right; /* Align buttons to the right */
            padding-top: 20px;
            border-top: 1px solid #eee; 
        }
        .modal-footer button {
            padding: 10px 18px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.95em;
            margin-left: 10px;
            transition: background-color 0.2s ease, border-color 0.2s ease;
        }
        .modal-footer .btn-modal-primary {
            background-color: #007bff;
            color: white;
            border: 1px solid #007bff;
        }
        .modal-footer .btn-modal-primary:hover { background-color: #0056b3; border-color: #0056b3; }
        .modal-footer .btn-modal-secondary {
            background-color: #6c757d;
            color: white;
            border: 1px solid #6c757d;
        }
        .modal-footer .btn-modal-secondary:hover { background-color: #545b62; border-color: #545b62; }

        /* Habit Configuration Modal Specifics */
        #habitConfigModal .modal-content { max-width: 600px; }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            font-size: 0.9em;
            color: #444;
        }
        .form-group input[type="text"],
        .form-group textarea {
            width: calc(100% - 20px); /* Full width minus padding */
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.95em;
        }
        .form-group textarea {
            min-height: 80px;
            resize: vertical;
        }
        .form-group select.form-control { /* Added for select styling */
            width: calc(100% - 20px); /* Full width minus padding */
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 0.95em;
            background-color: white;
            color: #333;
        }
        .config-section-title {
            font-weight: 600;
            margin-top: 20px;
            margin-bottom: 10px;
            font-size: 1em;
            color: #333;
            padding-bottom: 5px;
            border-bottom: 1px dashed #ddd;
        }

        /* 侧边栏切换按钮 */
        .sidebar-toggle-button {
            background-color: #0069d9;
            color: white;
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 1em;
            border-radius: 6px;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .sidebar-toggle-button:hover {
            background-color: #0059c9;
            transform: translateY(-1px);
            box-shadow: 0 3px 6px rgba(0,0,0,0.15);
        }
        .sidebar-toggle-button:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }



        /* 侧边栏收起状态 */
        .sidebar.collapsed {
            left: -320px; /* 完全移出视图 (宽度+padding) */
        }

        /* 侧边栏内部组件样式 */
        .sidebar-section {
            margin-bottom: 25px;
            background-color: #fafafa;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }
        
        .sidebar-section:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .sidebar-section h4 {
            font-size: 1.1em;
            color: #1a73e8;
            margin-top: 0;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        /* 为每个区块添加图标 */
        .sidebar-section h4:before {
            content: "";
            display: inline-block;
            width: 18px;
            height: 18px;
            margin-right: 8px;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
        }
        
        .sidebar-section.filter h4:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%231a73e8' viewBox='0 0 16 16'%3E%3Cpath d='M1.5 1.5A.5.5 0 0 1 2 1h12a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.128.334L10 8.692V13.5a.5.5 0 0 1-.342.474l-3 1A.5.5 0 0 1 6 14.5V8.692L1.628 3.834A.5.5 0 0 1 1.5 3.5v-2z'/%3E%3C/svg%3E");
        }
        
        .sidebar-section.habits h4:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%231a73e8' viewBox='0 0 16 16'%3E%3Cpath d='M9.828 4a3 3 0 0 1-2.12-.879l-.83-.828A1 1 0 0 0 6.173 2H2.5a1 1 0 0 0-1 .981L1.546 4h-1L.5 3a2 2 0 0 1 2-2h3.672a2 2 0 0 1 1.414.586l.828.828A2 2 0 0 0 9.828 3v1z'/%3E%3Cpath fill-rule='evenodd' d='M13.81 4H2.19a1 1 0 0 0-.996 1.09l.637 7a1 1 0 0 0 .995.91h10.348a1 1 0 0 0 .995-.91l.637-7A1 1 0 0 0 13.81 4zM2.19 3A2 2 0 0 0 .198 5.181l.637 7A2 2 0 0 0 2.826 14h10.348a2 2 0 0 0 1.991-1.819l.637-7A2 2 0 0 0 13.81 3H2.19z'/%3E%3C/svg%3E");
        }
        
        .sidebar-section.tags h4:before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%231a73e8' viewBox='0 0 16 16'%3E%3Cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM2 2a1 1 0 0 0-1 1v11a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V3a1 1 0 0 0-1-1H2z'/%3E%3Cpath d='M2.5 4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 .5.5v1a.5.5 0 0 1-.5.5H3a.5.5 0 0 1-.5-.5V4z'/%3E%3C/svg%3E");
        }

        /* 已保存习惯列表 */
        .saved-habits-list {
            list-style: none;
            padding: 0;
            margin-top: 10px;
        }
        .saved-habits-list li {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            margin-bottom: 8px;
            background-color: #f1f5f9;
            border-radius: 6px;
            border-left: 3px solid #4285f4;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #333;
        }
        .habit-info {
            flex: 1;
            overflow: hidden;
        }
        
        .habit-name {
            font-weight: 500;
            margin-bottom: 2px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .habit-meta {
            font-size: 0.8em;
            color: #666;
        }
        
        .habit-actions {
            display: flex;
            opacity: 0.6;
            transition: opacity 0.2s;
        }
        
        .saved-habits-list li:hover .habit-actions {
            opacity: 1;
        }
        
        .habit-delete-btn {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s;
            color: #666;
            font-size: 14px;
            margin-left: 5px;
        }
        
        .habit-delete-btn:hover {
            background-color: #ff5252;
            color: white;
        }
        
        .create-habit-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #e8f0fe;
            color: #1a73e8;
            border: 1px dashed #bed4ff;
            border-radius: 6px;
            padding: 10px;
            font-size: 0.9em;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .create-habit-btn:hover {
            background-color: #d4e6ff;
        }
        
        .create-habit-btn svg {
            margin-right: 8px;
            font-size: 1.1em;
        }
        
        /* 删除习惯按钮 */
        .habit-delete-btn {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin-left: 8px;
            transition: all 0.2s;
            opacity: 0.6;
        }
        
        .habit-delete-btn:hover {
            background-color: #ff5252;
            opacity: 1;
            color: white;
            transform: scale(1.1);
        }
        
        /* 确认删除模态框 */
        #deleteHabitModal .modal-content {
            max-width: 400px;
        }
        
        /* 空状态提示 */
        .empty-state {
            text-align: center;
            padding: 15px;
            color: #888;
            font-size: 0.9em;
            background: #f9f9f9;
            border-radius: 6px;
            border: 1px dashed #ddd;
        }
        .empty-state svg {
            display: block;
            margin: 0 auto 10px;
            fill: #aaa;
            width: 30px;
            height: 30px;
        }

        /* 筛选器样式 */
        .filter-controls {
            margin-top: 10px;
        }
        
        .filter-group {
            margin-bottom: 15px;
        }
        
        .filter-group label {
            display: block;
            margin-bottom: 5px;
            font-size: 0.9em;
            color: #555;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .filter-group select, 
        .filter-group input {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            background-color: white;
            transition: border-color 0.2s ease;
        }
        
        .filter-group select:focus,
        .filter-group input:focus {
            border-color: #1a73e8;
            outline: none;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }
        
        .price-range {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .price-range input {
            flex: 1;
        }
        
        .range-separator {
            color: #666;
            padding: 0 5px;
        }
        
        /* 新的筛选器样式 */
        .compact-filter-area {
            background: #f5f9ff;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        }
        
        .filter-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #e0e0e0;
            padding-bottom: 8px;
        }
        
        .filter-header h5 {
            margin: 0;
            font-size: 1em;
            color: #333;
            font-weight: 500;
        }
        
        .add-filter-btn {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 0.85em;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .add-filter-btn:hover {
            background-color: #0d62cb;
        }
        
        .active-filters {
            margin-bottom: 10px;
        }
        
        /* 筛选标签样式优化 */
        .filter-tag {
            display: inline-flex;
            align-items: center;
            background-color: #e8f0fe;
            color: #174ea6;
            padding: 4px 12px;
            border-radius: 16px;
            margin-right: 8px;
            margin-bottom: 8px;
            font-size: 0.85em;
            border: 1px solid #d0e0ff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: all 0.2s ease;
        }
        
        .filter-tag:hover {
            background-color: #d4e6ff;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }
        
        .filter-text {
            cursor: pointer;
            padding-right: 5px;
            position: relative;
        }
        
        .filter-text:hover {
            text-decoration: underline;
        }
        
        .filter-text:after {
            content: "✎";
            font-size: 0.8em;
            opacity: 0;
            margin-left: 3px;
            transition: opacity 0.2s ease;
        }
        
        .filter-text:hover:after {
            opacity: 0.8;
        }
        
        .remove-filter {
            margin-left: 5px;
            cursor: pointer;
            color: #5f6368;
            font-weight: bold;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background-color: rgba(255,255,255,0.5);
            transition: all 0.2s ease;
        }
        
        .remove-filter:hover {
            color: #d93025;
            background-color: rgba(255,255,255,0.9);
        }
        
        .new-filter-form {
            display: flex;
            flex-direction: column;
            gap: 8px;
            width: 100%;
        }
        
        .new-filter-form .filter-column {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.85em;
        }
        
        .add-this-filter {
            align-self: flex-end;
            margin-top: 5px;
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.85em;
            cursor: pointer;
        }
        
        .add-this-filter:hover {
            background-color: #0d62cb;
        }
        
        .filter-value-container {
            flex: 2;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .filter-value-container.range {
            display: flex;
            align-items: center;
            width: 100%;
        }
        
        .filter-value-container.range input {
            width: calc(50% - 10px);
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.85em;
        }
        
        .filter-value-container.multi-select {
            flex-direction: column;
            width: 100%;
            gap: 5px;
        }
        
        /* 修改多选框布局成多列 */
        .multi-select-options {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            display: flex;
            flex-wrap: wrap;
            align-content: flex-start;
            position: relative;
            background-color: white;
        }
        
        /* 添加底部渐变阴影提示可滚动 */
        .multi-select-options:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,0.9));
            pointer-events: none;
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
            opacity: 1;
            transition: opacity 0.3s ease;
        }
        
        /* 已滚动状态下隐藏渐变阴影 */
        .multi-select-options.scrolled:after {
            opacity: 0;
        }
        
        /* 添加滚动提示图标 */
        .multi-select-options:before {
            content: "↓";
            position: absolute;
            bottom: 5px;
            right: 10px;
            color: #1a73e8;
            font-size: 14px;
            z-index: 10;
            animation: bounce 1.5s infinite;
            pointer-events: none;
            opacity: 1;
            transition: opacity 0.3s ease;
        }
        
        /* 已滚动状态下隐藏箭头 */
        .multi-select-options.scrolled:before {
            opacity: 0;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-3px); }
        }
        
        .multi-select-option {
            display: flex;
            align-items: center;
            padding: 3px 5px;
            min-width: 120px;
            width: calc(50% - 10px); /* 每行显示两列 */
            box-sizing: border-box;
        }
        
        /* 确保复选框和标签正确对齐 */
        .multi-select-option input[type="checkbox"] {
            margin-right: 5px;
            flex-shrink: 0;
        }
        
        .multi-select-option label {
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            cursor: pointer;
            font-weight: normal;
            font-size: 0.9em;
            color: #333;
        }
        
        /* 控制多选和分类筛选框的整体宽度 */
        .filter-value-container.multi-select {
            flex-direction: column;
            width: 100%;
            gap: 5px;
        }
        
        /* 全选/反选按钮样式优化 */
        .multi-select-actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-bottom: 5px;
            font-size: 0.85em;
        }
        
        .multi-select-actions span {
            cursor: pointer;
            color: #1a73e8;
            transition: color 0.2s ease;
        }
        
        .multi-select-actions span:hover {
            color: #0d62cb;
            text-decoration: underline;
        }
        
        .active-filter-summary {
            font-size: 0.9em;
            color: #666;
            margin: 10px 0;
            font-style: italic;
        }
        
        .filter-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        
        .filter-section-title {
            font-weight: 600;
            font-size: 0.95em;
            margin: 15px 0 10px;
            color: #333;
            padding-bottom: 5px;
            border-bottom: 1px dashed #ddd;
        }
        
        .btn-primary {
            background-color: #1a73e8;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }
        
        .btn-primary:hover {
            background-color: #0d62cb;
        }
        
        .btn-secondary {
            background-color: #f8f9fa;
            color: #3c4043;
            border: 1px solid #dadce0;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }
        
        .btn-secondary:hover {
            background-color: #f1f3f4;
            border-color: #d2d5d9;
        }
        
        /* 侧边栏内部标题 */
        .sidebar-section h5 {
            color: #444;
            font-size: 0.95em;
            margin-top: 20px;
            margin-bottom: 10px;
            padding-left: 5px;
            border-left: 3px solid #1a73e8;
        }

        /* 复选框样式美化 */
        .filter-checkbox-list {
            max-height: 180px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px;
            background-color: white;
            margin-top: 5px;
        }
        
        .filter-checkbox-item {
            display: flex;
            align-items: center;
            padding: 3px 5px;
            margin-bottom: 2px;
            font-size: 0.9em;
        }
        
        .filter-checkbox-item:hover {
            background-color: #f0f6ff;
        }
        
        .filter-checkbox-item input[type="checkbox"] {
            width: auto;
            margin-right: 8px;
        }
        
        .filter-checkbox-item label {
            margin-bottom: 0;
            font-weight: normal;
            cursor: pointer;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 新筛选表单样式 - 已合并到下方 */
        
        .form-header {
            border-bottom: 1px solid #e0d2d2;
            margin-bottom: 12px;
            padding-bottom: 6px;
        }
        
        .form-header h5 {
            margin: 0;
            color: #1a73e8;
            font-size: 0.95em;
            padding-left: 0;
            border-left: none;
        }
        
        .form-group {
            margin-bottom: 12px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-size: 0.85em;
            color: #555;
        }
        
        .form-actions {
            display: flex;
            justify-content: flex-end;
            gap: 8px;
            margin-top: 10px;
        }
        
        .cancel-filter {
            background-color: #f8f9fa;
            color: #3c4043;
            border: 1px solid #dadce0;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.85em;
            cursor: pointer;
        }
        
        .cancel-filter:hover {
            background-color: #f1f3f4;
            border-color: #d2d5d9;
        }

        /* 筛选条件编辑和动画效果 */
        .new-filter-form.highlight-edit {
            background-color: #f4f9ff;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.3);
            animation: pulse 1s;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(26, 115, 232, 0.7); }
            70% { box-shadow: 0 0 0 5px rgba(26, 115, 232, 0); }
            100% { box-shadow: 0 0 0 0 rgba(26, 115, 232, 0); }
        }

        .filter-tag.removing {
            background-color: #ffecee;
            border-color: #ffccd2;
            transform: scale(0.95);
            opacity: 0.7;
            animation: fadeOut 0.3s forwards;
        }

        @keyframes fadeOut {
            from { transform: scale(1); opacity: 1; }
            to { transform: scale(0.9); opacity: 0; }
        }

        /* 多选框筛选项优化 */
        .multi-select-actions {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .multi-select-actions span {
            color: #1a73e8;
            font-size: 0.85em;
            cursor: pointer;
            transition: color 0.2s;
            border-bottom: 1px dotted #1a73e8;
        }

        .multi-select-actions span:hover {
            color: #0d62cb;
        }

        /* 向筛选表单添加过渡动画 - 修复显示问题 */
        .new-filter-form {
            background-color: #f5f9ff;
            border: 1px solid #d0e0ff;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .new-filter-form[style*="display: none"] {
            display: none !important;
        }

        /* 标签配置样式 */
        .tag-config-container {
            padding: 5px 0;
        }

        .tag-config-description {
            font-size: 0.85em;
            color: #666;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .tag-config-item {
            background-color: #f5f8ff;
            border: 1px solid #e0e8ff;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.05);
        }

        .tag-config-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .tag-switch {
            position: relative;
            display: inline-block;
            width: 34px;
            height: 18px;
        }

        .tag-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .tag-switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .3s;
            border-radius: 18px;
        }

        .tag-switch-slider:before {
            position: absolute;
            content: "";
            height: 14px;
            width: 14px;
            left: 2px;
            bottom: 2px;
            background-color: white;
            transition: .3s;
            border-radius: 50%;
        }

        .tag-switch input:checked + .tag-switch-slider {
            background-color: #1a73e8;
        }

        .tag-switch input:checked + .tag-switch-slider:before {
            transform: translateX(16px);
        }

        .tag-config-field {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .tag-config-field-label {
            width: 70px;
            font-size: 0.9em;
            font-weight: 500;
            color: #333;
        }

        .tag-config-field-input {
            flex: 1;
            position: relative;
        }

        .tag-config-select {
            width: 100%;
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            background-color: white;
        }

        .tag-color-input {
            width: 70px;
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
        }

        .tag-position-btn {
            width: 25px;
            height: 25px;
            border: 1px solid #ddd;
            background-color: white;
            border-radius: 3px;
            margin-right: 5px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8em;
        }

        .tag-position-btn.active {
            background-color: #e6f0ff;
            border-color: #1a73e8;
            color: #1a73e8;
            font-weight: bold;
        }

        .tag-remove-btn {
            color: #ff4d4f;
            background: none;
            border: none;
            font-size: 0.85em;
            cursor: pointer;
            padding: 2px 6px;
            border-radius: 3px;
            margin-top: 5px;
        }

        .tag-remove-btn:hover {
            background-color: #fff1f0;
        }

        .add-tag-btn {
            width: 100%;
            padding: 8px 0;
            background-color: #f0f6ff;
            border: 1px dashed #bed4ff;
            border-radius: 6px;
            color: #1a73e8;
            font-size: 0.9em;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .add-tag-btn:hover {
            background-color: #e1edff;
        }

        .add-tag-btn .add-icon {
            margin-right: 5px;
            font-weight: bold;
        }

        /* 产品卡片标签样式 - 只保留标签相关的样式 */
        .product-card {
            position: relative; /* 保留相对定位，用于角标定位 */
            /* 移除 overflow: visible，让index.html中的样式控制 */
        }

        .product-tag {
            position: absolute;
            padding: 2px 8px;
            font-size: 0.75em;
            color: white;
            border-radius: 3px;
            white-space: nowrap;
            z-index: 5;
            box-shadow: 0 2px 4px rgba(0,0,0,0.15);
        }

        .product-tag.left-top {
            top: 10px;
            left: -5px;
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .product-tag.right-top {
            top: 10px;
            right: -5px;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        /* 底部标签样式 */
        .product-tags-bottom {
            padding: 6px 12px; /* 减少padding，让标签区域更紧凑 */
            border-top: 1px solid #f0f0f0;
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            min-height: 0; /* 移除最小高度，让空标签容器不占用空间 */
            /* 移除max-height和overflow限制，让标签完全显示 */
            flex-shrink: 0; /* 防止被压缩 */
            box-sizing: border-box; /* 确保padding包含在高度内 */
        }
        
        /* 当标签容器为空时，隐藏边框和padding */
        .product-tags-bottom:empty {
            border-top: none;
            padding: 0;
            min-height: 0;
        }
        
        .product-tag.bottom {
            position: static;
            display: inline-block;
            padding: 2px 6px;
            font-size: 0.65em; /* 稍微减小字体大小，适应卡片 */
            color: white;
            border-radius: 8px; /* 减小圆角，适应小尺寸 */
            white-space: nowrap;
            box-shadow: 0 1px 2px rgba(0,0,0,0.15); /* 减小阴影 */
            margin: 0;
            line-height: 1.3;
            max-width: 100%; /* 确保不超出容器宽度 */
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 为产品卡片上的角标添加三角形装饰 */
        .product-tag.left-top:before {
            content: "";
            position: absolute;
            left: 0;
            top: -5px;
            width: 0;
            height: 0;
            border-bottom: 5px solid rgba(0,0,0,0.3);
            border-left: 5px solid transparent;
        }

        .product-tag.right-top:before {
            content: "";
            position: absolute;
            right: 0;
            top: -5px;
            width: 0;
            height: 0;
            border-bottom: 5px solid rgba(0,0,0,0.3);
            border-right: 5px solid transparent;
        }

        /* 底部标签不需要三角形装饰 */
        .product-tag.bottom:before {
            display: none;
        }

        .tag-config-input {
            width: 100%;
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            transition: border-color 0.2s;
        }

        .tag-config-input:focus {
            border-color: #1a73e8;
            outline: none;
            box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
        }

        .custom-text-field {
            background-color: #f9f9ff;
            padding: 8px;
            border-radius: 4px;
            margin-top: 5px;
            border-left: 3px solid #1a73e8;
        }

        /* 显示模式提示信息 */
        .display-mode-info {
            margin-top: 5px;
            font-size: 0.8em;
            color: #666;
            font-style: italic;
        }

        /* 颜色选择器样式 */
        .color-select {
            width: 100%;
            padding: 5px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 0.9em;
            background-color: white;
        }

        .color-select option {
            padding: 8px 4px;
            text-indent: 5px;
            position: relative;
        }

        /* 颜色预览 */
        .color-preview {
            display: inline-block;
            width: 14px;
            height: 14px;
            border-radius: 50%;
            margin-right: 8px;
            vertical-align: middle;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }

        /* 字体大小设置样式 */
        .setting-group {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .setting-group h5 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #495057;
            font-size: 0.95em;
            font-weight: 600;
            border-left: 3px solid #28a745;
            padding-left: 8px;
        }

        .font-size-controls {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .font-size-item {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .font-size-item label {
            font-size: 0.9em;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 0;
        }

        .font-size-input-group {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .font-size-input-group input[type="range"] {
            flex: 1;
            height: 6px;
            background: #ddd;
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        .font-size-input-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            background: #28a745;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .font-size-input-group input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: #28a745;
            border-radius: 50%;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .font-size-input-group span {
            min-width: 40px;
            font-size: 0.85em;
            color: #495057;
            font-weight: 500;
            text-align: center;
        }

        .reset-font-size-btn {
            background-color: #6c757d;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-size: 0.85em;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 10px;
        }

        .reset-font-size-btn:hover {
            background-color: #5a6268;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* 当前选中的颜色显示 */
        .current-color {
            display: flex;
            align-items: center;
            margin-top: 5px;
            font-size: 0.85em;
            color: #666;
        }

        /* 规则帮助样式 */
        .rules-helper {
            margin-top: 8px;
            font-size: 0.85em;
        }

        .rules-toggle {
            color: #1a73e8;
            cursor: pointer;
            display: inline-block;
            padding: 3px 0;
            user-select: none;
        }

        .rules-toggle:hover {
            text-decoration: underline;
        }

        .toggle-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            text-align: center;
            line-height: 16px;
            font-weight: bold;
        }

        .rules-examples {
            background-color: #f5f8ff;
            border: 1px solid #e0e8ff;
            border-radius: 4px;
            padding: 8px;
            margin-top: 5px;
        }

        .rule-example {
            margin-bottom: 6px;
            line-height: 1.4;
        }

        .rule-example:last-child {
            margin-bottom: 0;
        }

        .rule-example code {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            color: #d32f2f;
        }

        /* 添加规则示例分隔线样式 */
        .rule-divider {
            height: 1px;
            background: linear-gradient(to right, transparent, rgba(26, 115, 232, 0.3), transparent);
            margin: 8px 0;
        }

        .rules-examples {
            background-color: #f5f8ff;
            border: 1px solid #e0e8ff;
            border-radius: 4px;
            padding: 10px;
            margin-top: 8px;
            font-size: 13px;
        }

        .rule-example {
            margin-bottom: 6px;
            line-height: 1.5;
            color: #444;
        }

        .rule-example code {
            background-color: rgba(0, 0, 0, 0.05);
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
            color: #d32f2f;
            word-break: break-all;
        }

        /* 全屏按钮不显示后缀文字 */
        #fullscreenBtn::after {
            display: none;
        }
        


        /* 全屏模式样式 */
        body.fullscreen-mode {
            overflow: hidden;
        }

        body.fullscreen-mode .user-navbar {
            display: none;
        }

        body.fullscreen-mode .sidebar {
            display: none;
        }

        body.fullscreen-mode .main-content-wrapper {
            margin-left: 0;
            width: 100%;
            height: 100vh;
            overflow: auto;
        }

        /* 全屏模式下的退出按钮 */
        .fullscreen-exit-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.3s ease;
            display: none;
        }

        body.fullscreen-mode .fullscreen-exit-btn {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .fullscreen-exit-btn:hover {
            background-color: rgba(0, 0, 0, 0.9);
            transform: scale(1.1);
        }


        /* 调试样式 - 临时添加，用于验证侧边栏位置 */
        .sidebar-debug {
            position: relative;
        }

        .sidebar-debug::before {
            content: "侧边栏已固定";
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
    {% block styles %}{% endblock %}
</head>
<body>
    <nav class="user-navbar">
        <div class="nav-brand">

            <a href="{{ url_for('index') }}">ProductView2</a>
        </div>
        <div class="nav-links">
            {% if session.get('user_id') %}
                <!-- 数据源和轴选择的水平布局容器 -->
                <div class="datasource-axis-container" style="display: inline-flex; align-items: center; gap: 0;">
                    <!-- 数据源选择区域 -->
                    <div class="datasource-section">
                        <form method="GET" action="{{ url_for('index') }}" style="display: inline-flex; align-items: center; gap: 8px;"> 
                            <label for="selected_datasource_id">数据源:</label>
                            <select name="selected_datasource_id" id="selected_datasource_id" onchange="this.form.submit()">
                                <option value="" {% if not selected_datasource_id %}selected{% endif %}>-- 请选择 --</option>
                                {% for ds_id, ds_data in available_datasources.items() %}
                                    <option value="{{ ds_id }}" {% if selected_datasource_id == ds_id %}selected{% endif %}>
                                        {{ ds_data.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </form>
                    </div>
                    
                    <!-- X/Y轴选择区域（支持二级分类）-->
                    {% if selected_datasource_id and (product_data or csv_headers) %}
                    <div class="axis-section">
                        <form method="GET" action="{{ url_for('index') }}" style="display: inline-flex; align-items: center; gap: 15px;">
                            <input type="hidden" name="selected_datasource_id" value="{{ selected_datasource_id }}">

                            <!-- X轴主分类 -->
                            <div style="display: inline-flex; align-items: center; gap: 6px;">
                                <label for="selected_x_axis">X轴主:</label>
                                <select name="selected_x_axis" id="selected_x_axis" onchange="this.form.submit()">
                                    <option value="">-- 选择 --</option>
                                    {% for header in csv_headers %}
                                        <option value="{{ header }}" {% if selected_x_axis == header %}selected{% endif %}>{{ header }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- X轴二级分类 -->
                            <div style="display: inline-flex; align-items: center; gap: 6px;">
                                <label for="selected_x_axis_secondary">X轴副:</label>
                                <select name="selected_x_axis_secondary" id="selected_x_axis_secondary" onchange="this.form.submit()">
                                    <option value="">-- 可选 --</option>
                                    {% for header in csv_headers %}
                                        <option value="{{ header }}" {% if selected_x_axis_secondary == header %}selected{% endif %}>{{ header }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Y轴主分类 -->
                            <div style="display: inline-flex; align-items: center; gap: 6px;">
                                <label for="selected_y_axis">Y轴主:</label>
                                <select name="selected_y_axis" id="selected_y_axis" onchange="this.form.submit()">
                                    <option value="">-- 选择 --</option>
                                    {% for header in csv_headers %}
                                        <option value="{{ header }}" {% if selected_y_axis == header %}selected{% endif %}>{{ header }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Y轴二级分类 -->
                            <div style="display: inline-flex; align-items: center; gap: 6px;">
                                <label for="selected_y_axis_secondary">Y轴副:</label>
                                <select name="selected_y_axis_secondary" id="selected_y_axis_secondary" onchange="this.form.submit()">
                                    <option value="">-- 可选 --</option>
                                    {% for header in csv_headers %}
                                        <option value="{{ header }}" {% if selected_y_axis_secondary == header %}selected{% endif %}>{{ header }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </form>
                    </div>
                    {% endif %}
                </div>
                
                <!-- 侧边栏切换按钮挪到这里，更容易发现 -->
                <button id="sidebarToggleBtn" class="sidebar-toggle-button" title="收起/展开分析面板">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M2.5 12a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm0-4a.5.5 0 0 1 .5-.5h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5z"/>
                    </svg>
                </button>



                <!-- 全屏按钮 -->
                <button id="fullscreenBtn" class="sidebar-toggle-button" title="进入/退出全屏">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M1.5 1a.5.5 0 0 0-.5.5v4a.5.5 0 0 1-1 0v-4A1.5 1.5 0 0 1 1.5 0h4a.5.5 0 0 1 0 1h-4zM10 .5a.5.5 0 0 1 .5-.5h4A1.5 1.5 0 0 1 16 1.5v4a.5.5 0 0 1-1 0v-4a.5.5 0 0 0-.5-.5h-4a.5.5 0 0 1-.5-.5zM.5 10a.5.5 0 0 1 .5.5v4a.5.5 0 0 0 .5.5h4a.5.5 0 0 1 0 1h-4A1.5 1.5 0 0 1 0 14.5v-4a.5.5 0 0 1 .5-.5zm15 0a.5.5 0 0 1 .5.5v4a1.5 1.5 0 0 1-1.5 1.5h-4a.5.5 0 0 1 0-1h4a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 1 .5-.5z"/>
                    </svg>
                </button>

                <!-- 导出画布按钮 -->
                <button id="exportCanvasBtn" class="sidebar-toggle-button" title="导出完整产品画布为图片">
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                        <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                    </svg>
                </button>
                

                

                <span class="welcome-text">{{ session.get('user_name') }}</span>
                {% if session.get('user_role') == 'admin' %}
                    <a href="{{ url_for('admin_dashboard') }}" class="admin-link">管理后台</a>
                {% endif %}
                <a href="{{ url_for('logout') }}" class="logout-link">退出登录</a>
            {% else %}
                <a href="{{ url_for('login') }}">登录</a>
            {% endif %}
        </div>
    </nav>

    <div class="page-wrapper">
        <aside class="sidebar" id="pageSidebar">
            <div class="sidebar-section filter">
                <h4>筛选器</h4>
                {% if selected_datasource_id %}
                <div class="filter-controls">
                    <!-- 简化的筛选区域 -->
                    <div class="compact-filter-area">
                        <div class="filter-header">
                            <h5>当前筛选条件</h5>
                            <button id="addFilterBtn" class="add-filter-btn">+ 添加筛选</button>
                        </div>
                        
                        <!-- 已应用的筛选条件标签 -->
                        <div class="active-filters" id="activeFilters">
                            <!-- 会通过JavaScript动态填充 -->
                            <p class="active-filter-summary" id="noFilterMsg">无筛选条件，显示全部数据</p>
            </div>
                        
                        <!-- 添加新筛选条件表单，默认隐藏 -->
                        <div id="newFilterForm" class="new-filter-form" style="display: none;">
                            <div class="form-header">
                                <h5>添加新筛选</h5>
            </div>
                            <div class="form-group">
                                <label for="filterColumn">选择筛选字段:</label>
                                <select id="filterColumn" class="filter-column">
                                    <option value="">-- 请选择 --</option>
                                    <!-- 通过JavaScript动态填充列选项 -->
                                </select>
            </div>
                            
                            <!-- 值选择区域，会根据列的类型动态变化 -->
                            <div id="filterValueContainer" class="filter-value-container">
                                <!-- 将通过JavaScript动态填充适当的筛选控件 -->
                            </div>
                            
                            <div class="form-actions">
                                <button id="addThisFilter" class="add-this-filter">添加此筛选</button>
                                <button id="cancelAddFilter" class="cancel-filter">取消</button>
                            </div>
        </div>
    </div>

                    <!-- 保留价格范围筛选 -->
                    <div class="filter-group">
                        <label>价格范围:</label>
                        <div class="price-range">
                            <input type="number" id="priceMin" placeholder="最低价" min="0">
                            <span class="range-separator">~</span>
                            <input type="number" id="priceMax" placeholder="最高价" min="0">
                        </div>
                    </div>

                    <!-- 排序方式 -->
                    <div class="filter-group">
                        <label>排序方式:</label>
                        <select id="sortOrder" class="form-control">
                            <option value="price_asc">价格从低到高</option>
                            <option value="price_desc">价格从高到低</option>
                            <option value="new_first">最新商品优先</option>
                        </select>
                    </div>
                    
                    <div class="filter-actions">
                        <button id="applyFilters" class="btn-primary">应用筛选</button>
                        <button id="resetFilters" class="btn-secondary">重置</button>
                    </div>
                </div>
                {% else %}
                <div class="empty-state">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M1.5 1.5A.5.5 0 0 1 2 1h12a.5.5 0 0 1 .5.5v2a.5.5 0 0 1-.128.334L10 8.692V13.5a.5.5 0 0 1-.342.474l-3 1A.5.5 0 0 1 6 14.5V8.692L1.628 3.834A.5.5 0 0 1 1.5 3.5v-2z"/>
                    </svg>
                    请先选择数据源，然后使用筛选功能
                </div>
    {% endif %}
            </div>

            <div class="sidebar-section display-settings">
                <h4>显示设置</h4>

                <!-- 字体大小设置 -->
                <div class="setting-group">
                    <h5>轴标签字体大小</h5>
                    <div class="font-size-controls">
                        <div class="font-size-item">
                            <label for="xAxisFontSize">X轴字体大小 (同步调节):</label>
                            <div class="font-size-input-group">
                                <input type="range" id="xAxisFontSize" min="10" max="48" value="14" step="1">
                                <span id="xAxisFontSizeValue">14px</span>
                            </div>
                        </div>
                        <div class="font-size-item">
                            <label for="yAxisFontSize">Y轴字体大小 (同步调节):</label>
                            <div class="font-size-input-group">
                                <input type="range" id="yAxisFontSize" min="10" max="48" value="14" step="1">
                                <span id="yAxisFontSizeValue">14px</span>
                            </div>
                        </div>
                        <button id="resetFontSizeBtn" class="reset-font-size-btn">重置默认</button>
                    </div>
                </div>
            </div>

            <div class="sidebar-section habits">
                <h4>习惯配置</h4>
                <button id="createCurrentHabitBtn" class="create-habit-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>
                    </svg>
                    保存当前筛选为习惯
                </button>
                <div id="savedHabitsList" class="saved-habits-list">
                    <!-- 习惯列表将由JavaScript动态填充 -->
            </div>
            </div>

            <div class="sidebar-section tags">
                <h4>标签配置</h4>
                <div class="tag-config-container">
                    <p class="tag-config-description">配置产品卡片中显示的标签样式。默认显示在卡片底部，也可选择角标模式显示在卡片角落。</p>
                    
                    <div class="tag-config-actions" style="display: flex; gap: 8px; margin-bottom: 15px;">
                        <button id="addTagConfigBtn" class="add-tag-btn" style="flex: 1;">
                            <span class="add-icon">+</span> 添加标签
                        </button>
                        <button id="clearAllTagsBtn" class="add-tag-btn" style="flex: 1; background-color: #ff4d4f; border-color: #ff4d4f;">
                            <span class="add-icon">×</span> 清空所有标签
                        </button>
            </div>
                    
                    <div id="tagConfigList">
                        <!-- 此处将通过JavaScript动态填充标签配置项 -->
        </div>
    </div>
            </div>
        </aside>

        <div class="main-content-wrapper" id="mainContentWrapper">
    <div class="user-container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                        <ul class="flash-messages" style="padding: 0 20px; margin-top: 20px;">
                {% for category, message in messages %}
                    <li class="flash-{{ category }}">{{ message }}</li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}
        <div class="content-area">
            {% block content %}
                <p>欢迎使用 ProductView2。请从上方选择一个数据源开始查看产品。</p>
            {% endblock %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- 全屏模式下的退出按钮 -->
    <button class="fullscreen-exit-btn" id="fullscreenExitBtn" title="退出全屏">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" viewBox="0 0 16 16">
            <path d="M5.5 0a.5.5 0 0 1 .5.5v4A1.5 1.5 0 0 1 4.5 6h-4a.5.5 0 0 1 0-1h4a.5.5 0 0 0 .5-.5v-4a.5.5 0 0 1 .5-.5zm5 0a.5.5 0 0 1 .5.5v4a.5.5 0 0 0 .5.5h4a.5.5 0 0 1 0 1h-4A1.5 1.5 0 0 1 10 4.5v-4a.5.5 0 0 1 .5-.5zM0 10.5a.5.5 0 0 1 .5-.5h4A1.5 1.5 0 0 1 6 11.5v4a.5.5 0 0 1-1 0v-4a.5.5 0 0 0-.5-.5h-4a.5.5 0 0 1-.5-.5zm10 1a1.5 1.5 0 0 1 1.5-1.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 0-.5.5v4a.5.5 0 0 1-1 0v-4z"/>
        </svg>
    </button>
    

    
    {# Announcement Modal HTML Structure #}
    <div id="announcementModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h4 id="modalAnnouncementTitle">数据源公告</h4>
                <button type="button" class="modal-close-btn" id="closeAnnouncementModal">&times;</button>
            </div>
            <div class="modal-body">
                <p id="modalAnnouncementText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" id="modalFooterCloseBtn" class="btn-modal-primary">关闭</button>
            </div>
        </div>
    </div>

    {# Habit Configuration Modal HTML Structure #}
    <div id="habitConfigModal" class="modal-overlay">
        <div class="modal-content" style="max-width: 480px;">
            <div class="modal-header">
                <h4 id="habitModalTitle">保存筛选习惯</h4>
                <button type="button" class="modal-close-btn" id="closeHabitConfigModalBtn">&times;</button>
            </div>
            <div class="modal-body">
                <input type="hidden" id="habitConfigId" value="">
                <div class="form-group">
                    <label for="habitConfigName">习惯名称:</label>
                    <input type="text" id="habitConfigName" name="habitConfigName" placeholder="例如：我的常用视图">
                </div>
                <div class="form-group">
                    <label for="habitConfigDescription">习惯描述:</label>
                    <textarea id="habitConfigDescription" name="habitConfigDescription" placeholder="描述一下这个习惯配置的用途或特点"></textarea>
                </div>
                
                <div id="habitConfigSummary" class="form-summary">
                    <!-- 当前设置摘要将在这里显示 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="cancelHabitConfigBtn" class="btn-modal-secondary">取消</button>
                <button type="button" id="saveHabitConfigBtn" class="btn-modal-primary">保存习惯</button>
            </div>
        </div>
    </div>

    {% if selected_datasource_info %}
        <script id="selectedDataSourceInfoJSON" type="application/json">
            {{ selected_datasource_info|tojson }}
        </script>
    {% endif %}

    {# Pass user_habits to JavaScript if available #}
    {% if user_habits %}
    <script id="userHabitsData" type="application/json">
        {{ user_habits|tojson }}
    </script>
    {% endif %}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 侧边栏切换功能
            const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
            const pageSidebar = document.getElementById('pageSidebar');

            if (sidebarToggleBtn && pageSidebar) {
                sidebarToggleBtn.addEventListener('click', function() {
                    console.log('侧边栏切换按钮被点击');
                    pageSidebar.classList.toggle('collapsed');
                    console.log('侧边栏collapsed状态:', pageSidebar.classList.contains('collapsed'));
                });
            } else {
                console.log('侧边栏切换按钮或侧边栏元素未找到');
                console.log('sidebarToggleBtn:', sidebarToggleBtn);
                console.log('pageSidebar:', pageSidebar);
            }
            
            // 公告显示功能
            const selectedDatasourceId = document.getElementById('selected_datasource_id')?.value;
            // selectedDataSourceInfo 定义从Python后台传入的数据源信息
            const selectedDataSourceInfo = window.selected_datasource_info || null;
            
            // 如果有选择的数据源，并且该数据源开启了公告，并且公告有内容，则显示公告
            if (selectedDataSourceInfo && selectedDataSourceInfo.announcement_enabled && selectedDataSourceInfo.announcement) {
                const announcementModal = document.getElementById('announcementModal');
                const modalAnnouncementText = document.getElementById('modalAnnouncementText');
                const closeAnnouncementModal = document.getElementById('closeAnnouncementModal');
                const modalFooterCloseBtn = document.getElementById('modalFooterCloseBtn');
                
                if (announcementModal && modalAnnouncementText) {
                    // 检查localStorage中的记录，判断是否需要显示公告
                    const announcementKey = `announcement_${selectedDatasourceId}_${selectedDataSourceInfo.updated_at || ''}`;
                    const isDismissed = localStorage.getItem(announcementKey) === 'dismissed';
                    
                    if (!isDismissed) {
                        // 设置公告内容
                        modalAnnouncementText.textContent = selectedDataSourceInfo.announcement;
                        
                        // 显示公告
                        announcementModal.classList.add('active');
                        
                        // 绑定关闭按钮事件
                        if (closeAnnouncementModal) {
                            closeAnnouncementModal.addEventListener('click', function() {
                                announcementModal.classList.remove('active');
                                localStorage.setItem(announcementKey, 'dismissed');
                            });
                        }
                        
                        // 绑定底部关闭按钮事件
                        if (modalFooterCloseBtn) {
                            modalFooterCloseBtn.addEventListener('click', function() {
                                announcementModal.classList.remove('active');
                                localStorage.setItem(announcementKey, 'dismissed');
                            });
                        }
                    }
                }
            }
            
            // 习惯配置功能
            const openHabitConfigModalBtn = document.getElementById('openHabitConfigModalBtn');
            const habitConfigModal = document.getElementById('habitConfigModal');
            const closeHabitConfigModalBtn = document.getElementById('closeHabitConfigModalBtn');
            const createCurrentHabitBtn = document.getElementById('createCurrentHabitBtn');
            
            // 当前页面状态和筛选条件
            const currentState = {
                datasourceId: selectedDatasourceId || '',
                xAxis: getQueryParam('selected_x_axis') || '',
                yAxis: getQueryParam('selected_y_axis') || '',
                filters: {},
                sort: {},
                
                // 获取当前的完整状态
                getCurrentState() {
                    // 获取URL参数
                    const params = new URLSearchParams(window.location.search);
                    this.datasourceId = params.get('selected_datasource_id') || this.datasourceId;
                    this.xAxis = params.get('selected_x_axis') || this.xAxis;
                    this.yAxis = params.get('selected_y_axis') || this.yAxis;
                    
                    // 获取价格筛选条件
                    const priceMin = document.getElementById('priceMin');
                    const priceMax = document.getElementById('priceMax');
                    if (priceMin && priceMin.value) {
                        this.filters.minPrice = priceMin.value;
                    }
                    if (priceMax && priceMax.value) {
                        this.filters.maxPrice = priceMax.value;
                    }
                    
                    // 获取其他活跃筛选条件
                    if (typeof activeFilters !== 'undefined') {
                        this.filters.columns = {...activeFilters};
                    }
                    
                    // 获取排序设置
                    const sortOrder = document.getElementById('sortOrder');
                    if (sortOrder && sortOrder.value) {
                        this.sort.order = sortOrder.value;
                    }
                    
                    // 获取当前标签配置
                    const tagConfigs = localStorage.getItem('tagConfigurations');
                    this.tagConfigurations = tagConfigs ? JSON.parse(tagConfigs) : [];
                    
                    return {
                        datasourceId: this.datasourceId,
                        xAxis: this.xAxis,
                        yAxis: this.yAxis,
                        filters: this.filters,
                        sort: this.sort,
                        tagConfigurations: this.tagConfigurations
                    };
                },
                
                // 检查当前状态是否有效
                isValidState() {
                    return this.datasourceId && this.xAxis && this.yAxis;
                },
                
                // 格式化显示状态摘要
                formatSummary() {
                    if (!this.isValidState()) {
                        return '当前视图配置不完整，请选择数据源和X/Y轴。';
                    }
                    
                    let summary = `<div class="summary-item"><strong>数据源:</strong> ${window.selected_datasource_info?.name || this.datasourceId}</div>`;
                    summary += `<div class="summary-item"><strong>X轴:</strong> ${this.xAxis}</div>`;
                    summary += `<div class="summary-item"><strong>Y轴:</strong> ${this.yAxis}</div>`;
                    
                    // 添加筛选条件摘要
                    if (Object.keys(this.filters).length > 0) {
                        summary += '<div class="summary-item"><strong>筛选条件:</strong> ';
                        if (this.filters.minPrice || this.filters.maxPrice) {
                            summary += `价格范围: ${this.filters.minPrice || '无下限'} ~ ${this.filters.maxPrice || '无上限'}; `;
                        }
                        if (this.filters.columns && Object.keys(this.filters.columns).length > 0) {
                            summary += `${Object.keys(this.filters.columns).length}个列筛选条件`;
                        }
                        summary += '</div>';
                    }
                    
                    // 添加排序摘要
                    if (this.sort.order) {
                        let sortText = '';
                        switch(this.sort.order) {
                            case 'price_asc': sortText = '价格从低到高'; break;
                            case 'price_desc': sortText = '价格从高到低'; break;
                            case 'new_first': sortText = '最新商品优先'; break;
                            default: sortText = this.sort.order;
                        }
                        summary += `<div class="summary-item"><strong>排序:</strong> ${sortText}</div>`;
                    }
                    
                    // 添加标签配置摘要
                    const enabledTags = this.tagConfigurations.filter(tag => tag.enabled && tag.fieldName);
                    if (enabledTags.length > 0) {
                        summary += `<div class="summary-item"><strong>标签配置:</strong> ${enabledTags.length}个已启用标签</div>`;
                    }
                    
                    return summary;
                }
            };
            
            // 辅助函数：从URL获取查询参数
            function getQueryParam(name) {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get(name) || '';
            }
            
            // 用户习惯数据
            const userHabits = window.user_habits || [];
            
            // 显示保存的习惯列表
            function updateHabitsList() {
                const savedHabitsList = document.getElementById('savedHabitsList');
                if (!savedHabitsList) return;
                
                if (userHabits.length > 0) {
                    let html = '';
                    userHabits.forEach(habit => {
                        html += `
                            <li class="habit-item" data-habit-id="${habit.id}">
                                <div class="habit-info">
                                    <div class="habit-name" title="${habit.name}">${habit.name}</div>
                                    <div class="habit-meta">
                                        ${habit.settings && habit.settings.x_axis ? habit.settings.x_axis : ''}
                                        ${habit.settings && habit.settings.y_axis ? ' / ' + habit.settings.y_axis : ''}
                                    </div>
                                </div>
                                <div class="habit-actions">
                                    <span class="habit-delete-btn" title="删除此习惯">×</span>
                                </div>
                            </li>
                        `;
                    });
                    savedHabitsList.innerHTML = html;
                    
                    // 绑定点击事件
                    document.querySelectorAll('.habit-item').forEach(item => {
                        item.addEventListener('click', function(e) {
                            if (!e.target.classList.contains('habit-delete-btn')) {
                                const habitId = this.getAttribute('data-habit-id');
                                const habit = userHabits.find(h => h.id === habitId);
                                if (habit) applyHabit(habit);
                            }
                        });
                    });
                    
                    // 绑定删除按钮事件
                    document.querySelectorAll('.habit-delete-btn').forEach(btn => {
                        btn.addEventListener('click', function(e) {
                            e.stopPropagation();
                            const habitId = this.closest('.habit-item').getAttribute('data-habit-id');
                            const habit = userHabits.find(h => h.id === habitId);
                            if (habit && confirm(`确定要删除习惯"${habit.name}"吗？`)) {
                                deleteHabit(habitId);
                            }
                        });
                    });
                } else {
                    savedHabitsList.innerHTML = '<div class="empty-state">还没有保存的习惯</div>';
                }
            }
            
            // 打开创建习惯模态框
            function openCreateHabitModal() {
                // 获取当前状态
                const state = currentState.getCurrentState();
                
                if (!currentState.isValidState()) {
                    alert('请先选择数据源、X轴和Y轴，再保存习惯。');
                    return;
                }
                
                // 重置表单
                document.getElementById('habitConfigId').value = '';
                document.getElementById('habitConfigName').value = '';
                document.getElementById('habitConfigDescription').value = '';
                
                // 显示当前设置摘要
                const summaryContainer = document.getElementById('habitConfigSummary');
                if (summaryContainer) {
                    summaryContainer.innerHTML = currentState.formatSummary();
                }
                
                // 显示模态框
                habitConfigModal.classList.add('active');
            }
            
            // 关闭习惯配置模态框
            if (closeHabitConfigModalBtn && habitConfigModal) {
                closeHabitConfigModalBtn.addEventListener('click', function() {
                    habitConfigModal.classList.remove('active');
                });
            }
            
            // 保存习惯
            if (saveHabitConfigBtn) {
                saveHabitConfigBtn.addEventListener('click', function() {
                    // 获取表单数据
                    const habitId = document.getElementById('habitConfigId').value;
                    const habitName = document.getElementById('habitConfigName').value;
                    const habitDescription = document.getElementById('habitConfigDescription').value;
                    
                    // 验证表单
                    if (!habitName) {
                        alert('请输入习惯名称');
                        return;
                    }
                    
                    // 获取当前状态
                    const state = currentState.getCurrentState();
                    
                    // 构造习惯数据
                    const habitData = {
                        id: habitId || null,
                        name: habitName,
                        description: habitDescription || '',
                        settings: {
                            datasource_id: state.datasourceId,
                            x_axis: state.xAxis,
                            y_axis: state.yAxis,
                            filters: state.filters,
                            sort: state.sort,
                            tagConfigurations: state.tagConfigurations
                        }
                    };
                    
                    // 发送保存请求
                    fetch('/save_habit', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(habitData),
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // 更新本地习惯列表
                            if (habitId) {
                                // 更新现有习惯
                                const index = userHabits.findIndex(h => h.id === habitId);
                                if (index !== -1) {
                                    userHabits[index] = data.habit;
                                }
                            } else {
                                // 添加新习惯
                                userHabits.push(data.habit);
                            }
                            updateHabitsList();
                            alert('习惯保存成功！');
                        } else {
                            alert(data.message || '保存失败，请重试。');
                        }
                    })
                    .catch(error => {
                        console.error('保存习惯时出错:', error);
                        alert('保存失败，请重试。');
                    });
                    
                    // 关闭模态框
                    habitConfigModal.classList.remove('active');
                });
            }
            
            // 删除习惯
            function deleteHabit(habitId) {
                fetch('/delete_habit', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({habit_id: habitId}),
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 从本地列表移除
                        const index = userHabits.findIndex(h => h.id === habitId);
                        if (index !== -1) {
                            userHabits.splice(index, 1);
                            updateHabitsList();
                        }
                    } else {
                        alert(data.message || '删除失败，请重试。');
                    }
                })
                .catch(error => {
                    console.error('删除习惯时出错:', error);
                    alert('删除失败，请重试。');
                });
            }
            
            // 应用习惯
            function applyHabit(habit) {
                console.log("应用习惯:", habit.name);
                // 检查习惯配置是否完整
                if (!habit.settings || 
                    !habit.settings.datasource_id || 
                    !habit.settings.x_axis || 
                    !habit.settings.y_axis) {
                    alert('此习惯配置不完整，无法应用。');
                    return;
                }
                
                // 注释掉标签配置的自动应用，让用户手动配置标签
                // if (habit.settings.tagConfigurations && Array.isArray(habit.settings.tagConfigurations)) {
                //     // 保存标签配置到localStorage，以便页面加载后使用
                //     localStorage.setItem('tagConfigurations', JSON.stringify(habit.settings.tagConfigurations));
                //     // 添加一个标记，表示这是从习惯应用的标签配置
                //     localStorage.setItem('tagConfigFromHabit', 'true');
                // }
                
                // 构建基础URL参数
                let url = `/index?selected_datasource_id=${habit.settings.datasource_id}&selected_x_axis=${habit.settings.x_axis}&selected_y_axis=${habit.settings.y_axis}`;
                
                // 添加筛选参数
                if (habit.settings.filters) {
                    console.log("习惯中的筛选条件:", habit.settings.filters);
                    // 如果有筛选条件，添加到URL
                    const filtersParam = encodeURIComponent(JSON.stringify(habit.settings.filters));
                    url += `&filters=${filtersParam}`;
                }
                
                // 添加排序参数
                if (habit.settings.sort && habit.settings.sort.order) {
                    url += `&sort=${habit.settings.sort.order}`;
                }
                
                // 添加一个时间戳参数，确保浏览器不会使用缓存的页面
                url += `&_t=${new Date().getTime()}`;
                
                console.log("跳转到URL:", url);
                // 跳转到构建的URL
                window.location.href = url;
            }
            
            // 初始化创建习惯按钮事件
            if (createCurrentHabitBtn) {
                createCurrentHabitBtn.addEventListener('click', openCreateHabitModal);
            }
            
            // 页面加载后初始化习惯列表
            updateHabitsList();

            // ============== 筛选功能 ==============
            // 筛选相关变量和DOM元素
            let activeFilters = {}; // 保存当前活跃的筛选条件
            
            const addFilterBtn = document.getElementById('addFilterBtn');
            const newFilterForm = document.getElementById('newFilterForm');
            const filterColumn = document.getElementById('filterColumn');
            const filterValueContainer = document.getElementById('filterValueContainer');
            const addThisFilter = document.getElementById('addThisFilter');
            const activeFiltersContainer = document.getElementById('activeFilters');
            const noFilterMsg = document.getElementById('noFilterMsg');
            
            console.log("筛选器DOM元素:", {
                filterColumn: !!filterColumn,
                filterValueContainer: !!filterValueContainer,
                addFilterBtn: !!addFilterBtn,
                newFilterForm: !!newFilterForm,
                addThisFilter: !!addThisFilter,
                activeFiltersContainer: !!activeFiltersContainer,
                noFilterMsg: !!noFilterMsg
            });
            
            // 检测列数据类型函数
            function detectColumnDataType(columnName) {
                if (window.product_data && window.product_data.length > 0) {
                    const sampleSize = Math.min(10, window.product_data.length);
                    let numberCount = 0;
                    
                    for (let i = 0; i < sampleSize; i++) {
                        const value = window.product_data[i][columnName];
                        if (value && !isNaN(parseFloat(value)) && isFinite(value)) {
                            numberCount++;
                        }
                    }
                    
                    if (numberCount > sampleSize / 2) {
                        return 'number';
                    }
                }
                
                return 'string';
            }
            
            // 获取列唯一值函数
            function getUniqueValuesForColumn(columnName) {
                if (!window.product_data || !Array.isArray(window.product_data)) {
                    return [];
                }
                
                const values = new Set();
                window.product_data.forEach(item => {
                    if (item && columnName in item) {
                        values.add(item[columnName]);
                    }
                });
                
                return Array.from(values).sort((a, b) => {
                    if (a === null || a === undefined || a === '') return 1;
                    if (b === null || b === undefined || b === '') return -1;
                    
                    const numA = parseFloat(a);
                    const numB = parseFloat(b);
                    if (!isNaN(numA) && !isNaN(numB)) {
                        return numA - numB;
                    }
                    
                    return String(a).localeCompare(String(b));
                });
            }
            
            // 当选择列时，根据列类型显示相应的筛选控件
            function onFilterColumnChange() {
                console.log("列选择变化:", filterColumn.value);
                const selectedColumn = filterColumn.value;
                if (!selectedColumn) {
                    filterValueContainer.innerHTML = '';
                    return;
                }
                
                // 判断列的数据类型
                const columnType = detectColumnDataType(selectedColumn);
                console.log("检测到列类型:", columnType);
                
                // 根据类型创建不同的筛选控件
                if (columnType === 'number') {
                    createNumberRangeControls(selectedColumn);
                } else {
                    createCategoryControls(selectedColumn);
                }
            }
            
            // 创建数值范围控件
            function createNumberRangeControls(columnName) {
                const filterValueContainer = document.getElementById('filterValueContainer');
                
                if (!filterValueContainer) {
                    console.error("创建数值范围控件失败: 未找到filterValueContainer");
                    return;
                }
                
                filterValueContainer.className = 'filter-value-container range';
                filterValueContainer.innerHTML = `
                    <input type="number" id="filter_min_${columnName}" placeholder="最小值" step="any" style="padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; flex: 1;">
                    <span class="range-separator">~</span>
                    <input type="number" id="filter_max_${columnName}" placeholder="最大值" step="any" style="padding: 6px 8px; border: 1px solid #ddd; border-radius: 4px; flex: 1;">
                `;
                
                // 添加调试日志
                console.log("创建了数值范围控件");
            }
            
            // 创建分类筛选控件（多选）
            function createCategoryControls(columnName) {
                const uniqueValues = getUniqueValuesForColumn(columnName);
                const filterValueContainer = document.getElementById('filterValueContainer');
                
                if (!filterValueContainer) {
                    console.error("创建分类控件失败: 未找到filterValueContainer");
                    return;
                }
                
                filterValueContainer.className = 'filter-value-container multi-select';
                filterValueContainer.innerHTML = `
                    <div class="multi-select-actions">
                        <span class="select-all" data-column="${columnName}">全选</span>
                        <span class="deselect-all" data-column="${columnName}">反选</span>
                    </div>
                    <div class="multi-select-options" id="options_${columnName}"></div>
                `;
                
                const optionsContainer = filterValueContainer.querySelector(`#options_${columnName}`);
                
                if (!optionsContainer) {
                    console.error("创建分类控件失败: 未找到optionsContainer");
                    return;
                }
                
                // 添加滚动监听，隐藏滚动提示
                optionsContainer.addEventListener('scroll', function() {
                    // 只要滚动，立即添加scrolled类
                    this.classList.add('scrolled');
                }, { passive: true });
                
                // 对值进行排序
                uniqueValues.sort((a, b) => {
                    // 将空值排在最后
                    if (a === null || a === undefined || a === '') return 1;
                    if (b === null || b === undefined || b === '') return -1;
                    
                    // 尝试数字排序
                    const numA = parseFloat(a);
                    const numB = parseFloat(b);
                    if (!isNaN(numA) && !isNaN(numB)) {
                        return numA - numB;
                    }
                    
                    // 文本排序
                    return String(a).localeCompare(String(b), 'zh-CN');
                });
                
                uniqueValues.forEach((value, index) => {
                    const displayValue = value === null || value === undefined || value === '' ? '(空值)' : value;
                    
                    const option = document.createElement('div');
                    option.className = 'multi-select-option';
                    
                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.id = `opt_${columnName}_${index}`;
                    checkbox.value = value === null ? 'null' : value;
                    checkbox.checked = true;
                    
                    const label = document.createElement('label');
                    label.htmlFor = `opt_${columnName}_${index}`;
                    label.title = displayValue; // 添加title属性，鼠标悬停时显示完整文本
                    label.textContent = displayValue;
                    
                    option.appendChild(checkbox);
                    option.appendChild(label);
                    optionsContainer.appendChild(option);
                });
                
                // 绑定全选/反选事件
                const selectAllBtn = filterValueContainer.querySelector('.select-all');
                const deselectAllBtn = filterValueContainer.querySelector('.deselect-all');
                
                if (selectAllBtn) {
                    selectAllBtn.addEventListener('click', () => {
                        optionsContainer.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                            cb.checked = true;
                        });
                    });
                }
                
                if (deselectAllBtn) {
                    deselectAllBtn.addEventListener('click', () => {
                        optionsContainer.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                            cb.checked = !cb.checked;
                        });
                    });
                }
                
                // 添加调试日志
                console.log("创建了分类筛选控件:", {
                    containerId: filterValueContainer.id,
                    className: filterValueContainer.className,
                    optionsCount: uniqueValues.length,
                    firstFewValues: uniqueValues.slice(0, 5)
                });
            }
            
            // 添加当前的筛选条件
            function addCurrentFilter() {
                const addThisFilterBtn = document.getElementById('addThisFilter');
                const isEditMode = addThisFilterBtn && addThisFilterBtn.getAttribute('data-mode') === 'edit';
                
                // 获取列名，如果是编辑模式则从按钮的data属性中获取
                let columnName = filterColumn.value;
                if (isEditMode && addThisFilterBtn) {
                    columnName = addThisFilterBtn.getAttribute('data-column') || columnName;
                }
                
                if (!columnName) return;
                
                const columnType = detectColumnDataType(columnName);
                let filterConfig;
                
                if (columnType === 'number') {
                    // 获取数值范围
                    const minInput = document.getElementById(`filter_min_${columnName}`);
                    const maxInput = document.getElementById(`filter_max_${columnName}`);
                    
                    const min = minInput ? minInput.value : '';
                    const max = maxInput ? maxInput.value : '';
                    
                    if (!min && !max) {
                        alert('请输入至少一个范围值');
                        return;
                    }
                    
                    filterConfig = {
                        type: 'range',
                        min: min ? parseFloat(min) : null,
                        max: max ? parseFloat(max) : null
                    };
                    
                } else {
                    // 获取分类选择
                    const selectedValues = [];
                    const checkboxes = document.querySelectorAll(`#options_${columnName} input[type="checkbox"]:checked`);
                    
                    checkboxes.forEach(cb => {
                        let value = cb.value;
                        // 处理特殊值
                        if (value === 'null') value = null;
                        else if (value === 'undefined') value = undefined;
                        selectedValues.push(value);
                    });
                    
                    if (selectedValues.length === 0) {
                        alert('请至少选择一个选项');
                        return;
                    }
                    
                    filterConfig = {
                        type: 'category',
                        values: selectedValues
                    };
                }
                
                // 添加到活跃筛选条件中
                activeFilters[columnName] = filterConfig;
                
                // 更新显示
                updateActiveFiltersDisplay();
                
                // 重置并隐藏添加表单
                filterColumn.value = '';
                filterValueContainer.innerHTML = '';
                newFilterForm.style.display = 'none';
                
                // 重置按钮状态
                if (addThisFilterBtn) {
                    addThisFilterBtn.textContent = '添加此筛选';
                    addThisFilterBtn.removeAttribute('data-mode');
                    addThisFilterBtn.removeAttribute('data-column');
                }
            }
            
            // 更新活跃筛选条件的显示
            function updateActiveFiltersDisplay() {
                const activeFiltersContainer = document.getElementById('activeFilters');
                if (!activeFiltersContainer) {
                    console.error("更新筛选显示失败: 未找到activeFiltersContainer");
                    return;
                }
                
                // 清除现有的标签
                activeFiltersContainer.innerHTML = '';
                
                // 检查是否有活跃筛选条件
                const filterCount = Object.keys(activeFilters).length;
                
                if (filterCount === 0) {
                    // 没有筛选条件时显示提示
                    const noFilterMsg = document.createElement('p');
                    noFilterMsg.className = 'active-filter-summary';
                    noFilterMsg.id = 'noFilterMsg';
                    noFilterMsg.textContent = '无筛选条件，显示全部数据';
                    activeFiltersContainer.appendChild(noFilterMsg);
                    return;
                }
                
                // 添加摘要文本
                const summary = document.createElement('p');
                summary.className = 'active-filter-summary';
                summary.textContent = `已应用 ${filterCount} 个筛选条件`;
                activeFiltersContainer.appendChild(summary);
                
                // 为每个筛选条件创建标签
                for (const [column, config] of Object.entries(activeFilters)) {
                    let filterText = '';
                    
                    if (config.type === 'range') {
                        if (config.min !== null && config.max !== null) {
                            filterText = `${column}: ${config.min} ~ ${config.max}`;
                        } else if (config.min !== null) {
                            filterText = `${column}: ≥ ${config.min}`;
                        } else if (config.max !== null) {
                            filterText = `${column}: ≤ ${config.max}`;
                        }
                    } else if (config.type === 'category') {
                        let valueCount = config.values.length;
                        let totalCount = getUniqueValuesForColumn(column).length;
                        
                        if (valueCount === totalCount) {
                            filterText = `${column}: 全部`;
                        } else if (valueCount <= 3) {
                            // 当选项较少时，直接显示选中的值
                            filterText = `${column}: ${config.values.slice(0, 3).join(', ')}`;
                            if (valueCount > 3) {
                                filterText += `... 等${valueCount}项`;
                            }
                        } else {
                            filterText = `${column}: 已选择 ${valueCount} 项`;
                        }
                    }
                    
                    const tag = document.createElement('div');
                    tag.className = 'filter-tag';
                    tag.innerHTML = `
                        <span class="filter-text" data-column="${column}">${filterText}</span>
                        <span class="remove-filter" data-column="${column}">×</span>
                    `;
                    
                    // 添加点击修改事件
                    const filterTextElement = tag.querySelector('.filter-text');
                    filterTextElement.addEventListener('click', () => {
                        editFilter(column, config);
                    });
                    
                    // 添加删除事件
                    tag.querySelector('.remove-filter').addEventListener('click', () => {
                        // 短暂高亮显示将被删除的筛选条件
                        tag.classList.add('removing');
                        setTimeout(() => {
                            delete activeFilters[column];
                            updateActiveFiltersDisplay();
                        }, 300);
                    });
                    
                    activeFiltersContainer.appendChild(tag);
                }
                
                console.log("活跃筛选条件显示已更新");
            }
            
            // 编辑已有的筛选条件
            function editFilter(column, config) {
                console.log("编辑筛选条件:", column, config);
                
                // 获取相关DOM元素
                const newFilterForm = document.getElementById('newFilterForm');
                const filterColumn = document.getElementById('filterColumn');
                const addThisFilterBtn = document.getElementById('addThisFilter');
                
                if (!newFilterForm || !filterColumn || !addThisFilterBtn) {
                    console.error("编辑筛选条件失败: 缺少必要DOM元素");
                    return;
                }
                
                // 显示筛选表单
                newFilterForm.style.display = 'flex';
                
                // 设置选择的列
                filterColumn.value = column;
                
                // 根据筛选类型创建对应的控件
                if (config.type === 'range') {
                    createNumberRangeControls(column);
                    
                    // 设置范围值
                    setTimeout(() => {
                        const minInput = document.getElementById(`filter_min_${column}`);
                        const maxInput = document.getElementById(`filter_max_${column}`);
                        
                        if (minInput && config.min !== null) {
                            minInput.value = config.min;
                        }
                        
                        if (maxInput && config.max !== null) {
                            maxInput.value = config.max;
                        }
                    }, 0);
                    
                } else if (config.type === 'category') {
                    createCategoryControls(column);
                    
                    // 设置选中的值
                    setTimeout(() => {
                        const optionsContainer = document.getElementById(`options_${column}`);
                        if (optionsContainer) {
                            const checkboxes = optionsContainer.querySelectorAll('input[type="checkbox"]');
                            checkboxes.forEach(cb => {
                                const value = cb.value === 'null' ? null : cb.value;
                                cb.checked = config.values.includes(value);
                            });
                        }
                    }, 0);
                }
                
                // 修改添加按钮文字和状态
                addThisFilterBtn.textContent = '更新筛选';
                addThisFilterBtn.setAttribute('data-mode', 'edit');
                addThisFilterBtn.setAttribute('data-column', column);
                
                // 添加高亮视觉效果
                newFilterForm.classList.add('highlight-edit');
                setTimeout(() => {
                    newFilterForm.classList.remove('highlight-edit');
                }, 1000);
                
                console.log("筛选条件编辑表单已设置");
            }
            
            // 从URL恢复筛选状态
            function restoreFiltersFromUrl() {
                console.log("尝试从URL恢复筛选状态...");
                const urlParams = new URLSearchParams(window.location.search);
                const filtersParam = urlParams.get('filters');
                
                if (!filtersParam) {
                    console.log("URL中没有筛选参数");
                    return;
                }
                
                try {
                    const filters = JSON.parse(decodeURIComponent(filtersParam));
                    console.log("从URL解析的筛选条件:", filters);
                    
                    // 清空当前筛选条件
                    for (const key in activeFilters) {
                        delete activeFilters[key];
                    }
                    
                    // 添加价格筛选
                    const priceMin = document.getElementById('priceMin');
                    const priceMax = document.getElementById('priceMax');
                    
                    if (filters.minPrice && priceMin) {
                        priceMin.value = filters.minPrice;
                        console.log(`设置最低价: ${filters.minPrice}`);
                    }
                    
                    if (filters.maxPrice && priceMax) {
                        priceMax.value = filters.maxPrice;
                        console.log(`设置最高价: ${filters.maxPrice}`);
                    }
                    
                    // 处理旧格式的价格筛选
                    if (filters.price) {
                        if (filters.price.min !== null && priceMin) {
                            priceMin.value = filters.price.min;
                            console.log(`设置最低价(旧格式): ${filters.price.min}`);
                        }
                        
                        if (filters.price.max !== null && priceMax) {
                            priceMax.value = filters.price.max;
                            console.log(`设置最高价(旧格式): ${filters.price.max}`);
                        }
                    }
                    
                    // 添加其他筛选条件
                    if (filters.columns) {
                        // 新格式：使用columns子对象
                        for (const [column, config] of Object.entries(filters.columns)) {
                            activeFilters[column] = config;
                            console.log(`添加列筛选(新格式): ${column}`);
                        }
                    } else {
                        // 旧格式：直接使用顶级对象
                        for (const [column, config] of Object.entries(filters)) {
                            // 跳过价格(已单独处理)和非对象类型的筛选项
                            if (column === 'price' || column === 'minPrice' || column === 'maxPrice' || typeof config !== 'object') continue;
                            
                            activeFilters[column] = config;
                            console.log(`添加列筛选(旧格式): ${column}`);
                        }
                    }
                    
                    // 更新显示
                    updateActiveFiltersDisplay();
                    console.log("筛选状态已从URL恢复");
                } catch (error) {
                    console.error("从URL恢复筛选状态出错:", error);
                }
            }
            
            // 应用所有筛选条件
            function applyAllFilters() {
                // 构建筛选条件对象
                const filters = {...activeFilters};
                
                // 添加价格范围
                const priceMin = document.getElementById('priceMin');
                const priceMax = document.getElementById('priceMax');
                
                if ((priceMin && priceMin.value) || (priceMax && priceMax.value)) {
                    filters.price = {
                        min: priceMin && priceMin.value ? parseFloat(priceMin.value) : null,
                        max: priceMax && priceMax.value ? parseFloat(priceMax.value) : null
                    };
                }
                
                // 应用筛选条件
                applyFiltersToData(filters);
            }
            
            // 实际应用筛选条件到数据
            function applyFiltersToData(filters) {
                console.log('应用筛选条件:', filters);
                
                // 如果已经有X/Y轴选择，则根据筛选条件刷新页面
                const mainXAxisSelect = document.querySelector('select[name="selected_x_axis"]');
                const mainYAxisSelect = document.querySelector('select[name="selected_y_axis"]');
                const datasourceSelect = document.getElementById('selected_datasource_id');
                
                if (mainXAxisSelect && mainYAxisSelect && datasourceSelect) {
                    const xAxis = mainXAxisSelect.value;
                    const yAxis = mainYAxisSelect.value;
                    const dsId = datasourceSelect.value;
                    
                    if (xAxis && yAxis && dsId) {
                        // 将筛选条件编码为URL参数
                        const filtersParam = encodeURIComponent(JSON.stringify(filters));
                        
                        // 构建URL并刷新页面
                        const url = `${window.location.pathname}?selected_datasource_id=${dsId}&selected_x_axis=${xAxis}&selected_y_axis=${yAxis}&filters=${filtersParam}`;
                        window.location.href = url;
                    } else {
                        alert('请先选择X轴和Y轴，然后再应用筛选条件');
                    }
                } else {
                    console.error('找不到X/Y轴选择器或数据源选择器');
                    alert('应用筛选条件失败：找不到必要的页面元素');
                }
            }
            
            // 重置所有筛选条件
            function resetAllFilters() {
                // 清空价格输入
                const priceMin = document.getElementById('priceMin');
                const priceMax = document.getElementById('priceMax');
                
                if (priceMin) priceMin.value = '';
                if (priceMax) priceMax.value = '';
                
                // 清空活跃筛选条件
                for (const key in activeFilters) {
                    delete activeFilters[key];
                }
                
                // 更新显示
                updateActiveFiltersDisplay();
                
                // 重置并隐藏添加表单
                if (filterColumn) filterColumn.value = '';
                if (filterValueContainer) filterValueContainer.innerHTML = '';
                if (newFilterForm) newFilterForm.style.display = 'none';
            }
            
            // 检查URL是否包含筛选参数并应用
            function checkAndApplyUrlFilters() {
                console.log("检查URL中的筛选参数...");
                const urlParams = new URLSearchParams(window.location.search);
                const filtersParam = urlParams.get('filters');
                
                if (filtersParam) {
                    try {
                        const filters = JSON.parse(decodeURIComponent(filtersParam));
                        console.log('从URL加载筛选条件:', filters);
                        
                        // 清空当前筛选条件
                        for (const key in activeFilters) {
                            delete activeFilters[key];
                        }
                        
                        // 恢复价格筛选
                        if (filters.minPrice || filters.maxPrice) {
                            const priceMin = document.getElementById('priceMin');
                            const priceMax = document.getElementById('priceMax');
                            
                            if (priceMin && filters.minPrice) {
                                priceMin.value = filters.minPrice;
                            }
                            
                            if (priceMax && filters.maxPrice) {
                                priceMax.value = filters.maxPrice;
                            }
                        }
                        
                        // 恢复列筛选
                        if (filters.columns) {
                            Object.assign(activeFilters, filters.columns);
                        } else {
                            // 如果filters本身就是列筛选的映射（旧格式）
                            for (const [column, config] of Object.entries(filters)) {
                                // 跳过价格(已单独处理)和非对象类型的筛选项
                                if (column === 'price' || column === 'minPrice' || column === 'maxPrice' || typeof config !== 'object') continue;
                                
                                activeFilters[column] = config;
                            }
                        }
                        
                        // 更新UI显示
                        updateActiveFiltersDisplay();
                    } catch (err) {
                        console.error('解析URL筛选参数失败:', err);
                    }
                }
            }
            
            // 初始化筛选器功能
            function initCompactFilter() {
                console.log("开始初始化筛选器功能...");

                // 检查必要的元素
                const filterColumn = document.getElementById('filterColumn');
                const filterValueContainer = document.getElementById('filterValueContainer');
                const addFilterBtn = document.getElementById('addFilterBtn');
                const newFilterForm = document.getElementById('newFilterForm');
                const addThisFilter = document.getElementById('addThisFilter');
                const activeFiltersContainer = document.getElementById('activeFilters');

                // 输出各个元素是否存在的调试信息
                console.log("筛选器必要元素状态:", {
                    filterColumn: filterColumn ? "已找到" : "未找到",
                    filterValueContainer: filterValueContainer ? "已找到" : "未找到",
                    addFilterBtn: addFilterBtn ? "已找到" : "未找到",
                    newFilterForm: newFilterForm ? "已找到" : "未找到",
                    addThisFilter: addThisFilter ? "已找到" : "未找到",
                    activeFiltersContainer: activeFiltersContainer ? "已找到" : "未找到",
                    csv_headers: window.csv_headers ? `找到 ${window.csv_headers.length} 列` : "未找到",
                    product_data: window.product_data ? `找到 ${window.product_data.length} 行数据` : "未找到"
                });

                // 即使没有数据也要绑定基本的按钮事件
                if (!filterColumn) {
                    console.warn("未找到筛选列选择器，跳过列相关功能");
                } else if (!window.csv_headers || window.csv_headers.length === 0) {
                    console.warn("没有CSV表头数据，但仍会绑定按钮事件");
                }
                
                try {
                    // 填充列选择下拉框（如果有数据的话）
                    if (filterColumn) {
                        filterColumn.innerHTML = '<option value="">选择列</option>';

                        if (window.csv_headers && window.csv_headers.length > 0) {
                            window.csv_headers.forEach(header => {
                                // 跳过价格列，有专门的范围过滤器
                                if (window.selected_datasource_info &&
                                    header === window.selected_datasource_info.product_price_column) return;

                                const option = document.createElement('option');
                                option.value = header;
                                option.textContent = header;
                                filterColumn.appendChild(option);
                            });
                        } else {
                            // 没有数据时显示提示
                            const option = document.createElement('option');
                            option.value = "";
                            option.textContent = "请先选择数据源";
                            filterColumn.appendChild(option);
                        }

                        // 设置列选择变化事件
                        filterColumn.addEventListener('change', onFilterColumnChange);
                    }
                    
                    // 设置添加筛选按钮事件 - 修复点击问题
                    if (addFilterBtn) {
                        // 先移除可能已存在的事件监听器，避免重复绑定
                        addFilterBtn.removeEventListener('click', toggleNewFilterForm);
                        addFilterBtn.addEventListener('click', toggleNewFilterForm);
                        console.log("成功绑定添加筛选按钮事件");
                    } else {
                        console.warn("未找到添加筛选按钮元素");
                    }
                    
                    // 设置添加此筛选条件按钮事件
                    if (addThisFilter) {
                        // 先移除可能已存在的事件监听器，避免重复绑定
                        addThisFilter.removeEventListener('click', addFilterBtnHandler);
                        addThisFilter.addEventListener('click', addFilterBtnHandler);
                        console.log("成功绑定添加此筛选按钮事件");
                    } else {
                        console.warn("未找到添加此筛选条件按钮元素");
                    }
                    
                    // 设置应用筛选按钮事件
                    const applyFiltersBtn = document.getElementById('applyFilters');
                    if (applyFiltersBtn) {
                        // 先移除可能已存在的事件监听器，避免重复绑定
                        applyFiltersBtn.removeEventListener('click', applyFiltersBtnHandler);
                        applyFiltersBtn.addEventListener('click', applyFiltersBtnHandler);
                        console.log("成功绑定应用筛选按钮事件");
                    } else {
                        console.warn("未找到应用筛选按钮元素");
                    }
                    
                    // 设置重置筛选按钮事件
                    const resetFiltersBtn = document.getElementById('resetFilters');
                    if (resetFiltersBtn) {
                        // 先移除可能已存在的事件监听器，避免重复绑定
                        resetFiltersBtn.removeEventListener('click', resetFiltersBtnHandler);
                        resetFiltersBtn.addEventListener('click', resetFiltersBtnHandler);
                        console.log("成功绑定重置筛选按钮事件");
                    } else {
                        console.warn("未找到重置筛选按钮元素");
                    }
                    
                    // 设置取消添加筛选按钮事件
                    const cancelAddFilterBtn = document.getElementById('cancelAddFilter');
                    if (cancelAddFilterBtn) {
                        // 先移除可能已存在的事件监听器，避免重复绑定
                        cancelAddFilterBtn.removeEventListener('click', cancelAddFilterBtnHandler);
                        cancelAddFilterBtn.addEventListener('click', cancelAddFilterBtnHandler);
                        console.log("成功绑定取消添加筛选按钮事件");
                    } else {
                        console.warn("未找到取消添加筛选按钮元素");
                    }
                    
                    // 如果有已应用的筛选，显示它们
                    updateActiveFiltersDisplay();
                    
                    // 从URL恢复筛选状态
                    if (window.applied_filters) {
                        restoreFiltersFromUrl();
                    }
                    
                    console.log("筛选器功能初始化完成");
                } catch (err) {
                    console.error("初始化筛选器功能时出错:", err);
                }
            }
            
            // 添加筛选表单显示/隐藏切换函数
            function toggleNewFilterForm(e) {
                console.log("添加筛选按钮被点击");
                e.preventDefault();
                const newFilterForm = document.getElementById('newFilterForm');
                if (newFilterForm) {
                    const isHidden = newFilterForm.style.display === 'none';
                    newFilterForm.style.display = isHidden ? 'flex' : 'none';
                    console.log(`筛选表单现在${isHidden ? '显示' : '隐藏'}`);
                } else {
                    console.error("未找到筛选表单元素");
                }
            }
            
            // 添加筛选条件按钮处理函数
            function addFilterBtnHandler(e) {
                console.log("添加此筛选条件按钮被点击");
                e.preventDefault();
                addCurrentFilter();
            }
            
            // 应用筛选按钮处理函数
            function applyFiltersBtnHandler(e) {
                console.log("应用筛选按钮被点击");
                e.preventDefault();
                applyAllFilters();
            }
            
            // 重置筛选按钮处理函数
            function resetFiltersBtnHandler(e) {
                console.log("重置筛选按钮被点击");
                e.preventDefault();
                resetAllFilters();
            }
            
            // 取消添加筛选按钮处理函数
            function cancelAddFilterBtnHandler(e) {
                console.log("取消添加筛选按钮被点击");
                e.preventDefault();
                const newFilterForm = document.getElementById('newFilterForm');
                const filterColumn = document.getElementById('filterColumn');
                const filterValueContainer = document.getElementById('filterValueContainer');
                
                if (newFilterForm) {
                    // 重置并隐藏添加表单
                    if (filterColumn) filterColumn.value = '';
                    if (filterValueContainer) filterValueContainer.innerHTML = '';
                    newFilterForm.style.display = 'none';
                    
                    // 重置添加筛选按钮的状态
                    const addThisFilterBtn = document.getElementById('addThisFilter');
                    if (addThisFilterBtn) {
                        addThisFilterBtn.textContent = '添加此筛选';
                        addThisFilterBtn.removeAttribute('data-mode');
                        addThisFilterBtn.removeAttribute('data-column');
                    }
                }
            }
            
            // 暴露数据到window对象，供筛选功能使用
            if (typeof csv_headers !== 'undefined') {
                window.csv_headers = csv_headers;
            }
            if (typeof product_data !== 'undefined') {
                window.product_data = product_data;
            }

            // 等待数据加载完成后再初始化筛选器
            let waitAttempts = 0;
            const maxWaitAttempts = 50; // 最多等待5秒

            function waitForDataAndInitialize() {
                waitAttempts++;

                console.log(`⏳ 等待数据加载... (尝试 ${waitAttempts}/${maxWaitAttempts})`);
                console.log("当前数据状态:", {
                    csv_headers: window.csv_headers ? window.csv_headers.length + ' 列' : '未定义',
                    product_data: window.product_data ? window.product_data.length + ' 条' : '未定义',
                    selected_datasource_info: window.selected_datasource_info ? '已加载' : '未定义'
                });

                // 检查必要的数据是否已加载
                if (window.csv_headers && window.csv_headers.length > 0) {
                    console.log("✅ 数据已加载，开始初始化筛选器");
                    initializeFilters();
                } else if (waitAttempts >= maxWaitAttempts) {
                    console.warn("❌ 等待数据超时，强制初始化筛选器");
                    initializeFilters();
                } else {
                    // 如果数据还没加载，等待一段时间后重试
                    setTimeout(waitForDataAndInitialize, 100);
                }
            }

            function initializeFilters() {
                try {
                    // 初始化紧凑型筛选器
                    initCompactFilter();

                    // 检查URL筛选参数并应用
                    restoreFiltersFromUrl();

                    // 将activeFilters暴露到全局，以便调试
                    window.activeFilters = activeFilters;

                    console.log("✅ 筛选器初始化完成");
                    console.log("💡 使用 debugFilters() 查看调试信息");
                } catch (err) {
                    console.error("❌ 初始化阶段发生错误:", err);
                }
            }
            
            // 添加筛选器调试函数
            window.debugFilters = function() {
                console.log("=== 筛选器调试信息 ===");
                console.log("添加筛选按钮:", document.getElementById('addFilterBtn'));
                console.log("应用筛选按钮:", document.getElementById('applyFilters'));
                console.log("重置筛选按钮:", document.getElementById('resetFilters'));
                console.log("筛选表单:", document.getElementById('newFilterForm'));
                console.log("活跃筛选条件:", window.activeFilters);
                console.log("CSV表头:", window.csv_headers);
                console.log("产品数据:", window.product_data ? window.product_data.length + ' 条' : '未找到');
                console.log("数据源信息:", window.selected_datasource_info);
                console.log("========================");
            };

            // 添加强制初始化函数，供调试使用
            window.forceInitFilters = function() {
                console.log("🔧 强制初始化筛选器...");
                initializeFilters();
            };

            // 将关键函数添加到全局作用域
            window.getUniqueValuesForColumn = function(columnName) {
                console.log("🔍 获取列唯一值:", columnName);
                if (!window.product_data || !Array.isArray(window.product_data)) {
                    console.log("❌ 产品数据不存在或不是数组");
                    return [];
                }

                console.log("📊 产品数据长度:", window.product_data.length);
                console.log("📊 第一个产品:", window.product_data[0]);
                console.log("📊 产品数据类型:", typeof window.product_data);
                console.log("📊 产品数据完整内容:", JSON.stringify(window.product_data.slice(0, 2), null, 2));

                // 检查数据结构
                if (window.product_data.length > 0) {
                    const firstProduct = window.product_data[0];
                    console.log("📊 第一个产品的所有键:", Object.keys(firstProduct));
                    console.log("📊 第一个产品的值:", Object.values(firstProduct));
                    console.log("📊 查找的列名:", columnName);
                    console.log("📊 列名是否存在:", columnName in firstProduct);
                }

                const values = new Set();
                window.product_data.forEach((item, index) => {
                    if (item && columnName in item) {
                        values.add(item[columnName]);
                        if (index < 3) {
                            console.log(`产品${index}的${columnName}:`, item[columnName]);
                        }
                    } else if (index < 3) {
                        console.log(`产品${index}缺少字段${columnName}，可用字段:`, Object.keys(item || {}));
                    }
                });

                const result = Array.from(values).filter(v => v !== null && v !== undefined && v !== '').sort();
                console.log("✅ 找到唯一值:", result);
                return result;
            };

            window.createCategoryControls = function(columnName) {
                console.log("🎛️ 创建分类控件:", columnName);
                const uniqueValues = window.getUniqueValuesForColumn(columnName);
                const filterValueContainer = document.getElementById('filterValueContainer');

                if (!filterValueContainer) {
                    console.error("❌ 未找到filterValueContainer");
                    return;
                }

                console.log("📋 唯一值列表:", uniqueValues);

                if (uniqueValues.length === 0) {
                    filterValueContainer.innerHTML = '<div class="no-options">该字段暂无可选项</div>';
                    return;
                }

                filterValueContainer.className = 'filter-value-container multi-select';
                filterValueContainer.innerHTML = `
                    <div class="multi-select-actions">
                        <span class="select-all" data-column="${columnName}">全选</span>
                        <span class="deselect-all" data-column="${columnName}">反选</span>
                    </div>
                    <div class="multi-select-options" id="options_${columnName}"></div>
                `;

                const optionsContainer = filterValueContainer.querySelector(`#options_${columnName}`);

                uniqueValues.forEach(value => {
                    const option = document.createElement('div');
                    option.className = 'multi-select-option';
                    option.innerHTML = `
                        <input type="checkbox" id="option_${columnName}_${value}" value="${value}" checked>
                        <label for="option_${columnName}_${value}">${value}</label>
                    `;
                    optionsContainer.appendChild(option);
                });

                console.log("✅ 分类控件创建完成");
            };

            // 从页面中提取产品数据的函数
            window.extractProductDataFromPage = function() {
                const products = [];

                try {
                    // 查找所有产品卡片
                    const productCards = document.querySelectorAll('.product-card, [data-product-info]');
                    console.log("🔍 找到产品卡片:", productCards.length);

                    productCards.forEach((card, index) => {
                        try {
                            // 尝试从data属性中获取产品信息
                            const productInfo = card.getAttribute('data-product-info');
                            if (productInfo) {
                                const product = JSON.parse(productInfo);
                                products.push(product);
                                return;
                            }

                            // 如果没有data属性，尝试从DOM中提取信息
                            const product = {};

                            // 提取图片链接
                            const img = card.querySelector('img');
                            if (img) {
                                product['图片链接'] = img.src;
                            }

                            // 提取文本信息（这里需要根据实际页面结构调整）
                            const textElements = card.querySelectorAll('[data-field]');
                            textElements.forEach(el => {
                                const field = el.getAttribute('data-field');
                                if (field) {
                                    product[field] = el.textContent.trim();
                                }
                            });

                            // 如果提取到了一些信息，就添加到产品列表
                            if (Object.keys(product).length > 0) {
                                products.push(product);
                            }
                        } catch (e) {
                            console.warn("提取产品信息失败:", e, card);
                        }
                    });

                    // 如果没有找到产品卡片，创建一些示例数据用于测试
                    if (products.length === 0) {
                        console.log("⚠️ 未找到产品数据，创建示例数据");
                        const sampleData = [
                            { '品类': '翡翠', '价格带': '1-3K', '佩戴方式': '吊坠', 'spu': 'SPU001', '6月加购数': '10' },
                            { '品类': '和田玉', '价格带': '3-5K', '佩戴方式': '手镯', 'spu': 'SPU002', '6月加购数': '15' },
                            { '品类': '南红', '价格带': '1K以下', '佩戴方式': '戒指', 'spu': 'SPU003', '6月加购数': '8' },
                            { '品类': '翡翠', '价格带': '5-10K', '佩戴方式': '吊坠', 'spu': 'SPU004', '6月加购数': '20' },
                            { '品类': '18K金', '价格带': '10-15K', '佩戴方式': '手串', 'spu': 'SPU005', '6月加购数': '5' }
                        ];
                        products.push(...sampleData);
                    }

                } catch (error) {
                    console.error("提取产品数据时出错:", error);
                }

                return products;
            };

            // 添加手动加载数据源信息的函数
            window.loadDataSourceInfo = async function() {
                const urlParams = new URLSearchParams(window.location.search);
                const datasourceId = urlParams.get('selected_datasource_id');

                if (!datasourceId) {
                    console.log("❌ 未找到数据源ID");
                    return false;
                }

                try {
                    console.log("🔄 尝试加载数据源信息...", datasourceId);

                    // 尝试从页面中提取产品数据
                    if (!window.product_data || window.product_data.length === 0) {
                        console.log("🔄 尝试从页面提取产品数据...");
                        window.product_data = extractProductDataFromPage();
                        console.log("✅ 提取到产品数据:", window.product_data.length, "个产品");
                    }

                    // 设置headers
                    if (!window.csv_headers || window.csv_headers.length === 0) {
                        // 根据URL中的轴信息推断可能的headers
                        const xAxis = urlParams.get('selected_x_axis');
                        const yAxis = urlParams.get('selected_y_axis');

                        const inferredHeaders = [];
                        if (xAxis) inferredHeaders.push(xAxis);
                        if (yAxis) inferredHeaders.push(yAxis);

                        // 添加一些常见的字段
                        inferredHeaders.push('品类', '价格带', '佩戴方式', '图片链接', 'spu', '6月加购数');

                        // 去重
                        window.csv_headers = [...new Set(inferredHeaders)];
                        console.log("✅ 推断的CSV headers:", window.csv_headers);
                    }

                    return true;
                } catch (error) {
                    console.error("❌ 加载数据源信息失败:", error);
                    return false;
                }
            };

            // 简化的初始化逻辑
            console.log("🚀 开始初始化筛选器...");

            // 检查数据状态并尝试加载
            if (!window.csv_headers || window.csv_headers.length === 0) {
                console.log("⚠️ CSV headers未定义，尝试加载数据源信息");
                window.loadDataSourceInfo().then((loaded) => {
                    if (loaded) {
                        console.log("✅ 数据源信息加载成功，重新初始化筛选器");
                        initializeFilters();
                    } else {
                        console.log("⚠️ 无法加载数据源信息，使用默认初始化");
                        window.csv_headers = window.csv_headers || [];
                        window.product_data = window.product_data || [];
                        window.selected_datasource_info = window.selected_datasource_info || null;
                        initializeFilters();
                    }
                });
            } else {
                console.log("✅ 数据已存在，直接初始化");
                initializeFilters();
            }

            // 监听数据准备就绪事件（如果数据后来加载）
            window.addEventListener('dataReady', function(event) {
                console.log('🎉 收到数据准备就绪事件，重新初始化');
                initializeFilters();
            });

            // 页面完全加载后再次尝试
            window.addEventListener('load', function() {
                console.log('📄 页面完全加载，再次检查初始化');
                setTimeout(() => {
                    if (!window.activeFilters || Object.keys(window.activeFilters).length === 0) {
                        console.log("🔄 页面加载完成后重新初始化筛选器");
                        initializeFilters();
                    }
                }, 500);
            });

            // ============== 标签配置功能 ==============
            // 默认标签配置 - 初始化为空数组
            let tagConfigurations = [];
            
            // 清空所有标签配置的函数
            function clearAllTagConfigurations() {
                tagConfigurations = [];
                localStorage.removeItem('tagConfigurations');
                console.log("已清空所有标签配置");
                
                // 清除现有标签显示
                document.querySelectorAll('.product-tag').forEach(tag => tag.remove());
                document.querySelectorAll('.product-tags-bottom').forEach(container => container.remove());
                
                // 重新初始化标签配置界面
                if (typeof initTagConfigurations === 'function') {
                    initTagConfigurations();
                }
                
                return true;
            }
            
            // 清理无效的标签配置
            function cleanupTagConfigurations() {
                if (!window.csv_headers || !window.csv_headers.length) {
                    console.log("CSV headers 不存在，无法清理标签配置");
                    return;
                }
                
                const originalCount = tagConfigurations.length;
                
                // 过滤出有效的配置
                const validConfigs = tagConfigurations.filter(config => {
                    // 检查字段名是否存在且不为空
                    if (!config.fieldName || config.fieldName.trim() === '') {
                        console.log(`清理无效配置: ${config.id} - 字段名为空`);
                        return false;
                    }
                    
                    // 检查字段名是否在CSV headers中存在
                    if (!window.csv_headers.includes(config.fieldName)) {
                        console.log(`清理无效配置: ${config.id} - 字段名 "${config.fieldName}" 不存在于数据源中`);
                        return false;
                    }
                    
                    return true;
                });
                
                if (validConfigs.length !== originalCount) {
                    console.log(`清理了${originalCount - validConfigs.length}个无效标签配置`);
                    tagConfigurations = validConfigs;
                    localStorage.setItem('tagConfigurations', JSON.stringify(tagConfigurations));
                    
                    // 重新初始化标签配置界面
                    if (typeof initTagConfigurations === 'function') {
                        initTagConfigurations();
                    }
                    
                    // 显示清理结果
                    if (validConfigs.length === 0) {
                        console.warn("所有标签配置都已被清理，请重新配置标签");
                    } else {
                        console.log(`保留了${validConfigs.length}个有效的标签配置`);
                    }
                } else {
                    console.log("所有标签配置都是有效的，无需清理");
                }
            }
            
            // 标签配置容器
            const tagConfigList = document.getElementById('tagConfigList');
            const addTagConfigBtn = document.getElementById('addTagConfigBtn');
            
            // 获取可用字段列表
            function getAvailableFields() {
                if (window.csv_headers && window.csv_headers.length > 0) {
                    return window.csv_headers;
                }
                return ['品牌', '类别', 'SPU编号']; // 默认字段，如果没有数据源字段
            }
            
            // 创建标签配置项
            function createTagConfigItem(config = {}) {
                const id = config.id || 'tag_' + new Date().getTime();
                const enabled = config.enabled !== undefined ? config.enabled : true;
                const fieldName = config.fieldName || '';
                const color = config.color || '#1a73e8';
                const position = config.position || 'right-top';
                const displayMode = config.displayMode || 'value'; // 新增：显示模式，默认为显示字段值
                const tagDisplayMode = config.tagDisplayMode || 'bottom'; // 新增：标签显示模式，默认为底部标签
                
                // 预定义的颜色选项
                const colorOptions = [
                    { value: '#1a73e8', name: '蓝色' },
                    { value: '#34a853', name: '绿色' },
                    { value: '#ea4335', name: '红色' },
                    { value: '#fbbc04', name: '黄色' },
                    { value: '#673ab7', name: '紫色' },
                    { value: '#ff6d01', name: '橙色' },
                    { value: '#4285f4', name: '天蓝' },
                    { value: '#0f9d58', name: '墨绿' }
                ];
                
                // 生成颜色选项HTML
                const colorOptionsHtml = colorOptions.map(opt => 
                    `<option value="${opt.value}" ${color === opt.value ? 'selected' : ''}>${opt.name}</option>`
                ).join('');
                
                const tagItem = document.createElement('div');
                tagItem.className = 'tag-config-item';
                tagItem.setAttribute('data-tag-id', id);
                
                // 生成字段选项
                const fieldOptions = getAvailableFields().map(field => 
                    `<option value="${field}" ${field === fieldName ? 'selected' : ''}>${field}</option>`
                ).join('');
                
                tagItem.innerHTML = `
                    <div class="tag-config-header">
                        <label class="tag-switch">
                            <input type="checkbox" class="tag-switch-input" ${enabled ? 'checked' : ''}>
                            <span class="tag-switch-slider"></span>
                        </label>
                        <button class="tag-remove-btn">删除</button>
                    </div>
                    <div class="tag-config-field">
                        <span class="tag-config-field-label">字段名</span>
                        <div class="tag-config-field-input">
                            <select class="tag-config-select field-select">
                                <option value="">-- 请选择字段 --</option>
                                ${fieldOptions}
                            </select>
                        </div>
                    </div>
                    <div class="tag-config-field">
                        <span class="tag-config-field-label">标签位置</span>
                        <div class="tag-config-field-input">
                            <select class="tag-config-select tag-display-mode-select">
                                <option value="bottom" ${tagDisplayMode === 'bottom' ? 'selected' : ''}>底部标签</option>
                                <option value="corner" ${tagDisplayMode === 'corner' ? 'selected' : ''}>角标</option>
                            </select>
                            <div class="display-mode-info">
                                ${tagDisplayMode === 'bottom' ? '标签将显示在卡片底部' : '标签将显示为角标'}
                            </div>
                        </div>
                    </div>
                    <div class="tag-config-field corner-position-field" style="display: ${tagDisplayMode === 'corner' ? 'flex' : 'none'};">
                        <span class="tag-config-field-label">角标位置</span>
                        <div class="tag-config-field-input">
                            <div style="display: flex; align-items: center;">
                                <button type="button" class="tag-position-btn ${position === 'left-top' ? 'active' : ''}" data-position="left-top">左上角</button>
                                <button type="button" class="tag-position-btn ${position === 'right-top' ? 'active' : ''}" data-position="right-top">右上角</button>
                            </div>
                        </div>
                    </div>
                    <div class="tag-config-field">
                        <span class="tag-config-field-label">显示内容</span>
                        <div class="tag-config-field-input">
                            <select class="tag-config-select display-mode-select">
                                <option value="value" ${displayMode === 'value' ? 'selected' : ''}>显示字段值</option>
                                <option value="rank" ${displayMode === 'rank' ? 'selected' : ''}>显示排名</option>
                                <option value="label-value" ${displayMode === 'label-value' ? 'selected' : ''}>显示"字段名:值"</option>
                                <option value="custom" ${displayMode === 'custom' ? 'selected' : ''}>自定义文本</option>
                            </select>
                        </div>
                    </div>
                    <div class="tag-config-field">
                        <span class="tag-config-field-label">颜色</span>
                        <div class="tag-config-field-input">
                            <select class="tag-config-select color-select">
                                ${colorOptionsHtml}
                            </select>
                            <div class="current-color">
                                <span class="color-preview" style="background-color: ${color}"></span>
                                <span>当前选择: ${colorOptions.find(opt => opt.value === color)?.name || '自定义'}</span>
                            </div>
                        </div>
                    </div>
                `;
                
                // 绑定事件处理函数
                
                // 开关状态变化
                const switchInput = tagItem.querySelector('.tag-switch-input');
                switchInput.addEventListener('change', function() {
                    updateTagConfiguration(id, {enabled: this.checked});
                });
                
                // 字段选择变化
                const fieldSelect = tagItem.querySelector('.field-select');
                fieldSelect.addEventListener('change', function() {
                    updateTagConfiguration(id, {fieldName: this.value});
                });

                // 标签显示模式变化
                const tagDisplayModeSelect = tagItem.querySelector('.tag-display-mode-select');
                const cornerPositionField = tagItem.querySelector('.corner-position-field');
                const displayModeInfo = tagItem.querySelector('.display-mode-info');
                
                tagDisplayModeSelect.addEventListener('change', function() {
                    const newTagDisplayMode = this.value;
                    updateTagConfiguration(id, {tagDisplayMode: newTagDisplayMode});
                    
                    // 控制角标位置字段的显示/隐藏
                    cornerPositionField.style.display = newTagDisplayMode === 'corner' ? 'flex' : 'none';
                    
                    // 更新说明文字
                    displayModeInfo.textContent = newTagDisplayMode === 'bottom' ? '标签将显示在卡片底部' : '标签将显示为角标';
                });
                
                // 显示内容模式变化
                const displayModeSelect = tagItem.querySelector('.display-mode-select');
                displayModeSelect.addEventListener('change', function() {
                    const newDisplayMode = this.value;
                    updateTagConfiguration(id, {displayMode: newDisplayMode});
                    
                    // 处理自定义文本输入框的显示/隐藏
                    handleCustomTextFieldVisibility(tagItem, newDisplayMode, config.customText || '{value}');
                });
                
                // 如果初始显示模式是自定义文本，确保自定义文本输入框已显示
                if (displayMode === 'custom') {
                    setTimeout(() => {
                        handleCustomTextFieldVisibility(tagItem, 'custom', config.customText || '{value}');
                    }, 0);
                }
                
                // 颜色变化
                const colorSelect = tagItem.querySelector('.color-select');
                colorSelect.addEventListener('change', function() {
                    const selectedColor = this.value;
                    updateTagConfiguration(id, {color: selectedColor});
                    
                    // 更新颜色预览
                    const colorPreview = tagItem.querySelector('.color-preview');
                    if (colorPreview) {
                        colorPreview.style.backgroundColor = selectedColor;
                    }
                    
                    // 更新颜色名称
                    const colorName = tagItem.querySelector('.current-color span:last-child');
                    if (colorName) {
                        const selectedOption = colorOptions.find(opt => opt.value === selectedColor);
                        colorName.textContent = `当前选择: ${selectedOption ? selectedOption.name : '自定义'}`;
                    }
                });
                
                // 角标位置按钮点击
                const positionBtns = tagItem.querySelectorAll('.tag-position-btn');
                positionBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        positionBtns.forEach(b => b.classList.remove('active'));
                        this.classList.add('active');
                        updateTagConfiguration(id, {position: this.getAttribute('data-position')});
                    });
                });
                
                // 删除按钮点击
                const removeBtn = tagItem.querySelector('.tag-remove-btn');
                removeBtn.addEventListener('click', function() {
                    if (confirm('确定要删除这个标签配置吗？')) {
                        removeTagConfiguration(id);
                        tagItem.remove();
                    }
                });
                
                return tagItem;
            }
            
            // 更新标签配置
            function updateTagConfiguration(id, updates) {
                const configIndex = tagConfigurations.findIndex(config => config.id === id);
                
                if (configIndex !== -1) {
                    tagConfigurations[configIndex] = {...tagConfigurations[configIndex], ...updates};
                } else {
                    tagConfigurations.push({id, ...updates});
                }
                
                // 保存到本地存储
                localStorage.setItem('tagConfigurations', JSON.stringify(tagConfigurations));
                
                // 如果产品网格已存在，更新显示
                if (typeof updateProductTagsDisplay === 'function') {
                    updateProductTagsDisplay();
                }
            }
            
            // 删除标签配置
            function removeTagConfiguration(id) {
                tagConfigurations = tagConfigurations.filter(config => config.id !== id);
                
                // 保存到本地存储
                localStorage.setItem('tagConfigurations', JSON.stringify(tagConfigurations));
                
                // 如果产品网格已存在，更新显示
                if (typeof updateProductTagsDisplay === 'function') {
                    updateProductTagsDisplay();
                }
            }
            
            // 添加新标签配置
            function addNewTagConfiguration() {
                const newConfig = {
                    id: 'tag_' + new Date().getTime(),
                    enabled: true,
                    fieldName: '',
                    displayMode: 'value',
                    customText: '{value}',
                    color: '#1a73e8', // 使用预设的蓝色作为默认值
                    position: 'right-top',
                    tagDisplayMode: 'bottom' // 默认使用底部标签模式
                };
                
                tagConfigurations.push(newConfig);
                localStorage.setItem('tagConfigurations', JSON.stringify(tagConfigurations));
                
                const newTagItem = createTagConfigItem(newConfig);
                tagConfigList.appendChild(newTagItem);
            }
            
            // 初始化标签配置
            function initTagConfigurations() {
                if (tagConfigList) {
                    // 清空配置列表
                    tagConfigList.innerHTML = '';
                    
                    // 添加已保存的配置
                    if (tagConfigurations.length > 0) {
                        tagConfigurations.forEach(config => {
                            const tagItem = createTagConfigItem(config);
                            tagConfigList.appendChild(tagItem);
                        });
                    }
                    
                    // 绑定添加按钮事件
                    if (addTagConfigBtn) {
                        addTagConfigBtn.removeEventListener('click', addNewTagConfiguration);
                        addTagConfigBtn.addEventListener('click', addNewTagConfiguration);
                    }
                    
                    // 绑定清空所有标签按钮事件
                    const clearAllTagsBtn = document.getElementById('clearAllTagsBtn');
                    if (clearAllTagsBtn) {
                        clearAllTagsBtn.removeEventListener('click', handleClearAllTags);
                        clearAllTagsBtn.addEventListener('click', handleClearAllTags);
                    }
                }
            }
            
            // 处理清空所有标签按钮点击
            function handleClearAllTags() {
                if (confirm('确定要清空所有标签配置吗？此操作无法撤销。')) {
                    clearAllTagConfigurations();
                    alert('所有标签配置已清空！');
                }
            }
            
            // 更新产品卡片上的标签显示
            function updateProductTagsDisplay() {
                console.log("开始更新产品标签显示...");
                const productCards = document.querySelectorAll('.product-card');
                
                if (!productCards.length) {
                    console.log("未找到产品卡片，无法更新标签");
                    return;
                }
                
                // 清除现有标签和底部标签容器
                document.querySelectorAll('.product-tag').forEach(tag => tag.remove());
                document.querySelectorAll('.product-tags-bottom').forEach(container => container.remove());
                
                // 验证数据源配置
                if (!window.csv_headers || !window.csv_headers.length) {
                    console.warn("CSV headers 不存在，无法验证标签配置");
                    return;
                }
                
                if (!window.product_data || !window.product_data.length) {
                    console.warn("产品数据不存在，无法应用标签");
                    return;
                }
                
                console.log("数据验证通过:", {
                    headers_count: window.csv_headers.length,
                    product_count: window.product_data.length,
                    cards_count: productCards.length
                });
                
                // 过滤出启用且有效的标签配置
                const enabledConfigs = tagConfigurations.filter(config => {
                    if (!config.enabled) {
                        console.log(`标签配置 ${config.id} 已禁用，跳过`);
                        return false;
                    }
                    
                    if (!config.fieldName || config.fieldName.trim() === '') {
                        console.warn(`标签配置 ${config.id} 字段名为空，跳过`);
                        return false;
                    }
                    
                    if (!window.csv_headers.includes(config.fieldName)) {
                        console.warn(`标签配置 ${config.id} 字段名 "${config.fieldName}" 在数据源中不存在，跳过`);
                        return false;
                    }
                    
                    return true;
                });
                
                if (!enabledConfigs.length) {
                    console.log("没有有效的标签配置");
                    return;
                }
                
                console.log(`找到${productCards.length}个产品卡片，应用${enabledConfigs.length}个有效标签配置`);
                
                // 获取索引列名
                const indexColumnName = window.selected_datasource_info?.index_column;
                console.log("索引列名:", indexColumnName);
                
                // 统计匹配情况
                let matchedCount = 0;
                let unmatchedCount = 0;
                
                // 对每个产品卡片添加标签
                productCards.forEach((card, cardIndex) => {
                    let productData = card.productData; // 首先尝试从已附加的数据获取
                    
                    // 如果卡片上没有附加数据，尝试重新匹配
                    if (!productData && window.product_data && window.product_data.length > 0) {
                        // 优先方法：通过data-product-index属性精确匹配
                        const productIndex = card.getAttribute('data-product-index');
                        console.log(`卡片${cardIndex} data-product-index:`, productIndex);
                        
                        if (productIndex && indexColumnName && productIndex.trim() !== '') {
                            productData = window.product_data.find(item => {
                                return String(item[indexColumnName]) === String(productIndex);
                            });
                            
                            if (productData) {
                                console.log(`卡片${cardIndex} 通过索引匹配成功:`, productIndex);
                            } else {
                                console.warn(`卡片${cardIndex} 索引匹配失败，索引值: "${productIndex}"`);
                            }
                        } else {
                            console.warn(`卡片${cardIndex} 缺少有效的索引信息:`, {
                                productIndex: productIndex,
                                indexColumnName: indexColumnName,
                                isEmpty: !productIndex || productIndex.trim() === ''
                            });
                        }
                        
                        // 备用方法：通过价格匹配（当索引匹配失败时）
                        if (!productData) {
                            const priceElement = card.querySelector('.product-price');
                            if (priceElement) {
                                const priceText = priceElement.textContent.replace(/[￥¥,]/g, '').trim();
                                const cardPrice = parseFloat(priceText);
                                
                                if (!isNaN(cardPrice)) {
                                    const priceColumnName = window.selected_datasource_info?.product_price_column;
                                    if (priceColumnName) {
                                        productData = window.product_data.find(item => {
                                            const itemPrice = parseFloat(item[priceColumnName]);
                                            return Math.abs(itemPrice - cardPrice) < 0.01; // 价格匹配（考虑浮点精度）
                                        });
                                        
                                        if (productData) {
                                            console.log(`卡片${cardIndex} 通过价格匹配成功:`, cardPrice);
                                        }
                                    }
                                }
                            }
                        }
                        
                        // 将匹配到的数据附加到卡片上，以便下次使用
                        if (productData) {
                            card.productData = productData;
                            matchedCount++;
                        } else {
                            unmatchedCount++;
                        }
                    } else if (productData) {
                        matchedCount++;
                    } else {
                        unmatchedCount++;
                    }
                    
                    if (!productData) {
                        console.warn(`卡片${cardIndex}缺少产品数据，跳过标签添加`);
                        return;
                    }
                    
                    // 分别收集底部标签和角标
                    const bottomTags = [];
                    const cornerTags = [];
                    
                    enabledConfigs.forEach(config => {
                        const fieldValue = productData[config.fieldName];
                        
                        // 确保字段值存在且不为空
                        if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
                            let displayText = '';
                            let tagColor = config.color; // 默认使用配置中的颜色
                            
                            // 根据显示模式计算标签内容
                            switch (config.displayMode || 'value') {
                                case 'value':
                                    // 直接显示字段值
                                    displayText = fieldValue;
                                    break;
                                    
                                case 'rank':
                                    // 计算排名
                                    if (window.product_data && window.product_data.length > 0) {
                                        // 根据字段值对产品进行排序
                                        const sortedProducts = [...window.product_data].sort((a, b) => {
                                            const valA = a[config.fieldName];
                                            const valB = b[config.fieldName];
                                            
                                            // 处理数字和字符串排序
                                            if (!isNaN(valA) && !isNaN(valB)) {
                                                return Number(valB) - Number(valA); // 从大到小排序
                                            }
                                            return String(valA).localeCompare(String(valB));
                                        });
                                        
                                        // 找出当前产品的排名
                                        const rank = sortedProducts.findIndex(p => 
                                            p[config.fieldName] === fieldValue
                                        ) + 1; // 索引从0开始，排名从1开始
                                        
                                        displayText = `第${rank}名`;
                                    } else {
                                        displayText = '排名计算中';
                                    }
                                    break;
                                    
                                case 'label-value':
                                    // 显示"字段名:值"格式
                                    displayText = `${config.fieldName}: ${fieldValue}`;
                                    break;
                                    
                                case 'custom':
                                    // 自定义文本，使用{value}代表字段值，并支持简单规则
                                    if (config.customText) {
                                        try {
                                            let customText = config.customText;
                                            
                                            // 处理颜色规则：{color:#FF0000:文本}
                                            customText = customText.replace(/{color:([^:]+):([^}]+)}/g, (match, color, text) => {
                                                tagColor = color.trim();
                                                return text;
                                            });
                                            
                                            // 处理条件颜色规则：{if-color:条件:颜色1:颜色2}
                                            customText = customText.replace(/{if-color:([^:]+):([^:]+):([^}]+)}/g, (match, condition, trueColor, falseColor) => {
                                                // 替换condition中的{value}
                                                const processedCondition = condition.replace(/{value}/g, fieldValue);
                                                
                                                // 评估条件
                                                let result = evaluateCondition(processedCondition);
                                                
                                                // 设置颜色
                                                tagColor = result ? trueColor.trim() : falseColor.trim();
                                                return '';
                                            });
                                            
                                            // 处理条件规则：{if:条件:文本1:文本2}
                                            customText = customText.replace(/{if:([^:]+):([^:]+):([^}]+)}/g, (match, condition, trueText, falseText) => {
                                                // 替换condition中的{value}
                                                const processedCondition = condition.replace(/{value}/g, fieldValue);
                                                
                                                // 评估条件
                                                let result = evaluateCondition(processedCondition);
                                                
                                                return result ? trueText : falseText;
                                            });
                                            
                                            // 处理格式化规则：{format:价格￥{value}}
                                            customText = customText.replace(/{format:([^}]+)}/g, (match, format) => {
                                                return format.replace(/{value}/g, fieldValue);
                                            });
                                            
                                            // 处理数字格式化：{number:{value}:2}
                                            customText = customText.replace(/{number:([^:]+):(\d+)}/g, (match, numStr, decimals) => {
                                                const num = parseFloat(numStr.replace(/{value}/g, fieldValue));
                                                return isNaN(num) ? numStr : num.toFixed(parseInt(decimals));
                                            });
                                            
                                            // 最后替换所有{value}
                                            displayText = customText.replace(/{value}/g, fieldValue);
                } catch (e) {
                                            console.error('处理自定义文本规则出错:', e);
                                            displayText = fieldValue;
                                        }
                                    } else {
                                        displayText = fieldValue;
                                    }
                                    break;
                                    
                                default:
                                    displayText = fieldValue;
                            }
                            
                            // 根据标签显示模式决定放入哪个数组
                            const tagDisplayMode = config.tagDisplayMode || 'bottom';
                            
                            if (tagDisplayMode === 'corner') {
                                // 角标模式：创建角标元素
                                const tag = document.createElement('div');
                                tag.className = `product-tag ${config.position || 'right-top'}`;
                                tag.style.backgroundColor = tagColor;
                                tag.textContent = displayText;
                                cornerTags.push(tag);
                            } else {
                                // 底部标签模式：收集标签信息
                                bottomTags.push({
                                    text: displayText,
                                    color: tagColor
                                });
                            }
                        } else {
                            console.log(`卡片${cardIndex} 字段 "${config.fieldName}" 值为空，跳过标签`);
                        }
                    });
                    
                    // 添加角标到卡片
                    cornerTags.forEach(tag => {
                        card.appendChild(tag);
                    });
                    
                    // 添加底部标签容器和标签
                    if (bottomTags.length > 0) {
                        // 创建底部标签容器
                        const bottomContainer = document.createElement('div');
                        bottomContainer.className = 'product-tags-bottom';
                        
                        // 添加底部标签
                        bottomTags.forEach(tagInfo => {
                            const tag = document.createElement('div');
                            tag.className = 'product-tag bottom';
                            tag.style.backgroundColor = tagInfo.color;
                            tag.textContent = tagInfo.text;
                            bottomContainer.appendChild(tag);
                        });
                        
                        // 将底部标签容器添加到卡片末尾
                        card.appendChild(bottomContainer);
                    }
                });
                
                // 输出匹配统计
                console.log(`标签映射完成: ${matchedCount} 个卡片匹配成功, ${unmatchedCount} 个卡片匹配失败`);
                
                if (unmatchedCount > 0) {
                    console.warn("部分产品卡片无法匹配到数据，可能的原因:");
                    console.warn("1. 索引列配置不正确或为空");
                    console.warn("2. 产品数据与卡片显示的数据不一致");
                    console.warn("3. data-product-index 属性缺失或值不匹配");
                    
                    // 提供修复建议
                    if (!indexColumnName) {
                        console.error("❌ 索引列未配置！请在数据源设置中配置索引列。");
                    } else {
                        console.log("✅ 索引列已配置:", indexColumnName);
                    }
                }
            }
            
            // 辅助函数：评估简单条件
            function evaluateCondition(condition) {
                try {
                    if (condition.includes('>')) {
                        const [left, right] = condition.split('>').map(s => s.trim());
                        return Number(left) > Number(right);
                    } else if (condition.includes('<')) {
                        const [left, right] = condition.split('<').map(s => s.trim());
                        return Number(left) < Number(right);
                    } else if (condition.includes('==')) {
                        const [left, right] = condition.split('==').map(s => s.trim());
                        return String(left) === String(right);
                    } else if (condition.includes('!=')) {
                        const [left, right] = condition.split('!=').map(s => s.trim());
                        return String(left) !== String(right);
                    } else if (condition.includes('≥') || condition.includes('>=')) {
                        const [left, right] = condition.split(/≥|>=/).map(s => s.trim());
                        return Number(left) >= Number(right);
                    } else if (condition.includes('≤') || condition.includes('<=')) {
                        const [left, right] = condition.split(/≤|<=/).map(s => s.trim());
                        return Number(left) <= Number(right);
                    } else {
                        // 如果没有操作符，则检查条件是否为true
                        return Boolean(condition);
                    }
                } catch (e) {
                    console.error('条件评估错误:', e);
                    return false;
                }
            }

            // 在文档加载完成后初始化标签配置
            try {
                // 从本地存储加载标签配置
                const savedConfigs = localStorage.getItem('tagConfigurations');
                if (savedConfigs) {
                    try {
                        tagConfigurations = JSON.parse(savedConfigs);
                        console.log("从localStorage加载标签配置:", tagConfigurations);
                    } catch (e) {
                        console.error("解析标签配置失败:", e);
                        tagConfigurations = [];
                    }
                } else {
                    tagConfigurations = [];
                }

                // 等待数据加载完成后再初始化标签功能
                let tagWaitAttempts = 0;
                const maxTagWaitAttempts = 50;

                function waitForDataAndInitializeTags() {
                    tagWaitAttempts++;

                    if (window.csv_headers && window.csv_headers.length > 0) {
                        console.log("✅ 数据已加载，开始初始化标签功能");

                        // 清理无效的标签配置
                        if (tagConfigurations.length > 0) {
                            cleanupTagConfigurations();
                        }

                        initTagConfigurations();

                        // 页面完全加载后应用标签显示
                        window.addEventListener('load', function() {
                            setTimeout(() => {
                                if (typeof updateProductTagsDisplay === 'function') {
                                    updateProductTagsDisplay();
                                }
                            }, 500);
                        });
                    } else if (tagWaitAttempts >= maxTagWaitAttempts) {
                        console.warn("❌ 等待数据超时，强制初始化标签功能");
                        initTagConfigurations();
                    } else {
                        console.log(`⏳ 等待数据加载以初始化标签功能... (尝试 ${tagWaitAttempts}/${maxTagWaitAttempts})`);
                        setTimeout(waitForDataAndInitializeTags, 100);
                    }
                }

                // 开始等待数据并初始化标签功能
                waitForDataAndInitializeTags();
                
                // 监听产品卡片创建事件
                document.addEventListener('productCardsCreated', function() {
                    console.log("检测到产品卡片创建完成，应用标签显示");
                    setTimeout(() => {
                        if (typeof updateProductTagsDisplay === 'function') {
                            updateProductTagsDisplay();
                        }
                    }, 200);
                });
                
            } catch (err) {
                console.error("初始化标签配置功能时出错:", err);
            }

            // 处理自定义文本字段显示/隐藏
            function handleCustomTextFieldVisibility(tagItem, displayMode, defaultText = '{value}') {
                // 移除现有的自定义文本字段（如果有）
                const existingField = tagItem.querySelector('.custom-text-field');
                if (existingField) {
                    existingField.remove();
                }
                
                // 如果显示模式是自定义文本，添加自定义文本输入框
                if (displayMode === 'custom') {
                    const customTextField = document.createElement('div');
                    customTextField.className = 'tag-config-field custom-text-field';
                    customTextField.innerHTML = `
                        <span class="tag-config-field-label">自定义文本</span>
                        <div class="tag-config-field-input">
                            <input type="text" class="tag-config-input custom-text-input" 
                                value="${defaultText}"
                                placeholder="输入自定义文本，使用{value}代表字段值">
                            <div class="rules-helper">
                                <div class="rules-toggle">显示规则帮助 <span class="toggle-icon">+</span></div>
                                <div class="rules-examples" style="display: none;">
                                    <div class="rule-example">
                                        <code>{value}</code> - 直接显示字段的原始值
                                    </div>
                                    <div class="rule-divider"></div>
                                    
                                    <div class="rule-example">
                                        <code>{if:{value}>100:高价:普通}</code> - 条件判断：如果值大于100显示"高价"，否则显示"普通"
                                    </div>
                                    <div class="rule-divider"></div>
                                    
                                    <div class="rule-example">
                                        <code>{color:#ff0000:文本}</code> - 将"文本"设为红色
                                    </div>
                                    <div class="rule-divider"></div>
                                    
                                    <div class="rule-example">
                                        <code>{if-color:{value}>100:#ff0000:#00ff00}</code> - 条件颜色：如果值大于100使用红色，否则使用绿色
                                    </div>
                                    <div class="rule-divider"></div>
                                    
                                    <div class="rule-example">
                                        <code>{if:{value}>100:高价:低价}{if-color:{value}>100:#ff0000:#00ff00}</code> - 组合使用：根据值设置不同文本和颜色
                                    </div>
                                    <div class="rule-divider"></div>
                                    
                                    <div class="rule-example">
                                        <code>{format:￥{value}}</code> - 在值前添加货币符号等格式文本
                                    </div>
                                    <div class="rule-divider"></div>
                                    
                                    <div class="rule-example">
                                        <code>{number:{value}:2}</code> - 将数字格式化为保留2位小数
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 在显示模式选择器之后插入
                    const displayModeField = tagItem.querySelector('.display-mode-select').closest('.tag-config-field');
                    displayModeField.after(customTextField);
                    
                    // 添加自定义文本变化事件
                    const customTextInput = customTextField.querySelector('.custom-text-input');
                    customTextInput.addEventListener('input', function() {
                        const tagId = tagItem.getAttribute('data-tag-id');
                        updateTagConfiguration(tagId, {customText: this.value});
                    });
                    
                    // 添加规则帮助切换事件
                    const rulesToggle = customTextField.querySelector('.rules-toggle');
                    const rulesExamples = customTextField.querySelector('.rules-examples');
                    const toggleIcon = customTextField.querySelector('.toggle-icon');
                    
                    if (rulesToggle && rulesExamples) {
                        rulesToggle.addEventListener('click', function() {
                            const isVisible = rulesExamples.style.display !== 'none';
                            rulesExamples.style.display = isVisible ? 'none' : 'block';
                            toggleIcon.textContent = isVisible ? '+' : '-';
                        });
                    }
                }
            }
            
            // 暴露函数到全局作用域
            window.updateProductTagsDisplay = updateProductTagsDisplay;

            // 画布诊断功能
            window.diagnosticProductCanvas = function() {
                console.log('=== ProductView2 画布诊断开始 ===');
                
                // 1. 检查数据源状态
                const urlParams = new URLSearchParams(window.location.search);
                const selectedDatasourceId = urlParams.get('selected_datasource_id');
                const selectedXAxis = urlParams.get('selected_x_axis');
                const selectedYAxis = urlParams.get('selected_y_axis');
                
                console.log('URL参数检查:');
                console.log('- 数据源ID:', selectedDatasourceId);
                console.log('- X轴字段:', selectedXAxis);
                console.log('- Y轴字段:', selectedYAxis);
                
                // 2. 检查产品画布DOM结构
                const productCanvas = document.querySelector('#productCanvas') || document.querySelector('.product-canvas');
                console.log('画布DOM元素:', productCanvas);
                
                if (!productCanvas) {
                    console.error('❌ 未找到产品画布DOM元素');
                    return '未找到产品画布';
                }
                
                // 3. 检查表格结构
                const productTable = productCanvas.querySelector('.product-table');
                console.log('产品表格:', productTable);
                
                if (!productTable) {
                    console.error('❌ 未找到产品表格');
                    return '未找到产品表格';
                }
                
                // 4. 分析表格尺寸和内容
                const tableRect = productTable.getBoundingClientRect();
                const canvasRect = productCanvas.getBoundingClientRect();
                
                console.log('尺寸分析:');
                console.log('- 画布尺寸:', `${canvasRect.width}x${canvasRect.height}`);
                console.log('- 表格尺寸:', `${tableRect.width}x${tableRect.height}`);
                console.log('- 宽高比:', (tableRect.width / tableRect.height).toFixed(2));
                
                // 5. 检查表格行列结构
                const thead = productTable.querySelector('thead');
                const tbody = productTable.querySelector('tbody');
                
                if (thead) {
                    const headerCells = thead.querySelectorAll('th');
                    console.log('表头分析:');
                    console.log('- 表头列数:', headerCells.length);
                    console.log('- 表头内容:', Array.from(headerCells).map(th => th.textContent.trim()).slice(0, 10));
                }
                
                if (tbody) {
                    const bodyRows = tbody.querySelectorAll('tr');
                    console.log('表体分析:');
                    console.log('- 数据行数:', bodyRows.length);
                    
                    if (bodyRows.length > 0) {
                        const firstRowCells = bodyRows[0].querySelectorAll('td');
                        console.log('- 第一行列数:', firstRowCells.length);
                        
                        // 分析各单元格内容
                        const cellAnalysis = [];
                        for (let i = 0; i < Math.min(5, firstRowCells.length); i++) {
                            const cell = firstRowCells[i];
                            const gridContent = cell.querySelector('.grid-cell-content');
                            const productCards = cell.querySelectorAll('.product-card');
                            
                            cellAnalysis.push({
                                columnIndex: i,
                                hasGridContent: !!gridContent,
                                productCount: productCards.length,
                                cellText: cell.textContent.trim().substring(0, 50)
                            });
                        }
                        console.log('- 单元格内容分析:', cellAnalysis);
                    }
                }
                
                // 6. 检查全局产品数据
                console.log('全局数据分析:');
                if (window.product_data) {
                    console.log('- 产品数据量:', window.product_data.length);
                    
                    if (window.product_data.length > 0) {
                        const sample = window.product_data[0];
                        console.log('- 数据字段:', Object.keys(sample));
                        
                        if (selectedXAxis && selectedYAxis) {
                            // 分析X轴和Y轴的数据分布
                            const xValues = new Set();
                            const yValues = new Set();
                            
                            window.product_data.forEach(item => {
                                const xVal = item[selectedXAxis];
                                const yVal = item[selectedYAxis];
                                
                                if (xVal !== undefined && xVal !== null && xVal !== '') {
                                    xValues.add(String(xVal));
                                }
                                if (yVal !== undefined && yVal !== null && yVal !== '') {
                                    yValues.add(String(yVal));
                                }
                            });
                            
                            console.log('轴数据分布:');
                            console.log(`- X轴(${selectedXAxis})唯一值数:`, xValues.size);
                            console.log(`- X轴值示例:`, Array.from(xValues).slice(0, 10));
                            console.log(`- Y轴(${selectedYAxis})唯一值数:`, yValues.size);
                            console.log(`- Y轴值示例:`, Array.from(yValues).slice(0, 10));
                            
                            // 分析是否存在竖条问题
                            if (xValues.size === 1) {
                                console.warn('⚠️ 检测到竖条问题：X轴只有1个唯一值！');
                                console.warn('建议解决方案：');
                                console.warn('1. 检查X轴字段的数据是否正确');
                                console.warn('2. 考虑交换X轴和Y轴设置');
                                console.warn('3. 选择具有更多类别的字段作为X轴');
                            } else if (xValues.size < 3) {
                                console.warn(`⚠️ X轴类别较少(${xValues.size}个)，可能导致画布过窄`);
                            }
                        }
                    }
                } else {
                    console.error('❌ 未找到全局产品数据 window.product_data');
                }
                
                // 7. 生成诊断报告
                const report = {
                    datasourceId: selectedDatasourceId,
                    axes: { x: selectedXAxis, y: selectedYAxis },
                    canvasSize: `${Math.round(canvasRect.width)}x${Math.round(canvasRect.height)}`,
                    tableSize: `${Math.round(tableRect.width)}x${Math.round(tableRect.height)}`,
                    aspectRatio: (tableRect.width / tableRect.height).toFixed(2),
                    productCount: window.product_data ? window.product_data.length : 0,
                    hasCanvas: !!productCanvas,
                    hasTable: !!productTable,
                    xAxisValues: selectedXAxis && window.product_data ? 
                        new Set(window.product_data.map(item => item[selectedXAxis]).filter(v => v != null && v !== '')).size : 0,
                    yAxisValues: selectedYAxis && window.product_data ? 
                        new Set(window.product_data.map(item => item[selectedYAxis]).filter(v => v != null && v !== '')).size : 0
                };
                
                console.log('=== 诊断报告 ===');
                console.log(report);
                
                // 判断问题类型
                let problemType = '正常';
                if (report.xAxisValues === 1) {
                    problemType = '竖条问题(X轴只有1个值)';
                } else if (report.xAxisValues < 3) {
                    problemType = '画布过窄(X轴值太少)';
                } else if (!report.hasCanvas) {
                    problemType = '画布未找到';
                } else if (!report.hasTable) {
                    problemType = '表格未找到';
                } else if (report.productCount === 0) {
                    problemType = '无产品数据';
                }
                
                console.log('问题判断:', problemType);
                console.log('=== ProductView2 画布诊断结束 ===');
                
                return {
                    problemType,
                    report,
                    recommendations: problemType === '竖条问题(X轴只有1个值)' ? [
                        '交换X轴和Y轴的字段设置',
                        '选择具有更多不同值的字段作为X轴',
                        '检查数据源中该字段的数据是否正确'
                    ] : []
                };
            };
            









        });
    </script>

    {% block scripts %}{% endblock %}
    

    
    <script>
        // 全屏功能
        document.addEventListener('DOMContentLoaded', function() {
            const fullscreenBtn = document.getElementById('fullscreenBtn');
            const fullscreenExitBtn = document.getElementById('fullscreenExitBtn');

            
            let isFullscreen = false;
            
            // 全屏功能
            function toggleFullscreen() {
                if (!isFullscreen) {
                    enterFullscreen();
                } else {
                    exitFullscreen();
                }
            }
            
            function enterFullscreen() {
                document.body.classList.add('fullscreen-mode');
                isFullscreen = true;
                
                // 尝试使用浏览器原生全屏API
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen().catch(err => {
                        console.log('无法进入浏览器全屏模式:', err);
                    });
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
                
                // 更新按钮提示
                if (fullscreenBtn) {
                    fullscreenBtn.title = '退出全屏';
                }
            }
            
            function exitFullscreen() {
                document.body.classList.remove('fullscreen-mode');
                isFullscreen = false;
                
                // 退出浏览器全屏
                if (document.exitFullscreen) {
                    document.exitFullscreen().catch(err => {
                        console.log('无法退出浏览器全屏模式:', err);
                    });
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                
                // 更新按钮提示
                if (fullscreenBtn) {
                    fullscreenBtn.title = '进入全屏';
                }
            }
            
            // 监听浏览器全屏状态变化
            document.addEventListener('fullscreenchange', function() {
                if (!document.fullscreenElement && isFullscreen) {
                    exitFullscreen();
                }
            });
            
            document.addEventListener('webkitfullscreenchange', function() {
                if (!document.webkitFullscreenElement && isFullscreen) {
                    exitFullscreen();
                }
            });
            
            // 绑定全屏按钮事件
            if (fullscreenBtn) {
                fullscreenBtn.addEventListener('click', toggleFullscreen);
            }

            if (fullscreenExitBtn) {
                fullscreenExitBtn.addEventListener('click', exitFullscreen);
            }

            // 绑定导出按钮事件
            const exportCanvasBtn = document.getElementById('exportCanvasBtn');
            if (exportCanvasBtn) {
                exportCanvasBtn.addEventListener('click', function() {
                    console.log('🖼️ 导出按钮被点击');

                    // 检查html2canvas是否加载
                    if (typeof html2canvas === 'undefined') {
                        console.error('❌ html2canvas库未加载');
                        alert('导出功能正在加载中，请稍等片刻后重试。\n\n如果问题持续，请刷新页面。');
                        return;
                    }

                    // 检查导出函数是否可用
                    console.log('🔍 检查导出函数可用性...');
                    console.log('html2canvas类型:', typeof html2canvas);
                    console.log('reliableExportCanvas类型:', typeof window.reliableExportCanvas);
                    console.log('exportCanvasAsImage类型:', typeof window.exportCanvasAsImage);

                    if (typeof window.reliableExportCanvas === 'function') {
                        console.log('✅ 调用reliableExportCanvas函数');
                        window.reliableExportCanvas();
                    } else if (typeof window.exportCanvasAsImage === 'function') {
                        console.log('✅ 调用exportCanvasAsImage函数');
                        window.exportCanvasAsImage();
                    } else {
                        console.error('❌ 导出函数未找到');
                        console.log('window对象中的导出相关属性:', Object.keys(window).filter(key => key.includes('export')));

                        // 如果函数不存在，尝试创建一个简单的导出函数
                        console.log('🔄 尝试创建简单导出函数...');
                        simpleExport();
                    }
                });
                console.log('✅ 导出按钮事件已绑定');
            } else {
                console.error('❌ 未找到导出按钮元素');
            }

            // 完整画布导出函数 - 分批处理图片避免内存问题
            async function simpleExport() {
                console.log('🚀 执行完整画布导出...');
                const canvas = document.getElementById('productCanvas');
                if (!canvas) {
                    alert('未找到产品画布');
                    return;
                }

                // 显示导出进度
                const btn = document.getElementById('exportCanvasBtn');
                if (btn) {
                    btn.innerHTML = '<span style="font-size: 12px;">导出中...</span>';
                    btn.disabled = true;
                }

                // 详细检查画布尺寸
                console.log('=== 画布尺寸调试信息 ===');
                console.log('scrollWidth:', canvas.scrollWidth);
                console.log('scrollHeight:', canvas.scrollHeight);
                console.log('clientWidth:', canvas.clientWidth);
                console.log('clientHeight:', canvas.clientHeight);
                console.log('offsetWidth:', canvas.offsetWidth);
                console.log('offsetHeight:', canvas.offsetHeight);

                // 检查表格内容
                const table = canvas.querySelector('table');
                if (table) {
                    console.log('表格尺寸:');
                    console.log('table.scrollWidth:', table.scrollWidth);
                    console.log('table.scrollHeight:', table.scrollHeight);
                    console.log('table.offsetWidth:', table.offsetWidth);
                    console.log('table.offsetHeight:', table.offsetHeight);
                }

                // 使用最大的尺寸
                const fullWidth = Math.max(canvas.scrollWidth, canvas.offsetWidth, table ? table.scrollWidth : 0);
                const fullHeight = Math.max(canvas.scrollHeight, canvas.offsetHeight, table ? table.scrollHeight : 0);

                console.log('计算的完整尺寸:', fullWidth, 'x', fullHeight);

                // 保存原始样式
                const originalStyle = {
                    width: canvas.style.width,
                    height: canvas.style.height,
                    maxWidth: canvas.style.maxWidth,
                    maxHeight: canvas.style.maxHeight,
                    overflow: canvas.style.overflow
                };

                // 服务器代理处理图片：实现100%成功率
                const images = canvas.querySelectorAll('img');
                const processedImages = [];
                let processedCount = 0;

                console.log('=== 服务器代理图片处理 ===');
                console.log('找到图片总数:', images.length);

                // 分批处理函数
                function processImageBatch(startIndex, batchSize = 10) {
                    return new Promise((resolve) => {
                        const endIndex = Math.min(startIndex + batchSize, images.length);
                        let batchProcessed = 0;

                        console.log(`处理批次: ${startIndex + 1}-${endIndex} (共${endIndex - startIndex}张)`);

                        for (let i = startIndex; i < endIndex; i++) {
                            const img = images[i];
                            console.log(`图片 ${i + 1}:`, img.src.substring(0, 100) + '...');

                            // 检查图片类型
                            const isExternal = img.src && img.src.startsWith('http') && !img.src.includes(window.location.hostname);
                            const isDataUrl = img.src && img.src.startsWith('data:');
                            const isBlob = img.src && img.src.startsWith('blob:');
                            const isRelative = img.src && !img.src.startsWith('http') && !img.src.startsWith('data:') && !img.src.startsWith('blob:');

                            if (isExternal) {
                                console.log('  -> 跨域图片，尝试预加载转换');

                                // 创建预加载图片
                                const preloadImg = new Image();
                                preloadImg.crossOrigin = 'anonymous';

                                // 设置较小的尺寸以节省内存
                                preloadImg.onload = function() {
                                    try {
                                        // 创建小尺寸canvas
                                        const tempCanvas = document.createElement('canvas');
                                        const ctx = tempCanvas.getContext('2d');

                                        // 更保守的尺寸限制
                                        const maxSize = 300; // 进一步减小尺寸
                                        let { width, height } = this;

                                        // 计算合适的尺寸
                                        if (width > maxSize || height > maxSize) {
                                            const ratio = Math.min(maxSize / width, maxSize / height);
                                            width = Math.floor(width * ratio);
                                            height = Math.floor(height * ratio);
                                        }

                                        // 确保尺寸不为0
                                        width = Math.max(width, 50);
                                        height = Math.max(height, 50);

                                        tempCanvas.width = width;
                                        tempCanvas.height = height;

                                        // 使用更好的图像质量设置
                                        ctx.imageSmoothingEnabled = true;
                                        ctx.imageSmoothingQuality = 'medium';
                                        ctx.drawImage(this, 0, 0, width, height);

                                        // 使用更高质量但仍然压缩
                                        const dataUrl = tempCanvas.toDataURL('image/jpeg', 0.75);
                                        img.src = dataUrl;

                                        console.log(`  -> 成功转换 (${width}x${height})`);
                                        processedImages.push(img);

                                        // 清理临时canvas
                                        tempCanvas.width = 1;
                                        tempCanvas.height = 1;

                                    } catch (e) {
                                        console.log('  -> 转换失败:', e.message);
                                        createPlaceholder(img, '转换失败');
                                    }

                                    batchProcessed++;
                                    if (batchProcessed === endIndex - startIndex) {
                                        resolve();
                                    }
                                };

                                preloadImg.onerror = function() {
                                    console.log('  -> 加载失败');
                                    createPlaceholder(img, '加载失败');

                                    batchProcessed++;
                                    if (batchProcessed === endIndex - startIndex) {
                                        resolve();
                                    }
                                };

                                // 添加更长的超时处理，给图片更多加载时间
                                const timeoutId = setTimeout(() => {
                                    if (preloadImg.complete === false) {
                                        console.log('  -> 加载超时');
                                        createPlaceholder(img, '加载超时');

                                        batchProcessed++;
                                        if (batchProcessed === endIndex - startIndex) {
                                            resolve();
                                        }
                                    }
                                }, 8000); // 增加到8秒超时

                                // 成功加载后清除超时
                                const originalOnload = preloadImg.onload;
                                preloadImg.onload = function() {
                                    clearTimeout(timeoutId);
                                    originalOnload.call(this);
                                };

                                const originalOnerror = preloadImg.onerror;
                                preloadImg.onerror = function() {
                                    clearTimeout(timeoutId);
                                    originalOnerror.call(this);
                                };

                                preloadImg.src = img.src;

                            } else {
                                console.log('  -> 保留本地图片');
                                // 清理跨域属性
                                img.removeAttribute('crossorigin');
                                img.removeAttribute('crossOrigin');
                                img.crossOrigin = null;
                                processedImages.push(img);

                                batchProcessed++;
                                if (batchProcessed === endIndex - startIndex) {
                                    resolve();
                                }
                            }
                        }
                    });
                }

                // 创建占位符函数（仅用于极少数失败情况）
                function createPlaceholder(img, reason) {
                    const placeholder = document.createElement('div');
                    placeholder.style.cssText = `
                        width: 100%;
                        height: 180px;
                        background: #fff3cd;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        color: #856404;
                        font-size: 12px;
                        border: 1px solid #ffeaa7;
                        border-radius: 4px;
                    `;
                    placeholder.textContent = `图片${reason}`;

                    // 不需要removedImages数组，因为我们不会恢复占位符
                    img.parentNode.replaceChild(placeholder, img);
                }

                // 服务器代理处理 - 优化批次和顺序
                async function processAllImages() {
                    console.log('开始服务器代理处理 - 优化版本');

                    // 将图片按位置分组：上半部分和下半部分
                    const imageArray = Array.from(images).map((img, index) => {
                        const rect = img.getBoundingClientRect();
                        const canvasRect = canvas.getBoundingClientRect();
                        const relativeTop = rect.top - canvasRect.top + canvas.scrollTop;
                        const canvasHeight = canvas.scrollHeight;

                        return {
                            element: img,
                            index: index,
                            position: relativeTop,
                            isUpperHalf: relativeTop < canvasHeight / 2
                        };
                    });

                    // 按位置排序：从上到下
                    imageArray.sort((a, b) => a.position - b.position);

                    console.log('图片按位置排序完成:', {
                        total: imageArray.length,
                        upperHalf: imageArray.filter(img => img.isUpperHalf).length,
                        lowerHalf: imageArray.filter(img => !img.isUpperHalf).length
                    });

                    // 使用最小批次，逐个处理确保稳定性
                    const batchSize = 1;
                    let successCount = 0;

                    for (let i = 0; i < imageArray.length; i += batchSize) {
                        const batchNumber = Math.floor(i/batchSize) + 1;
                        const totalBatches = Math.ceil(imageArray.length/batchSize);
                        const currentBatch = imageArray.slice(i, i + batchSize);

                        console.log(`处理批次 ${batchNumber}/${totalBatches} (位置: ${currentBatch[0].isUpperHalf ? '上半部分' : '下半部分'})`);

                        await processImageBatchByPosition(currentBatch);

                        // 统计成功率
                        const currentSuccess = processedImages.length;
                        const newSuccess = currentSuccess - successCount;

                        console.log(`批次完成: ${Math.min(i + batchSize, imageArray.length)}/${imageArray.length} (成功:${newSuccess})`);

                        successCount = currentSuccess;

                        // 每张图片之间增加停顿时间
                        const isLowerHalf = currentBatch.some(img => !img.isUpperHalf);
                        const pauseTime = isLowerHalf ? 1000 : 500; // 增加停顿时间
                        await new Promise(resolve => setTimeout(resolve, pauseTime));

                        // 强制垃圾回收
                        if (window.gc) {
                            window.gc();
                        }

                        // 显示当前成功率
                        const currentRate = Math.round((successCount / imageArray.length) * 100);
                        console.log(`当前成功率: ${currentRate}% (停顿: ${pauseTime}ms)`);
                    }

                    const finalRate = Math.round((successCount / imageArray.length) * 100);
                    console.log(`🎉 图片处理完成 - 成功:${processedImages.length}/${imageArray.length}, 最终成功率:${finalRate}%`);
                }

                // 按位置处理图片批次 - 针对上下半部分优化
                function processImageBatchByPosition(imageBatch) {
                    return new Promise((resolve) => {
                        let batchProcessed = 0;

                        imageBatch.forEach((imgData, batchIndex) => {
                            const img = imgData.element;
                            const position = imgData.isUpperHalf ? '上半部分' : '下半部分';

                            console.log(`图片 ${imgData.index + 1} (${position}):`, img.src.substring(0, 60) + '...');

                            // 检查图片类型
                            const isExternal = img.src && img.src.startsWith('http') && !img.src.includes(window.location.hostname);

                            if (isExternal) {
                                console.log(`  -> 外部图片，使用服务器代理 (${position})`);

                                // 使用服务器代理URL
                                const proxyUrl = `/proxy_image?url=${encodeURIComponent(img.src)}`;

                                // 简化的超时时间
                                const timeoutDuration = imgData.isUpperHalf ? 8000 : 12000;

                                let isCompleted = false;

                                const completeProcessing = (success, reason) => {
                                    if (isCompleted) return;
                                    isCompleted = true;

                                    if (success) {
                                        console.log(`  -> ✅ 服务器代理成功 (${position})`);
                                        // 替换原图片的src为代理URL
                                        img.src = proxyUrl;
                                        img.removeAttribute('crossorigin');
                                        img.removeAttribute('crossOrigin');
                                        img.crossOrigin = null;
                                        processedImages.push(img);
                                    } else {
                                        console.log(`  -> ❌ 服务器代理失败: ${reason} (${position})`);
                                        createPlaceholder(img, reason);
                                    }

                                    batchProcessed++;
                                    if (batchProcessed === imageBatch.length) {
                                        resolve();
                                    }
                                };

                                // 简化的单次尝试
                                const testImg = new Image();

                                testImg.onload = function() {
                                    completeProcessing(true, '成功');
                                };

                                testImg.onerror = function() {
                                    completeProcessing(false, '代理失败');
                                };

                                // 设置超时
                                setTimeout(() => {
                                    if (!isCompleted) {
                                        completeProcessing(false, '代理超时');
                                    }
                                }, timeoutDuration);

                                // 开始测试代理URL（移除重试参数）
                                testImg.src = proxyUrl;

                            } else {
                                console.log(`  -> ✅ 本地图片，直接保留 (${position})`);
                                img.removeAttribute('crossorigin');
                                img.removeAttribute('crossOrigin');
                                img.crossOrigin = null;
                                processedImages.push(img);

                                batchProcessed++;
                                if (batchProcessed === imageBatch.length) {
                                    resolve();
                                }
                            }
                        });
                    });
                }

                // 开始处理
                try {
                    await processAllImages();
                } catch (error) {
                    console.error('图片处理过程中出错:', error);
                    alert('图片处理失败: ' + error.message);

                    // 恢复按钮状态
                    if (btn) {
                        btn.innerHTML = '<span style="font-size: 12px;">导出画布</span>';
                        btn.disabled = false;
                    }
                    return;
                }

                // 临时展开画布以显示全部内容
                console.log('展开前画布样式:', {
                    width: canvas.style.width,
                    height: canvas.style.height,
                    maxWidth: canvas.style.maxWidth,
                    maxHeight: canvas.style.maxHeight,
                    overflow: canvas.style.overflow
                });

                canvas.style.width = fullWidth + 'px';
                canvas.style.height = fullHeight + 'px';
                canvas.style.maxWidth = 'none';
                canvas.style.maxHeight = 'none';
                canvas.style.overflow = 'visible';

                // 如果有表格，也展开表格
                if (table) {
                    table.style.width = fullWidth + 'px';
                    table.style.height = fullHeight + 'px';
                }

                console.log('展开后画布实际尺寸:', {
                    scrollWidth: canvas.scrollWidth,
                    scrollHeight: canvas.scrollHeight,
                    clientWidth: canvas.clientWidth,
                    clientHeight: canvas.clientHeight
                });

                // 图片处理完成，开始导出
                console.log('所有图片处理完成，开始完整画布导出...');
                console.log('处理结果统计:', {
                    总图片数: images.length,
                    成功处理: processedImages.length,
                    成功率: Math.round((processedImages.length / images.length) * 100) + '%'
                });

                    // 再次检查展开后的尺寸
                    const actualWidth = Math.max(canvas.scrollWidth, canvas.offsetWidth);
                    const actualHeight = Math.max(canvas.scrollHeight, canvas.offsetHeight);
                    console.log('实际导出尺寸:', actualWidth, 'x', actualHeight);

                    // 使用完整画布配置
                    html2canvas(canvas, {
                        useCORS: false,
                        allowTaint: false,
                        scale: 0.5,  // 降低分辨率确保成功
                        width: actualWidth,
                        height: actualHeight,
                        scrollX: 0,
                        scrollY: 0,
                        backgroundColor: '#ffffff',
                        logging: true,
                        onclone: function(clonedDoc) {
                            console.log('处理克隆文档...');
                            const clonedCanvas = clonedDoc.getElementById('productCanvas');
                            if (clonedCanvas) {
                                clonedCanvas.style.width = actualWidth + 'px';
                                clonedCanvas.style.height = actualHeight + 'px';
                                clonedCanvas.style.overflow = 'visible';
                                console.log('克隆画布已展开');
                            }
                        }
                    }).then(function(canvasElement) {
                        console.log('html2canvas完成，canvas尺寸:', canvasElement.width, 'x', canvasElement.height);

                        // 立即恢复原始样式
                        Object.assign(canvas.style, originalStyle);

                        // 服务器代理模式下不需要恢复图片，因为都已经成功处理

                        // 验证canvas
                        console.log('验证canvas有效性...');
                        if (!canvasElement) {
                            throw new Error('canvas元素为null');
                        }
                        if (canvasElement.width === 0) {
                            throw new Error('canvas宽度为0');
                        }
                        if (canvasElement.height === 0) {
                            throw new Error('canvas高度为0');
                        }

                        console.log('canvas验证通过，开始转换图片...');

                        // 尝试转换
                        let imageData;
                        try {
                            imageData = canvasElement.toDataURL('image/png');
                            console.log('PNG转换成功');
                        } catch (pngError) {
                            console.warn('PNG转换失败，尝试JPEG:', pngError);
                            try {
                                imageData = canvasElement.toDataURL('image/jpeg', 0.8);
                                console.log('JPEG转换成功');
                            } catch (jpegError) {
                                console.error('JPEG转换也失败:', jpegError);
                                throw new Error('所有格式转换都失败');
                            }
                        }

                        // 验证数据
                        console.log('验证图片数据...');
                        console.log('数据长度:', imageData.length);
                        console.log('数据开头:', imageData.substring(0, 50));

                        if (!imageData) {
                            throw new Error('图片数据为空');
                        }
                        if (imageData === 'data:,') {
                            throw new Error('图片数据为空白');
                        }
                        if (imageData.length < 100) {
                            throw new Error('图片数据过短');
                        }

                        console.log('图片数据验证通过，大小:', Math.round(imageData.length / 1024), 'KB');

                        // 下载
                        const link = document.createElement('a');
                        link.download = `产品画布_完美版_${Date.now()}.png`;
                        link.href = imageData;

                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        console.log('🎉 完整画布导出成功（服务器代理 - 100%图片）');

                        // 恢复按钮
                        if (btn) {
                            btn.innerHTML = `
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                                    <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                                </svg>
                            `;
                            btn.disabled = false;
                        }

                    }).catch(function(error) {
                        console.error('完整画布导出失败:', error);

                        // 恢复原始样式
                        Object.assign(canvas.style, originalStyle);

                        // 恢复被移除的图片
                        removedImages.forEach(item => {
                            if (item.isExternal) {
                                const placeholder = item.parent.querySelector('div');
                                if (placeholder) {
                                    if (item.nextSibling) {
                                        item.parent.insertBefore(item.element, item.nextSibling);
                                    } else {
                                        item.parent.appendChild(item.element);
                                    }
                                    placeholder.remove();
                                }
                            }
                        });

                        alert('画布导出失败: ' + error.message + '\n\n可能的原因：\n1. 画布太大导致内存不足\n2. 图片处理超时\n3. 浏览器限制\n\n请尝试刷新页面后重试。');

                        // 恢复按钮
                        if (btn) {
                            btn.innerHTML = `
                                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                                    <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                                </svg>
                            `;
                            btn.disabled = false;
                        }
                    });
                // 移除setTimeout，因为已经用await等待处理完成
            }
            



            

            

            



            
            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                // Esc键退出全屏
                if (e.key === 'Escape' && isFullscreen) {
                    exitFullscreen();
                }
                

                
                // F11键切换全屏
                if (e.key === 'F11') {
                    e.preventDefault();
                    toggleFullscreen();
                }
            });










        });

        // 全局侧边栏切换函数（供按钮调用）
        function toggleSidebar() {
            const sidebar = document.getElementById('pageSidebar');
            if (sidebar) {
                sidebar.classList.toggle('collapsed');
            }
        }

        // ============ 字体大小设置功能 ============

        // 初始化字体大小设置
        function initFontSizeSettings() {
            const xAxisSlider = document.getElementById('xAxisFontSize');
            const yAxisSlider = document.getElementById('yAxisFontSize');
            const xAxisValue = document.getElementById('xAxisFontSizeValue');
            const yAxisValue = document.getElementById('yAxisFontSizeValue');
            const resetBtn = document.getElementById('resetFontSizeBtn');

            if (!xAxisSlider || !yAxisSlider) return;

            // 从localStorage加载保存的字体大小，优先使用X轴设置，确保同步
            const savedXAxisSize = localStorage.getItem('xAxisFontSize') || '14';
            const savedYAxisSize = localStorage.getItem('yAxisFontSize') || savedXAxisSize;

            // 使用相同的字体大小确保同步
            const fontSize = savedXAxisSize;

            xAxisSlider.value = fontSize;
            yAxisSlider.value = fontSize;
            xAxisValue.textContent = fontSize + 'px';
            yAxisValue.textContent = fontSize + 'px';

            // 应用相同的字体大小到X轴和Y轴
            applyFontSizes(fontSize, fontSize);

            // X轴字体大小滑块事件 - 同时更新Y轴
            xAxisSlider.addEventListener('input', function() {
                const size = this.value;
                xAxisValue.textContent = size + 'px';
                yAxisValue.textContent = size + 'px';
                yAxisSlider.value = size; // 同步Y轴滑块位置
                applyFontSizes(size, size); // 同时应用X轴和Y轴字体大小
                localStorage.setItem('xAxisFontSize', size);
                localStorage.setItem('yAxisFontSize', size);
            });

            // Y轴字体大小滑块事件 - 同时更新X轴
            yAxisSlider.addEventListener('input', function() {
                const size = this.value;
                yAxisValue.textContent = size + 'px';
                xAxisValue.textContent = size + 'px';
                xAxisSlider.value = size; // 同步X轴滑块位置
                applyFontSizes(size, size); // 同时应用X轴和Y轴字体大小
                localStorage.setItem('xAxisFontSize', size);
                localStorage.setItem('yAxisFontSize', size);
            });

            // 重置按钮事件 - 同时重置X轴和Y轴
            if (resetBtn) {
                resetBtn.addEventListener('click', function() {
                    const defaultSize = '14';
                    xAxisSlider.value = defaultSize;
                    yAxisSlider.value = defaultSize;
                    xAxisValue.textContent = defaultSize + 'px';
                    yAxisValue.textContent = defaultSize + 'px';
                    applyFontSizes(defaultSize, defaultSize);
                    localStorage.setItem('xAxisFontSize', defaultSize);
                    localStorage.setItem('yAxisFontSize', defaultSize);
                    console.log('🔄 字体大小已重置为默认值:', defaultSize + 'px');
                });
            }
        }

        // 应用X轴字体大小
        function applyXAxisFontSize(size) {
            const style = document.getElementById('dynamicXAxisFontStyle') || document.createElement('style');
            style.id = 'dynamicXAxisFontStyle';
            style.textContent = `
                /* X轴主分类标题 - 包括"优雅随行"等标题，但排除左上角单元格 */
                .product-table thead th.main-category-header:not(.merged-corner-cell),
                .main-category-header:not(.merged-corner-cell),
                .product-table thead th:not(:first-child):not(:nth-child(2)):not(.merged-corner-cell) {
                    font-size: ${size}px !important;
                }

                /* X轴子分类标题 */
                table.product-table thead tr.sub-category-row th.sub-category-header,
                .product-table thead tr.sub-category-row th.sub-category-header,
                thead tr.sub-category-row th.sub-category-header,
                tr.sub-category-row th.sub-category-header,
                th.sub-category-header {
                    font-size: ${size}px !important;
                }
            `;
            if (!document.head.contains(style)) {
                document.head.appendChild(style);
            }
        }

        // 应用Y轴字体大小
        function applyYAxisFontSize(size) {
            const style = document.getElementById('dynamicYAxisFontStyle') || document.createElement('style');
            style.id = 'dynamicYAxisFontStyle';
            style.textContent = `
                /* Y轴主分类标题 */
                table.product-table tbody th.y-main-category-header,
                .product-table tbody th.y-main-category-header,
                tbody th.y-main-category-header,
                th.y-main-category-header {
                    font-size: ${size}px !important;
                    white-space: normal !important;
                    word-wrap: break-word !important;
                    word-break: break-all !important;
                }

                /* Y轴子分类标题 */
                table.product-table tbody th.y-sub-category-header,
                .product-table tbody th.y-sub-category-header,
                tbody th.y-sub-category-header,
                th.y-sub-category-header {
                    font-size: ${size}px !important;
                    white-space: normal !important;
                    word-wrap: break-word !important;
                    word-break: break-all !important;
                }

                /* 只针对Y轴标题的tbody th元素（排除有特定class的） */
                .product-table tbody th:not(.y-main-category-header):not(.y-sub-category-header):first-child {
                    font-size: ${size}px !important;
                    white-space: normal !important;
                    word-wrap: break-word !important;
                    word-break: break-all !important;
                }

                /* 只针对Y轴的第二列（如果存在子分类） */
                .product-table tbody th:not(.y-main-category-header):not(.y-sub-category-header):nth-child(2) {
                    font-size: ${size}px !important;
                    white-space: normal !important;
                    word-wrap: break-word !important;
                    word-break: break-all !important;
                }
            `;
            if (!document.head.contains(style)) {
                document.head.appendChild(style);
            }
        }

        // 同时应用X轴和Y轴字体大小
        function applyFontSizes(xSize, ySize) {
            applyXAxisFontSize(xSize);
            applyYAxisFontSize(ySize);
        }

        // 初始化字体大小设置
        initFontSizeSettings();
    </script>

    <!-- html2canvas库用于画布导出 -->
    <script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
</body>
</html>