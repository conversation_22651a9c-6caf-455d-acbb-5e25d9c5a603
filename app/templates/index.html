{% extends "user_base.html" %}

{% block title %}首页 - {{ super() }}{% endblock %}

{% block styles %}
<style>
    /* 产品卡片基本样式 */
    .product-card {
        border: 1px solid #eaeaea;
        border-radius: 12px;
        padding: 0;
        margin-bottom: 15px;
        background-color: #fff;
        box-shadow: 0 6px 16px rgba(0,0,0,0.08);
        width: 180px; /* 卡片固定宽度 */
        min-width: 180px; /* 确保卡片不会被过度压缩 */
        /* 移除固定最小高度，让卡片根据实际内容自适应 */
        text-align: left;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.12);
    }
    
    /* 产品图片区域 */
    .product-image-container {
        width: 100%;
        height: 180px; /* 保持图片区域高度不变 */
        position: relative;
        background-color: #f8f8f8;
        overflow: hidden;
        flex-shrink: 0; /* 防止图片区域被压缩 */
    }
    
    .product-image-container img {
        display: block !important;
        width: 100% !important;
        height: 180px !important;
        object-fit: cover !important;
        max-width: 100% !important;
        transition: transform 0.4s ease;
    }
    
    .product-image-container:hover img {
        transform: scale(1.05);
    }
    
    .no-image-placeholder {
        width: 100%;
        height: 180px;
        background-color: #f0f0f0;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8em;
        color: #aaa;
    }
    
    /* 产品信息区域 - 进一步优化为更紧凑的布局 */
    .product-info-container {
        padding: 8px 12px; /* 减少内边距，从12px改为8px */
        display: flex;
        flex-direction: column;
        justify-content: flex-start; /* 改为顶部对齐，让内容更靠上 */
        min-height: 50px; /* 进一步减少最小高度，从60px改为50px */
        flex-shrink: 0; /* 防止被压缩 */
    }
    
    .product-name {
        font-weight: 500;
        font-size: 0.9em;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #333;
    }
    
    .product-price {
        font-size: 1.2em;
        font-weight: 600;
        color: #e74c3c;
        margin-top: 2px; /* 减少上边距，从8px改为2px */
        padding: 0; /* 移除内边距，从3px 0改为0 */
        line-height: 1.2; /* 减少行高，让文字更紧凑 */
    }
    
    /* 产品元数据区域 */
    .product-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
        font-size: 0.8em;
        color: #666;
    }
    
    .store-name {
        background-color: #f0f7ff;
        color: #0066cc;
        padding: 2px 8px;
        border-radius: 10px;
        display: inline-block;
        font-size: 0.75em;
        max-width: 100px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .payment-info {
        color: #666;
        font-size: 0.75em;
    }
    
    /* 产品标签和按钮 */
    .product-tag {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 3px 8px;
        font-size: 0.7em;
        border-radius: 10px;
        z-index: 2;
    }

    /* 虚拟产品卡片样式 */
    .product-card.virtual-product {
        border: 2px solid #4CAF50;
        box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
        animation: virtualGlow 2s ease-in-out infinite alternate;
        position: relative;
    }

    .product-card.virtual-product::before {
        content: '虚拟';
        position: absolute;
        top: -8px;
        right: -8px;
        background: linear-gradient(45deg, #4CAF50, #45a049);
        color: white;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 0.7em;
        font-weight: bold;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    @keyframes virtualGlow {
        from {
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.3);
        }
        to {
            box-shadow: 0 0 25px rgba(76, 175, 80, 0.6);
        }
    }

    /* 虚拟产品编辑按钮 */
    .virtual-product-actions {
        position: absolute;
        top: 5px;
        left: 5px;
        display: flex;
        gap: 5px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 5;
    }

    .product-card.virtual-product:hover .virtual-product-actions {
        opacity: 1;
    }

    .virtual-action-btn {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: white;
        transition: all 0.2s ease;
    }

    .virtual-edit-btn {
        background: #2196F3;
    }

    .virtual-delete-btn {
        background: #f44336;
    }

    .virtual-action-btn:hover {
        transform: scale(1.1);
    }

    /* 虚拟产品弹窗样式 */
    .virtual-product-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        z-index: 10000;
        backdrop-filter: blur(5px);
    }

    .virtual-product-modal.active {
        display: flex;
        align-items: center;
        justify-content: center;
        animation: modalFadeIn 0.3s ease;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    .virtual-product-form {
        background: white;
        border-radius: 15px;
        padding: 25px;
        width: 90%;
        max-width: 500px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        animation: modalSlideIn 0.3s ease;
        position: relative;
    }

    @keyframes modalSlideIn {
        from {
            transform: translateY(-50px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .virtual-product-form h3 {
        margin: 0 0 20px 0;
        color: #333;
        font-size: 1.4em;
        text-align: center;
        border-bottom: 2px solid #4CAF50;
        padding-bottom: 10px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #555;
        font-size: 0.9em;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        padding: 12px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        font-size: 14px;
        transition: border-color 0.3s ease;
        box-sizing: border-box;
    }

    .form-group input:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #4CAF50;
        box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
    }

    .form-group textarea {
        resize: vertical;
        min-height: 80px;
    }

    /* 图片上传区域 */
    .image-upload-area {
        border: 2px dashed #4CAF50;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        background: #f8fff8;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        min-height: 120px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .image-upload-area:hover {
        background: #f0fff0;
        border-color: #45a049;
    }

    .image-upload-area.dragover {
        background: #e8f5e8;
        border-color: #2e7d32;
        transform: scale(1.02);
    }

    .image-upload-text {
        color: #4CAF50;
        font-size: 14px;
        margin-bottom: 10px;
    }

    .image-upload-hint {
        color: #888;
        font-size: 12px;
    }

    .image-preview {
        max-width: 100%;
        max-height: 200px;
        border-radius: 8px;
        margin-top: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .image-remove-btn {
        position: absolute;
        top: 5px;
        right: 5px;
        background: #f44336;
        color: white;
        border: none;
        border-radius: 50%;
        width: 25px;
        height: 25px;
        cursor: pointer;
        font-size: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* 弹窗按钮 */
    .modal-buttons {
        display: flex;
        gap: 15px;
        justify-content: center;
        margin-top: 25px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }

    .modal-btn {
        padding: 12px 25px;
        border: none;
        border-radius: 8px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 100px;
    }

    .modal-btn-primary {
        background: #4CAF50;
        color: white;
    }

    .modal-btn-primary:hover {
        background: #45a049;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
    }

    .modal-btn-secondary {
        background: #f5f5f5;
        color: #666;
        border: 1px solid #ddd;
    }

    .modal-btn-secondary:hover {
        background: #e8e8e8;
        color: #333;
    }

    .modal-close-btn {
        position: absolute;
        top: 15px;
        right: 15px;
        background: none;
        border: none;
        font-size: 24px;
        color: #999;
        cursor: pointer;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        transition: all 0.2s ease;
    }

    .modal-close-btn:hover {
        background: #f0f0f0;
        color: #666;
    }

    /* 空白区域悬停检测区域 */
    .empty-hover-area {
        width: 180px;
        min-width: 180px;
        height: 250px;
        margin-bottom: 15px;
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    /* 添加按钮（默认隐藏） - 暂时完全隐藏 */
    .empty-add-button {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: #4CAF50;
        color: white;
        border: none;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
        z-index: 10;
        display: none !important; /* 暂时完全隐藏空白单元格的+号按钮 */
    }

    /* 悬停0.5秒后显示按钮 */
    .empty-hover-area:hover .empty-add-button {
        opacity: 1;
        visibility: visible;
        transform: translate(-50%, -50%) scale(1.1);
        transition-delay: 0.5s;
    }

    .empty-add-button:hover {
        background: #45a049;
        transform: translate(-50%, -50%) scale(1.2) !important;
        box-shadow: 0 6px 16px rgba(76, 175, 80, 0.4);
    }

    /* 隐藏原有的提示和按钮 */
    .grid-cell-content .empty-cell-hint,
    .grid-cell-content .add-virtual-product-btn {
        display: none;
    }

    /* 有产品的单元格保持原有的添加按钮 */
    .grid-cell-content.has-products .add-virtual-product-btn {
        display: flex;
    }

    /* 有产品的单元格 hover 时按钮完全可见 */
    .grid-cell-content.has-products:hover .add-virtual-product-btn {
        opacity: 1 !important;
    }

    /* 添加产品按钮（在空白单元格中） - 暂时隐藏 */
    .add-virtual-product-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #4CAF50;
        color: white;
        border: none;
        border-radius: 50%;
        width: 35px;
        height: 35px;
        cursor: pointer;
        font-size: 18px;
        display: none !important; /* 暂时隐藏+号按钮 */
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: all 0.3s ease;
        z-index: 2;
        box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
    }

    /* 有产品的单元格中的按钮样式覆盖 */
    .grid-cell-content.has-products .add-virtual-product-btn {
        opacity: 0.7 !important; /* 使用 !important 确保优先级 */
    }

    .grid-cell-content:hover .add-virtual-product-btn {
        opacity: 1;
        transform: scale(1.1);
    }

    .add-virtual-product-btn:hover {
        background: #45a049;
        box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
    }

    /* 动画效果 */
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .virtual-product-form {
            width: 95%;
            padding: 20px;
            max-height: 90vh;
        }

        .modal-buttons {
            flex-direction: column;
            gap: 10px;
        }

        .modal-btn {
            width: 100%;
        }

        .image-upload-area {
            padding: 20px;
            min-height: 100px;
        }

        .add-virtual-product-btn {
            width: 30px;
            height: 30px;
            font-size: 16px;
        }
    }
    
    /* 表格和网格布局 */
    .grid-cell-content {
        display: flex;
        flex-wrap: wrap;
        gap: 10px; /* 减少卡片间距，优化空间利用 */
        justify-content: flex-start; /* 卡片在单元格内靠左对齐 */
        align-items: flex-start;
        padding: 10px; /* 减少内边距，优化空间利用 */
        position: relative; /* 为绝对定位的按钮提供定位上下文 */
    }

    /* X轴表头标题样式 - 确保X轴标题跟随水平滚动移动 */
    thead .main-category-header,
    thead th.main-category-header,
    .main-category-row .main-category-header:not(.merged-corner-cell) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 700;
        /* 移除固定字体大小，让动态设置生效 */
        text-align: center;
        padding: 8px 6px;
        border: 2px solid #5a67d8;
        /* 确保X轴标题跟随水平滚动移动 */
        position: static !important;
        top: auto !important;
        left: auto !important;
        z-index: auto !important;
        /* 强制定位约束 */
        transform: translateZ(0);
        will-change: transform;
        /* 缩小高度为原来的1/3 */
        height: 36px !important;
        max-height: 36px !important;
        min-height: 36px !important;
        line-height: 1.2 !important;
    }

    /* 所有X轴主分类标题的通用样式（包括优雅随行等品牌名称） */
    .main-category-row .main-category-header:not(.merged-corner-cell) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        color: white !important;
        font-weight: 700 !important;
        text-align: center !important;
        padding: 8px 6px !important;
        border: 2px solid #5a67d8 !important;
        /* 缩小高度为原来的1/3 */
        height: 36px !important;
        max-height: 36px !important;
        min-height: 36px !important;
        line-height: 1.2 !important;
        /* 移除粘性定位，让X轴标题跟随水平滚动移动 */
        position: static !important;
        top: auto !important;
        z-index: auto !important;
    }

    /* 使用最强的选择器来确保X轴子分类标题不会被粘性定位 */
    table.product-table thead tr.sub-category-row th.sub-category-header,
    .product-table thead tr.sub-category-row th.sub-category-header,
    thead tr.sub-category-row th.sub-category-header,
    tr.sub-category-row th.sub-category-header,
    th.sub-category-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
        color: white !important;
        font-weight: 600 !important;
        /* 移除固定字体大小，让动态设置生效 */
        text-align: center !important;
        padding: 6px 4px !important;
        border: 1px solid #e53e3e !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        /* 强制移除粘性定位，让X轴子分类标题跟随水平滚动 */
        position: static !important;
        left: auto !important;
        top: auto !important;
        z-index: auto !important;
        /* 强制定位约束 */
        transform: translateZ(0) !important;
        will-change: auto !important;
        /* 确保宽度不受其他规则影响 */
        min-width: auto !important;
        width: auto !important;
        max-width: none !important;
        /* 固定高度，不随字体大小变化 */
        height: 36px !important;
        max-height: 36px !important;
        min-height: 36px !important;
        line-height: 36px !important;
        /* 确保文字垂直居中 */
        vertical-align: middle !important;
        /* 防止字体大小影响高度 */
        box-sizing: border-box !important;
    }

    /* ===== Y轴样式 - 主分类标签（水平冻结）===== */
    .product-table tbody th:first-child,
    tbody th.y-main-category-header {
        /* 水平冻结：只固定左侧位置，垂直方向自然滚动 */
        position: sticky;
        left: 0;
        z-index: 100;

        /* 尺寸控制 */
        width: 120px;
        min-width: 120px;
        max-width: 120px;
        min-height: 150px;

        /* 外观样式 */
        background: #e8f4fd;
        color: #1565c0;
        border: 1px solid #bbdefb;
        border-right: none;

        /* 文字样式 */
        writing-mode: vertical-rl;
        text-orientation: upright;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-align: center;

        /* 布局 */
        padding: 15px 8px;
        box-sizing: border-box;
        vertical-align: top;

        /* 文字处理 */
        white-space: normal;
        word-wrap: break-word;
        overflow: hidden;

        /* 确保水平冻结效果 */
        will-change: transform;
    }

    /* ===== Y轴样式 - 子分类标签（水平冻结）===== */
    .product-table tbody th:nth-child(2),
    tbody th.y-sub-category-header {
        /* 水平冻结：只固定左侧位置，垂直方向自然滚动 */
        position: sticky;
        left: 120px;
        z-index: 99;

        /* 尺寸控制 */
        width: 120px;
        min-width: 120px;
        max-width: 120px;
        min-height: 150px;

        /* 外观样式 */
        background: #f8f0ff;
        color: #7b1fa2;
        border: 1px solid #e1bee7;
        border-left: none;

        /* 文字样式 */
        writing-mode: vertical-rl;
        text-orientation: upright;
        font-weight: 500;
        letter-spacing: 0.5px;
        text-align: center;

        /* 布局 */
        padding: 15px 8px;
        box-sizing: border-box;
        vertical-align: top;

        /* 文字处理 */
        white-space: normal;
        word-wrap: break-word;
        overflow: hidden;

        /* 确保水平冻结效果 */
        will-change: transform;
    }
    
    .product-table {
        width: auto; /* 改为auto，让表格根据内容自适应宽度 */
        min-width: 100%; /* 确保至少占满容器宽度 */
        border-collapse: separate; /* 改为separate，避免影响粘性定位 */
        border-spacing: 0; /* 保持无间距外观 */
        margin-top: 10px;
        table-layout: auto; /* 使用auto布局，让列宽根据内容调整 */
        border: 1px solid #eaeaea;
        border-radius: 8px;
        /* 添加表格边界约束 */
        position: relative;
        overflow: visible;
    }

    /* ===== 产品画布容器样式 ===== */
    .product-canvas {
        position: relative;
        max-height: calc(100vh - 120px);
        overflow: auto;
        width: 100%;
        min-width: 100%;
        scroll-behavior: smooth;

        /* 为sticky定位创建正确的滚动容器 */
        transform: translateZ(0);

        /* 确保Y轴标签边界正确 */
        contain: none;
    }
    
    .product-table thead {
        position: sticky;
        top: 0;
        z-index: 10;
        background: #fff3e0;
    }

    /* 只对左上角单元格应用粘性定位，其他X轴标题不应该是sticky */
    .product-table thead th.merged-corner-cell,
    .product-table thead th:first-child:not(.main-category-header),
    .product-table thead th:nth-child(2):not(.main-category-header) {
        position: sticky;
        top: 0;
        z-index: 15;
    }

    /* X轴标题的通用样式（不包括粘性定位） */
    .product-table thead th {
        background: #fff3e0;
        color: #e65100;
        font-weight: 600;
        font-size: 14px;
        letter-spacing: 0.5px;
        box-shadow: none;
        border-bottom: 2px solid #ffcc02;
        border-right: 1px solid #ffb74d;
        min-width: 180px; /* 优化列宽，减少空白空间 */
        width: 180px; /* 设置更紧凑的固定宽度 */
        /* 缩小高度为原来的1/3 */
        height: 36px !important;
        max-height: 36px !important;
        min-height: 36px !important;
        padding: 8px 12px !important;
        line-height: 1.2 !important;
    }
    
    /* ===== Y轴通用样式重置 ===== */
    .product-table tbody th {
        /* 移除所有通用的sticky定位，由具体选择器控制 */
        position: static;
        background: #f8f9fa;
        border-right: 1px solid #dee2e6;
        box-sizing: border-box;
    }


    
    /* 合并的左上角单元格样式 */
    .merged-corner-cell,
    th.merged-corner-cell,
    table.product-table thead th.merged-corner-cell,
    .product-table thead th.merged-corner-cell {
        position: sticky !important;
        left: 0px !important;
        top: 0px !important;  /* 固定在顶部 */
        z-index: 9999 !important;  /* 超高z-index，确保不被任何元素覆盖 */
        /* 强制边界约束 */
        max-width: 240px !important;
        width: 240px !important;
        min-width: 240px !important;
        overflow: hidden !important;
        word-wrap: break-word !important;
        /* 强制定位约束 */
        transform: translateZ(0) !important;
        will-change: transform !important;
        /* 确保在最顶层 */
        isolation: isolate !important;
        /* 覆盖通用thead th样式 */
        background: #fff3e0 !important;
        color: #e65100 !important;
        /* 斜杠文字样式 */
        font-size: 28px !important;
        text-align: center !important;
        font-weight: bold !important;
        /* 行高设置为54px */
        height: 54px !important;
        max-height: 54px !important;
        min-height: 54px !important;
        line-height: 54px !important;
    }

    /* 左上角第一个单元格 (翡翠) - 移除粘性定位，让X轴标题跟随水平滚动 */
    table.product-table thead th.main-category-header:first-child,
    .product-table thead th.main-category-header:first-child,
    thead th.main-category-header:first-child {
        position: static !important;  /* 改为static，不再固定位置 */
        left: auto !important;        /* 移除left固定 */
        top: auto !important;         /* 移除top固定 */
        z-index: auto !important;     /* 移除z-index */
        /* 强制边界约束 */
        max-width: 120px !important;
        width: 120px !important;
        min-width: 120px !important;
        overflow: hidden !important;
        word-wrap: break-word !important;
        /* 移除强制定位约束 */
        transform: none !important;
        will-change: auto !important;
        /* 移除isolation */
        isolation: auto !important;
        /* 覆盖通用thead th样式 */
        background: #fff3e0 !important;
        color: #e65100 !important;
        /* 行高设置为108px */
        height: 108px !important;
        max-height: 108px !important;
        min-height: 108px !important;
        line-height: 108px !important;
    }

    /* 左上角第二个单元格 (三彩翡翠) - 移除粘性定位，让X轴标题跟随水平滚动 */
    table.product-table thead th.main-category-header:nth-child(2),
    .product-table thead th.main-category-header:nth-child(2),
    thead th.main-category-header:nth-child(2) {
        position: static !important;  /* 改为static，不再固定位置 */
        left: auto !important;        /* 移除left固定 */
        top: auto !important;         /* 移除top固定 */
        z-index: auto !important;     /* 移除z-index */
        /* 强制边界约束 */
        max-width: 120px !important;
        width: 120px !important;
        min-width: 120px !important;
        overflow: hidden !important;
        word-wrap: break-word !important;
        /* 移除强制定位约束 */
        transform: none !important;
        will-change: auto !important;
        /* 移除isolation */
        isolation: auto !important;
        /* 覆盖通用thead th样式 */
        background: #fff3e0 !important;
        color: #e65100 !important;
        /* 行高设置为108px */
        height: 108px !important;
        max-height: 108px !important;
        min-height: 108px !important;
        line-height: 108px !important;
    }

    
    .product-table th {
        padding: 12px;
        background: #fff3e0;
        color: #e65100;
        font-weight: 600;
        font-size: 14px;
        letter-spacing: 0.5px;
        /* 允许Y轴标题文字换行 */
        white-space: normal;
        word-wrap: break-word;
        word-break: break-all;
        text-align: center; /* 表头文字居中 */
        border: 1px solid #ffb74d;
        line-height: 1.3; /* 设置合适的行高 */
    }
    
    .product-table td {
        padding: 0;
        border: 1px solid #eaeaea;
        text-align: left; /* 单元格内容（grid-cell-content）将靠左对齐 */
        vertical-align: top;
        min-width: 180px; /* 优化数据单元格宽度，提高空间利用率 */
        width: 180px; /* 设置更紧凑的固定宽度 */
    }
    
    .empty-cell {
        color: #ccc;
        font-size: 0.9em;
        padding: 15px;
        display: block;
        text-align: center;
    }
    
    /* 轴选择表单 - 已移到顶部导航栏，保留备用 */
    
    /* 响应式布局适配 */
    @media (max-width: 768px) {
        .product-card {
            width: 160px;
            min-width: 160px;
            /* 移除固定最小高度，让卡片根据内容自适应 */
        }

        .product-image-container,
        .no-image-placeholder {
            height: 160px;
        }

        .product-image-container img {
            height: 160px !important;
        }

        .product-info-container {
            padding: 6px 10px; /* 减少小屏幕下的内边距 */
            min-height: 45px; /* 减少小屏幕下的最小高度 */
        }

        .grid-cell-content {
            gap: 10px;
            padding: 10px;
            justify-content: center; /* 在小屏幕上居中卡片 */
        }
        .product-table thead th {
            min-width: 150px; /* 适应优化后的列宽 */
        }
        .product-table tbody th {
            min-width: 120px;
        }


    }


    }
    
    @media (max-width: 576px) {
        .product-card {
            width: 140px;
            min-width: 140px;
            /* 移除固定最小高度，让卡片根据内容自适应 */
        }
        
        .product-image-container,
        .no-image-placeholder {
            height: 140px;
        }
        
        .product-image-container img {
            height: 140px !important;
        }
        
        .product-info-container {
            padding: 6px 8px; /* 进一步减少超小屏幕下的内边距 */
            min-height: 40px; /* 进一步减少超小屏幕下的最小高度 */
        }
        
        .product-name {
            font-size: 0.8em;
        }
        
        .product-price {
            font-size: 1em;
            margin-top: 1px; /* 超小屏幕下进一步减少上边距 */
        }
        .product-table thead th {
            min-width: 150px; 
        }
        .product-table tbody th {
            min-width: 100px;
        }


    }
    
    /* 可编辑图片样式 */
    .editable-image {
        cursor: pointer; /* 显示手型光标指示可点击 */
        position: relative;
    }
    
    .editable-image::after {
        content: "点击编辑";
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .editable-image:hover::after {
        opacity: 1;
    }
    

    
    /* 产品笔记功能样式 - 重新设计 */
    
    /* 蓝色笔记角标 - 缩小尺寸 */
    .note-indicator {
        position: absolute;
        top: 8px;
        left: 35px; /* 避开现有的📝指示器位置 */
        width: 8px; /* 从12px缩小到8px */
        height: 8px; /* 从12px缩小到8px */
        background: #1a73e8;
        border-radius: 50%;
        border: 1.5px solid white; /* 缩小边框 */
        box-shadow: 0 1px 3px rgba(0,0,0,0.2); /* 缩小阴影 */
        cursor: pointer;
        z-index: 6;
        transition: all 0.2s ease;
    }
    
    .note-indicator:hover {
        transform: scale(1.3); /* 增加悬停放大倍数 */
        background: #0d62cb;
    }
    
    .note-indicator.active {
        background: #0d62cb;
        box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.3); /* 缩小激活效果 */
    }
    
    /* 产品画布容器 - 合并定义，避免覆盖滚动属性 */
    /* 此处已在上方定义，不重复定义以避免覆盖overflow属性 */
    
    /* SVG连线容器 - 相对于画布定位 */
    .note-lines-container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        pointer-events: none;
        z-index: 999;
    }
    
    .note-line {
        stroke: #1a73e8;
        stroke-width: 2;
        stroke-dasharray: 5,5;
        fill: none;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }
    
    .connection-dot {
        transition: r 0.2s ease;
    }
    
    .connection-dot:hover {
        r: 4;
    }
    
    /* 画布内固定笔记框 */
    .canvas-note {
        position: absolute; /* 相对于画布绝对定位 */
        width: 250px;
        height: 170px; /* 增加高度，为拖动手柄留出空间 */
        background: white;
        border: 2px solid #1a73e8;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        z-index: 1000;
        display: none;
        padding: 0;
        overflow: hidden;
        /* 移除固定的top和right，改为动态计算位置 */
    }
    
    /* 拖动手柄 */
    .canvas-note-header {
        width: 100%;
        height: 20px;
        background: linear-gradient(135deg, #1a73e8, #0d62cb);
        cursor: move;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        user-select: none;
    }
    
    .canvas-note-header:hover {
        background: linear-gradient(135deg, #0d62cb, #0a52a3);
    }
    
    /* 拖动指示器 */
    .drag-indicator {
        width: 16px;
        height: 3px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 2px;
        position: relative;
    }
    
    .drag-indicator::before,
    .drag-indicator::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 2px;
    }
    
    .drag-indicator::before {
        top: -5px;
    }
    
    .drag-indicator::after {
        bottom: -5px;
    }
    
    /* 关闭按钮 */
    .canvas-note-close {
        position: absolute;
        right: 4px;
        top: 50%;
        transform: translateY(-50%);
        width: 12px;
        height: 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 2px;
        transition: background-color 0.2s ease;
    }
    
    .canvas-note-close:hover {
        background-color: rgba(255, 255, 255, 0.2);
    }
    
    .canvas-note-close::before,
    .canvas-note-close::after {
        content: '';
        position: absolute;
        width: 8px;
        height: 1.5px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 1px;
    }
    
    .canvas-note-close::before {
        transform: rotate(45deg);
    }
    
    .canvas-note-close::after {
        transform: rotate(-45deg);
    }
    
    .canvas-note.active {
        display: block;
        animation: noteAppear 0.3s ease-out;
    }
    
    @keyframes noteAppear {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }
    
    .canvas-note.dragging {
        user-select: none;
        opacity: 0.9;
    }
    
    .canvas-note-textarea {
        width: 100%;
        height: calc(100% - 20px); /* 减去拖动手柄的高度 */
        border: none;
        outline: none;
        padding: 12px;
        resize: none;
        font-size: 0.9em;
        font-family: inherit;
        background: transparent;
        color: #333;
        cursor: text;
        transition: border-color 0.3s ease;
    }
    
    .canvas-note-textarea:focus {
        border: 2px solid #1a73e8;
        border-radius: 6px;
        padding: 10px; /* 调整padding以补偿border */
    }
    
    .canvas-note-textarea::placeholder {
        color: #999;
        font-style: italic;
    }
    
    /* 移除原有的复杂笔记样式 */
    .product-note,
    .note-header,
    .note-content,
    .note-footer,
    .note-btn,
    .note-save-btn,
    .note-delete-btn {
        display: none !important;
    }
    
    /* 移除原有的笔记指示器 */
    .product-card.has-note::after {
        display: none;
    }
</style>
{% endblock %}

{% block content %}


    <div class="product-display-area">
        {% if selected_datasource_id and selected_datasource_info %}
            {# Display Area: Error, Grid, or Prompt #}
            {% if data_error_message %}
                <div style="color: red; border: 1px solid red; padding: 10px; margin-bottom: 15px;">
                    <strong>数据加载错误:</strong> {{ data_error_message }}
                </div>
            {% elif selected_x_axis and selected_y_axis and grouped_data is defined %}


                 <div id="productCanvas" class="product-canvas">
                     <!-- SVG连线容器 -->
                     <svg class="note-lines-container" id="noteLinesContainer">
                         <!-- 连线将通过JavaScript动态添加 -->
                     </svg>
                     <table class="product-table">
                         <thead>
                             {% if x_axis_hierarchy and x_axis_hierarchy|length > 0 %}
                                 {# 二级分类：显示合并标题 #}
                                 {# 第一行：主分类标题 #}
                                 <tr class="main-category-row">
                                     {% if y_axis_hierarchy and y_axis_hierarchy|length > 0 %}
                                         <th colspan="2" rowspan="2" class="main-category-header merged-corner-cell" style="width: 240px; font-size: 28px !important; text-align: center; height: 54px !important;"> \ </th>
                                     {% else %}
                                         <th rowspan="2" class="main-category-header" style="width: 150px; font-size: 28px !important; text-align: center; height: 54px !important;"> \ </th>
                                     {% endif %}
                                     {% for x_primary, x_secondaries in x_axis_hierarchy.items() %}
                                         <th colspan="{{ x_secondaries|length }}" class="main-category-header">{{ x_primary }}</th>
                                     {% endfor %}
                                 </tr>
                                 {# 第二行：子分类标题 #}
                                 <tr class="sub-category-row">
                                     {% for x_primary, x_secondaries in x_axis_hierarchy.items() %}
                                         {% for x_secondary in x_secondaries %}
                                             <th class="sub-category-header" title="{{ x_primary }} | {{ x_secondary }}">{{ x_secondary if x_secondary and x_secondary != '' else '-' }}</th>
                                         {% endfor %}
                                     {% endfor %}
                                 </tr>
                             {% else %}
                                 {# 单级分类：原有显示方式 #}
                                 <tr>
                                     {% if y_axis_hierarchy and y_axis_hierarchy|length > 0 %}
                                         <th colspan="2" class="merged-corner-cell" style="width: 240px; font-size: 28px !important; text-align: center; height: 108px !important;"> \ </th>
                                     {% else %}
                                         <th style="width: 150px; font-size: 28px !important; text-align: center; height: 108px !important;"> \ </th>
                                     {% endif %}
                                     {% for x_val in x_axis_values %}
                                         <th title="{{ x_val }}">{{ x_val }}</th>
                                     {% endfor %}
                                 </tr>
                             {% endif %}
                         </thead>
                         <tbody>
                             {% if y_axis_hierarchy and y_axis_hierarchy|length > 0 %}
                                 {# 二级分类：Y轴格子效果 #}
                                 {% set y_row_index = 0 %}
                                 {% for y_primary, y_secondaries in y_axis_hierarchy.items() %}
                                     {% if y_secondaries and y_secondaries|length > 0 %}
                                         {% for y_secondary in y_secondaries %}
                                         <tr>
                                             {% if loop.index0 == 0 %}
                                                 {# 主分类标题，跨越多行 #}
                                                 <th rowspan="{{ y_secondaries|length }}" class="y-main-category-header" title="{{ y_primary }}">
                                                     {{ y_primary }}
                                                 </th>
                                             {% endif %}
                                             {# 子分类标题 #}
                                             <th class="y-sub-category-header" title="{{ y_primary }} | {{ y_secondary }}">
                                                 {{ y_secondary if y_secondary and y_secondary != '' else '-' }}
                                             </th>
                                             {% for x_val in x_axis_values %}
                                                 {% set y_combined = (y_primary + " | " + y_secondary) if y_secondary and y_secondary and y_secondary != '' else y_primary %}
                                                 <td> {# Cell #}
                                                     {% set cell_data = grouped_data.get(x_val, {}).get(y_combined, []) %}
                                             {% if cell_data %}
                                                 <div class="grid-cell-content has-products" data-x-axis="{{ x_val }}" data-y-axis="{{ y_combined }}">
                                                     <button class="add-virtual-product-btn" onclick="openVirtualProductModal('{{ x_val }}', '{{ y_combined }}')" title="X: '{{ x_val }}' Y: '{{ y_combined }}'">+</button>
                                                 {% for product_item in cell_data %}
                                                     {% set index_col_name = selected_datasource_info.get('index_column') %}
                                                     <div class="product-card" data-product-index="{{ product_item.get(index_col_name, '') }}">
                                                         {% set img_col = selected_datasource_info.get('image_column_name') %}
                                                         <div class="product-image-container">
                                                             {% if img_col and product_item.get(img_col) %}
                                                                 <img src="{{ product_item.get(img_col) }}" alt="产品图片" class="editable-image">
                                                             {% else %}
                                                                 <div class="no-image-placeholder editable-image">暂无图片</div>
                                                             {% endif %}
                                                             
                                                             {# 移除ID标签显示，让卡片更简洁 #}
                                                             {# <div class="product-id-tag">ID: {{ loop.index }}</div> #}
                                                         </div>
                                                         
                                                         <div class="product-info-container">
                                                             {% set index_col_name = selected_datasource_info.get('index_column') %}
                                                             {# 注释掉SKU编码的显示 #}
                                                             {# {% if index_col_name and product_item.get(index_col_name) %}
                                                                 <div class="product-name" title="{{ product_item.get(index_col_name) }}">{{ product_item.get(index_col_name) }}</div>
                                                             {% else %}
                                                                 <div class="product-name" title="未知产品">未知产品</div>
                                                             {% endif %} #}

                                                             {% set price_col_name = selected_datasource_info.get('product_price_column') %}
                                                             {% if price_col_name and product_item.get(price_col_name) is not none %}
                                                                 {% set price_value = product_item.get(price_col_name) %}
                                                                 {% if price_value is number %}
                                                                     <div class="product-price">¥{{ "%.2f"|format(price_value) }}</div>
                                                                 {% else %}
                                                                     <div class="product-price" style="color: #888;">{{ price_value }}</div>
                                                                 {% endif %}
                                                             {% elif price_col_name %}
                                                                  <div class="product-price" style="color: #888;">价格未知</div>
                                                             {% endif %}
                                                             
                                                             {# 注释掉底部的店铺名和支付信息显示 #}
                                                             {# <div class="product-meta">
                                                                 <span class="store-name" title="店铺名称">{{ x_val[:10] }}</span>
                                                                 <span class="payment-info">支付: {{ y_val[:5] }}</span>
                                                             </div> #}
                                                         </div>
                                                     </div>
                                                 {% endfor %}
                                                 </div>
                                             {% else %}
                                                 <div class="grid-cell-content" data-x-axis="{{ x_val }}" data-y-axis="{{ y_combined }}">
                                                     <div class="empty-hover-area">
                                                         <button class="empty-add-button" onclick="openVirtualProductModal('{{ x_val }}', '{{ y_combined }}')" title="X: '{{ x_val }}' Y: '{{ y_combined }}'">+</button>
                                                     </div>
                                                 </div>
                                             {% endif %}
                                                 </td>
                                             {% endfor %}
                                         </tr>
                                         {% endfor %}
                                     {% endif %}
                                 {% endfor %}
                             {% else %}
                                 {# 单级分类：原有显示方式 #}
                                 {% for y_val in y_axis_values %}
                                     <tr>
                                         {% if y_axis_hierarchy and y_axis_hierarchy|length > 0 %}
                                             <th title="{{ y_val }}">{{ y_val }}</th> {# Y-axis main category #}
                                             <th title="{{ y_val }}">-</th> {# Y-axis sub category placeholder #}
                                         {% else %}
                                             <th class="y-main-category-header" title="{{ y_val }}">{{ y_val }}</th> {# Y-axis header cell #}
                                         {% endif %}
                                         {% for x_val in x_axis_values %}
                                             <td> {# Cell #}
                                                 {% set cell_data = grouped_data.get(x_val, {}).get(y_val, []) %}
                                                 {% if cell_data %}
                                                     <div class="grid-cell-content has-products" data-x-axis="{{ x_val }}" data-y-axis="{{ y_val }}">
                                                         <button class="add-virtual-product-btn" onclick="openVirtualProductModal('{{ x_val }}', '{{ y_val }}')" title="X: '{{ x_val }}' Y: '{{ y_val }}'">+</button>
                                                     {% for product_item in cell_data %}
                                                         {% set index_col_name = selected_datasource_info.get('index_column') %}
                                                         <div class="product-card" data-product-index="{{ product_item.get(index_col_name, '') }}">
                                                             {% set img_col = selected_datasource_info.get('image_column_name') %}
                                                             <div class="product-image-container">
                                                                 {% if img_col and product_item.get(img_col) %}
                                                                     <img src="{{ product_item.get(img_col) }}" alt="产品图片" class="editable-image">
                                                                 {% else %}
                                                                     <div class="no-image-placeholder editable-image">暂无图片</div>
                                                                 {% endif %}
                                                             </div>

                                                             <div class="product-info-container">
                                                                 {% set price_col_name = selected_datasource_info.get('product_price_column') %}
                                                                 {% if price_col_name and product_item.get(price_col_name) is not none %}
                                                                     {% set price_value = product_item.get(price_col_name) %}
                                                                     {% if price_value is number %}
                                                                         <div class="product-price">¥{{ "%.2f"|format(price_value) }}</div>
                                                                     {% else %}
                                                                         <div class="product-price" style="color: #888;">{{ price_value }}</div>
                                                                     {% endif %}
                                                                 {% elif price_col_name %}
                                                                      <div class="product-price" style="color: #888;">价格未知</div>
                                                                 {% endif %}
                                                             </div>
                                                         </div>
                                                     {% endfor %}
                                                     </div>
                                                 {% else %}
                                                     <div class="grid-cell-content" data-x-axis="{{ x_val }}" data-y-axis="{{ y_val }}">
                                                         <div class="empty-hover-area">
                                                             <button class="empty-add-button" onclick="openVirtualProductModal('{{ x_val }}', '{{ y_val }}')" title="X: '{{ x_val }}' Y: '{{ y_val }}'">+</button>
                                                         </div>
                                                     </div>
                                                 {% endif %}
                                             </td>
                                         {% endfor %}
                                     </tr>
                                 {% endfor %}
                             {% endif %}
                         </tbody>
                     </table>
                 </div>
            {% elif product_data %}
                {# Data loaded, but X/Y not selected #}
                <p style="text-align: center; color: #666; padding: 40px; font-size: 16px;">请从页面顶部选择 X 轴和 Y 轴字段以生成产品分布视图</p>
            {% else %}
                 {# No product data loaded (e.g., Feishu pending, empty CSV but no error) #}
                {% if selected_datasource_info.type == 'feishu_lark_sheets_url' %}
                    <p style="text-align: center; color: #666; padding: 40px; font-size: 16px;">飞书链接数据加载功能正在开发中</p>
                {% elif selected_datasource_info.type == 'local_csv' and not data_error_message %}
                     <p style="text-align: center; color: #666; padding: 40px; font-size: 16px;">CSV数据源已选择，但未加载到任何数据行</p>
                {% endif %}
            {% endif %}

        {% elif available_datasources and available_datasources|length > 0 %}
            <p style="text-align: center; color: #666; padding: 40px; font-size: 16px;">请从页面顶部的导航栏中选择一个数据源来开始查看产品数据</p>
        {% elif session.get('user_id') %}
             <p style="text-align: center; color: #666; padding: 40px; font-size: 16px;">目前系统还没有配置任何数据源。请联系管理员在后台添加数据源</p>
        {% else %}
            <p style="text-align: center; color: #666; padding: 40px; font-size: 16px;">欢迎来到 ProductView2！请先 <a href="{{ url_for('login') }}">登录</a>，然后从顶部选择数据源查看产品</p>
        {% endif %}
    </div>
    
    <!-- 画布内笔记框模板 -->
    <div id="canvasNoteTemplate" class="canvas-note">
        <div class="canvas-note-header">
            <div class="drag-indicator"></div>
            <div class="canvas-note-close"></div>
        </div>
        <textarea class="canvas-note-textarea" placeholder="记录想法..."></textarea>
    </div>

    <!-- 虚拟产品编辑弹窗 -->
    <div id="virtualProductModal" class="virtual-product-modal">
        <div class="virtual-product-form">
            <button class="modal-close-btn" onclick="closeVirtualProductModal()">&times;</button>
            <h3 id="modalTitle">添加虚拟产品</h3>

            <form id="virtualProductForm">
                <div class="form-group">
                    <label>产品图片</label>
                    <div class="image-upload-area" id="imageUploadArea">
                        <div class="image-upload-content">
                            <div class="image-upload-text">📷 点击上传图片或拖拽图片到此处</div>
                            <div class="image-upload-hint">支持 JPG、PNG、GIF 格式，也可以直接粘贴图片 (Ctrl+V)</div>
                        </div>
                        <input type="file" id="imageFileInput" accept="image/*" style="display: none;">
                        <img id="imagePreview" class="image-preview" style="display: none;">
                        <button type="button" class="image-remove-btn" id="imageRemoveBtn" style="display: none;">×</button>
                    </div>
                </div>

                <div class="form-group">
                    <label for="virtualProductDescription">产品描述</label>
                    <textarea id="virtualProductDescription" name="description" placeholder="请输入产品描述"></textarea>
                    <small style="color: #666; font-size: 12px;">* 产品图片和产品描述至少需要填写一项</small>
                </div>

                <div class="modal-buttons">
                    <button type="button" class="modal-btn modal-btn-secondary" onclick="closeVirtualProductModal()">取消</button>
                    <button type="submit" class="modal-btn modal-btn-primary">保存产品</button>
                </div>
            </form>
        </div>
    </div>

{% endblock %}

{% block scripts %}
<script>
    /* 将CSV表头和产品数据暴露给window对象 */
    window.csv_headers = {% if csv_headers %}{{ csv_headers|tojson|safe }}{% else %}[]{% endif %};
    window.product_data = {% if product_data %}{{ product_data|tojson|safe }}{% else %}[]{% endif %};
    window.applied_filters = {% if applied_filters %}{{ applied_filters|tojson|safe }}{% else %}null{% endif %};
    
    /* 将数据源信息和用户习惯数据暴露给window对象，供user_base.html中的脚本使用 */
    window.selected_datasource_info = {% if selected_datasource_info %}{{ selected_datasource_info|tojson|safe }}{% else %}null{% endif %};
    window.user_habits = {% if user_habits %}{{ user_habits|tojson|safe }}{% else %}[]{% endif %};

    /* 标记数据已准备就绪 */
    window.dataReady = true;
    console.log('📊 数据已准备就绪:', {
        csv_headers: window.csv_headers ? window.csv_headers.length + ' 列' : '无',
        product_data: window.product_data ? window.product_data.length + ' 条' : '无',
        selected_datasource_info: window.selected_datasource_info ? '已加载' : '无'
    });

    /* 触发自定义事件通知其他脚本数据已准备好 */
    if (typeof window.CustomEvent === 'function') {
        window.dispatchEvent(new CustomEvent('dataReady', {
            detail: {
                csv_headers: window.csv_headers,
                product_data: window.product_data,
                selected_datasource_info: window.selected_datasource_info
            }
        }));
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        /* 如果存在筛选参数，更新UI以反映当前筛选条件 */
        if (window.applied_filters) {
            console.log('当前筛选条件：', window.applied_filters);
            
            /* 尝试应用价格筛选条件到UI */
            if (window.applied_filters.price) {
                const priceMin = document.getElementById('priceMin');
                const priceMax = document.getElementById('priceMax');
                
                if (priceMin && window.applied_filters.price.min !== null) {
                    priceMin.value = window.applied_filters.price.min;
                }
                
                if (priceMax && window.applied_filters.price.max !== null) {
                    priceMax.value = window.applied_filters.price.max;
                }
            }
        }
        
        // 延迟初始化，确保所有功能按正确顺序加载
        setTimeout(() => {
            // 1. 先初始化笔记功能
            initProductNotes();
            setupScrollListener();
            setupResizeListener();

            // 调试粘性定位
            // debugStickyPositioning(); // 暂时注释掉，可能影响表格显示

            // 添加左上角单元格调试
            // debugTopLeftCells(); // 暂时注释掉，可能影响表格显示



            // 3. 然后附加产品数据和点击事件
            attachProductDataToCards();

            // 4. 最后应用标签配置
            if (typeof updateProductTagsDisplay === 'function') {
                updateProductTagsDisplay();
            }

            // 5. 初始化虚拟产品功能
            initVirtualProducts();

            // 6. 绑定清理缓存按钮
            const clearCacheBtn = document.getElementById('clearCacheBtn');
            if (clearCacheBtn) {
                clearCacheBtn.addEventListener('click', clearAllCache);
            }

            // 7. 延迟刷新虚拟产品显示（确保页面完全渲染）
            setTimeout(() => {
                console.log('延迟刷新虚拟产品显示...');
                refreshVirtualProducts();
            }, 1000);

            // 8. 初始化新的Y轴系统
            initializeYAxis();

            // 9. 设置Y轴滚动监听
            setupYAxisScrollListener();

            // 10. 导出函数注册已移到脚本定义处，这里不需要重复注册
            console.log('🎉 页面初始化完成！');

        }, 300);
    });

    // ===== Y轴系统初始化（水平冻结版本）=====
    function initializeYAxis() {
        console.log('🚀 初始化Y轴系统（水平冻结模式）...');

        // 1. 查找所有Y轴标签
        const yMainHeaders = document.querySelectorAll('tbody th:first-child');
        const ySubHeaders = document.querySelectorAll('tbody th:nth-child(2)');

        console.log(`找到 ${yMainHeaders.length} 个主分类标签，${ySubHeaders.length} 个子分类标签`);

        // 2. 处理文字反转（仅针对用户偏好）
        processYAxisTextReverse(yMainHeaders);
        processYAxisTextReverse(ySubHeaders);

        // 3. 确保水平冻结样式正确应用
        ensureYAxisHorizontalFreeze(yMainHeaders, ySubHeaders);

        console.log('✅ Y轴系统初始化完成（水平冻结已启用）');
    }

    // 处理Y轴文字反转
    function processYAxisTextReverse(headers) {
        headers.forEach((header, index) => {
            const originalText = header.textContent.trim();

            // 只处理有效的文字内容，跳过特殊字符和数字
            if (originalText &&
                originalText !== '\\' &&
                originalText !== '-' &&
                !/^\d+$/.test(originalText) &&
                !header.dataset.reversed) {

                const reversedText = originalText.split('').reverse().join('');
                header.textContent = reversedText;
                header.dataset.reversed = 'true';

                console.log(`Y轴文字反转: "${originalText}" -> "${reversedText}"`);
            }
        });
    }

    // 确保Y轴水平冻结样式正确应用
    function ensureYAxisHorizontalFreeze(yMainHeaders, ySubHeaders) {
        console.log('🎨 确保Y轴水平冻结样式正确应用...');

        // 处理主分类标签
        yMainHeaders.forEach((header, index) => {
            header.classList.add('y-main-category-header');
            // 应用水平冻结：只设置left和z-index，不设置top
            header.style.position = 'sticky';
            header.style.left = '0px';
            header.style.zIndex = '100';
            // 确保不设置top值，让垂直滚动自然进行
            header.style.removeProperty('top');
            console.log(`✅ 主分类标签 ${index + 1} 水平冻结已应用`);
        });

        // 处理子分类标签
        ySubHeaders.forEach((header, index) => {
            header.classList.add('y-sub-category-header');
            // 应用水平冻结：只设置left和z-index，不设置top
            header.style.position = 'sticky';
            header.style.left = '120px';
            header.style.zIndex = '99';
            // 确保不设置top值，让垂直滚动自然进行
            header.style.removeProperty('top');
            console.log(`✅ 子分类标签 ${index + 1} 水平冻结已应用`);
        });

        console.log('🎨 Y轴水平冻结样式应用完成');
    }

    // Y轴水平冻结监听
    function setupYAxisScrollListener() {
        console.log('📜 设置Y轴水平冻结监听...');

        const productCanvas = document.getElementById('productCanvas');
        if (!productCanvas) {
            console.warn('未找到产品画布容器');
            return;
        }

        // 监听水平滚动，确保Y轴水平冻结正常工作
        let scrollTimeout;
        productCanvas.addEventListener('scroll', () => {
            // 防抖处理，避免频繁调用
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                validateYAxisHorizontalFreeze();
            }, 100);
        });

        console.log('✅ Y轴水平冻结监听设置完成');
    }

    // 验证Y轴水平冻结
    function validateYAxisHorizontalFreeze() {
        const yHeaders = document.querySelectorAll('tbody th:first-child, tbody th:nth-child(2)');

        yHeaders.forEach((header, index) => {
            const styles = window.getComputedStyle(header);
            const position = styles.position;
            const left = styles.left;

            // 检查水平冻结是否正常
            if (position !== 'sticky') {
                console.warn(`Y轴标签 ${index + 1} 水平冻结异常，重新应用`);
                header.style.position = 'sticky';
                header.style.left = header.cellIndex === 0 ? '0px' : '120px';
                header.style.zIndex = header.cellIndex === 0 ? '100' : '99';
            }

            // 确保没有top值（保持垂直自然滚动）
            if (styles.top !== 'auto' && styles.top !== '0px') {
                header.style.removeProperty('top');
            }
        });
    }





    // 将产品数据附加到产品卡片DOM元素
    function attachProductDataToCards() {
        const productCards = document.querySelectorAll('.product-card');
        
        console.log('开始附加产品数据，找到', productCards.length, '个产品卡片');
        
        if (!productCards.length || !window.product_data) {
            console.log('没有产品卡片或产品数据');
            return;
        }
        
        // 获取索引列名称
        const indexColumnName = window.selected_datasource_info?.index_column;
        if (!indexColumnName) {
            console.log('没有找到索引列名称');
            return;
        }
        
        // 逐个处理卡片
        productCards.forEach((card, cardIndex) => {
            let productData = null;
            
            // 优先方法：通过data-product-index属性精确匹配
            const productIndex = card.getAttribute('data-product-index');
            if (productIndex) {
                productData = window.product_data.find(item => {
                    return String(item[indexColumnName]) === String(productIndex);
                });
            }
            
            // 备用方法1：通过价格匹配（当索引匹配失败时）
            if (!productData) {
                const priceElement = card.querySelector('.product-price');
                if (priceElement) {
                    const priceText = priceElement.textContent.replace(/[￥¥,]/g, '').trim();
                    const cardPrice = parseFloat(priceText);
                    
                    if (!isNaN(cardPrice)) {
                        const priceColumnName = window.selected_datasource_info?.product_price_column;
                        if (priceColumnName) {
                            productData = window.product_data.find(item => {
                                const itemPrice = parseFloat(item[priceColumnName]);
                                return Math.abs(itemPrice - cardPrice) < 0.01; // 价格匹配（考虑浮点精度）
                            });
                        }
                    }
                }
            }
            
            // 将匹配到的数据附加到卡片
            if (productData) {
                card.productData = productData;
                
                // 添加图片点击事件
                const editableImage = card.querySelector('.editable-image');
                if (editableImage) {
                    console.log('为卡片', cardIndex, '添加点击事件，产品索引:', productIndex);
                    editableImage.addEventListener('click', function(e) {
                        console.log('图片被点击了！产品:', productData[indexColumnName]);
                        e.preventDefault();
                        e.stopPropagation();
                        editProductInfo(card, productData);
                    });
                } else {
                    console.log('卡片', cardIndex, '没有找到可编辑图片元素');
                }
            } else {
                console.log('卡片', cardIndex, '没有匹配到产品数据，产品索引:', productIndex);
            }
        });
        
        // 数据附加完成后，触发自定义事件
        const event = new CustomEvent('productCardsCreated');
        document.dispatchEvent(event);
    }
    
    // 编辑产品信息的函数 - 改为显示笔记框
    function editProductInfo(card, productData) {
        console.log('点击了产品图片，准备显示笔记框');
        
        // 检查笔记功能是否已初始化
        if (!canvasNote) {
            console.log('笔记功能还未初始化，延迟执行');
            setTimeout(() => editProductInfo(card, productData), 200);
            return;
        }
        
        const productId = getProductId(card);
        console.log('产品ID:', productId);
        
        // 查找或创建角标
        let indicator = card.querySelector('.note-indicator');
        if (!indicator) {
            // 如果没有角标，创建一个临时的（用于连接线绘制）
            indicator = document.createElement('div');
            indicator.className = 'note-indicator';
            indicator.setAttribute('data-product-id', productId);
            indicator.style.display = 'none'; // 临时隐藏，直到有笔记内容
            card.appendChild(indicator);
            console.log('创建了临时角标');
        }
        
        // 显示笔记框
        showCanvasNote(productId, indicator);
    }

    // ============ 画布内固定笔记功能 ============
    let productNotes = {}; // 存储所有产品笔记
    let canvasNote = null; // 画布内的单一笔记框
    let currentProductId = null; // 当前正在编辑的产品ID
    
    // 初始化笔记功能
    function initProductNotes() {
        // 从localStorage加载已保存的笔记
        const savedNotes = localStorage.getItem('productNotes');
        if (savedNotes) {
            try {
                productNotes = JSON.parse(savedNotes);
                console.log('加载了', Object.keys(productNotes).length, '条产品笔记');
            } catch (e) {
                console.error('解析笔记数据失败:', e);
                productNotes = {};
            }
        }
        
        // 为所有产品卡片添加笔记角标
        addNoteIndicators();
        
        // 初始化画布内笔记框
        initCanvasNote();
    }
    
    // 初始化画布内笔记框
    function initCanvasNote() {
        const canvas = document.getElementById('productCanvas');
        if (!canvas) return;
        
        // 创建笔记框
        const template = document.getElementById('canvasNoteTemplate');
        canvasNote = template.cloneNode(true);
        canvasNote.id = 'canvasNote';
        canvasNote.style.display = 'none'; // 初始隐藏
        
        // 添加到画布中
        canvas.appendChild(canvasNote);
        
        // 设置笔记框事件
        setupCanvasNoteEvents();
        
        // 添加拖动功能
        setupCanvasNoteDragging();
    }
    
    // 为产品卡片添加笔记角标（只为有笔记的卡片添加）
    function addNoteIndicators() {
        const productCards = document.querySelectorAll('.product-card');
        
        productCards.forEach(card => {
            const productId = getProductId(card);
            
            // 只为已有笔记的产品添加蓝色角标
            if (productNotes[productId]) {
                // 创建蓝色角标
                const indicator = document.createElement('div');
                indicator.className = 'note-indicator active';
                indicator.setAttribute('data-product-id', productId);
                
                // 点击角标切换笔记显示
                indicator.addEventListener('click', (e) => {
                    e.stopPropagation();
                    toggleCanvasNote(productId, indicator);
                });
                
                card.appendChild(indicator);
            }
        });
    }
    
    // 生成产品唯一ID
    function getProductId(productCard) {
        const allCards = document.querySelectorAll('.product-card');
        const cardIndex = Array.from(allCards).indexOf(productCard);
        return `card_${cardIndex}`;
    }
    
    // 根据产品ID获取产品卡片
    function getProductCard(productId) {
        const allCards = document.querySelectorAll('.product-card');
        const cardIndex = parseInt(productId.replace('card_', ''));
        return allCards[cardIndex] || null;
    }
    
    // 计算笔记框在产品卡片附近的最佳位置
    function positionNoteBoxNearCard(indicator) {
        if (!canvasNote || !indicator) return;
        
        const canvas = document.getElementById('productCanvas');
        if (!canvas) return;
        
        // 获取产品卡片（角标的父元素）
        const productCard = indicator.closest('.product-card');
        if (!productCard) return;
        
        // 获取画布和产品卡片的位置信息
        const canvasRect = canvas.getBoundingClientRect();
        const cardRect = productCard.getBoundingClientRect();
        
        // 计算产品卡片相对于画布的位置
        const cardRelativeX = cardRect.left - canvasRect.left + canvas.scrollLeft;
        const cardRelativeY = cardRect.top - canvasRect.top + canvas.scrollTop;
        
        // 笔记框尺寸
        const noteWidth = 250;
        const noteHeight = 170;
        const margin = 15; // 与卡片的间距
        
        // 画布可用空间
        const canvasWidth = canvas.scrollWidth;
        const canvasHeight = canvas.scrollHeight;
        
        let noteX, noteY;
        
        // 优先尝试在卡片右侧放置
        if (cardRelativeX + cardRect.width + margin + noteWidth <= canvasWidth) {
            // 右侧有足够空间
            noteX = cardRelativeX + cardRect.width + margin;
            noteY = cardRelativeY;
        } 
        // 尝试在卡片左侧放置
        else if (cardRelativeX - margin - noteWidth >= 0) {
            // 左侧有足够空间
            noteX = cardRelativeX - margin - noteWidth;
            noteY = cardRelativeY;
        }
        // 尝试在卡片下方放置
        else if (cardRelativeY + cardRect.height + margin + noteHeight <= canvasHeight) {
            // 下方有足够空间
            noteX = Math.max(0, Math.min(cardRelativeX, canvasWidth - noteWidth));
            noteY = cardRelativeY + cardRect.height + margin;
        }
        // 尝试在卡片上方放置
        else if (cardRelativeY - margin - noteHeight >= 0) {
            // 上方有足够空间
            noteX = Math.max(0, Math.min(cardRelativeX, canvasWidth - noteWidth));
            noteY = cardRelativeY - margin - noteHeight;
        }
        // 如果都没有足够空间，放在画布中央
        else {
            noteX = Math.max(0, (canvasWidth - noteWidth) / 2);
            noteY = Math.max(0, (canvasHeight - noteHeight) / 2);
        }
        
        // 确保笔记框不会超出画布边界
        noteX = Math.max(0, Math.min(noteX, canvasWidth - noteWidth));
        noteY = Math.max(0, Math.min(noteY, canvasHeight - noteHeight));
        
        // 设置笔记框位置
        canvasNote.style.left = noteX + 'px';
        canvasNote.style.top = noteY + 'px';
        
        console.log(`笔记框位置: (${noteX}, ${noteY}), 卡片位置: (${cardRelativeX}, ${cardRelativeY})`);
    }
    
    // 切换画布笔记显示/隐藏
    function toggleCanvasNote(productId, indicator) {
        if (currentProductId === productId && canvasNote.classList.contains('active')) {
            // 如果点击的是当前正在编辑的产品，隐藏笔记框
            hideCanvasNote();
        } else {
            // 显示笔记框并切换到指定产品
            showCanvasNote(productId, indicator);
        }
    }
    
    // 显示画布笔记框
    function showCanvasNote(productId, indicator) {
        if (!canvasNote) return;
        
        // 保存之前产品的笔记内容
        if (currentProductId && currentProductId !== productId) {
            saveCurrentNote();
        }
        
        // 更新当前产品ID
        currentProductId = productId;
        
        // 重置所有角标状态（只处理可见的角标）
        document.querySelectorAll('.note-indicator').forEach(ind => {
            const id = ind.getAttribute('data-product-id');
            if (productNotes[id]) {
                ind.style.display = 'block';
                ind.classList.add('active');
            } else {
                // 如果不是当前正在编辑的产品，隐藏没有笔记的角标
                if (id !== productId) {
                    ind.style.display = 'none';
                    ind.classList.remove('active');
                }
            }
        });
        
        // 临时显示当前角标（用于连接线绘制）
        indicator.style.display = 'block';
        indicator.classList.add('active');
        
        // 计算笔记框的最佳位置
        positionNoteBoxNearCard(indicator);
        
        // 加载笔记内容
        const textarea = canvasNote.querySelector('.canvas-note-textarea');
        if (productNotes[productId]) {
            textarea.value = productNotes[productId].content || '';
        } else {
            textarea.value = '';
        }
        
        // 显示笔记框
        canvasNote.style.display = 'block';
        canvasNote.classList.add('active');
        
        // 绘制连接线
        drawConnectionLine(indicator, canvasNote);
        
        // 焦点到文本域
        setTimeout(() => textarea.focus(), 100);
        
        console.log(`显示产品 ${productId} 的笔记`);
    }
    
    // 隐藏画布笔记框
    function hideCanvasNote() {
        if (!canvasNote) return;
        
        // 保存当前笔记内容（这会自动处理角标的显示/隐藏）
        saveCurrentNote();
        
        // 清除连接线
        clearConnectionLines();
        
        // 隐藏笔记框
        canvasNote.style.display = 'none';
        canvasNote.classList.remove('active');
        
        // 清空当前产品ID
        currentProductId = null;
        
        console.log('隐藏笔记框');
    }
    
    // 保存当前笔记内容
    function saveCurrentNote() {
        if (!currentProductId || !canvasNote) return;
        
        const textarea = canvasNote.querySelector('.canvas-note-textarea');
        const content = textarea.value.trim();
        
        const indicator = document.querySelector(`[data-product-id="${currentProductId}"]`);
        
        if (content) {
            // 保存笔记
            productNotes[currentProductId] = {
                content: content,
                updatedAt: new Date().toISOString()
            };
            
            // 显示角标
            if (indicator) {
                indicator.style.display = 'block';
                indicator.classList.add('active');
            }
        } else {
            // 删除空笔记
            delete productNotes[currentProductId];
            
            // 隐藏角标
            if (indicator) {
                indicator.style.display = 'none';
                indicator.classList.remove('active');
            }
        }
        
        localStorage.setItem('productNotes', JSON.stringify(productNotes));
        
        console.log(`笔记已保存: ${currentProductId}`, content ? '有内容' : '已清空');
    }
    
    // 设置画布笔记框事件
    function setupCanvasNoteEvents() {
        if (!canvasNote) return;
        
        const textarea = canvasNote.querySelector('.canvas-note-textarea');
        const closeBtn = canvasNote.querySelector('.canvas-note-close');
        
        // 关闭按钮事件
        if (closeBtn) {
            closeBtn.addEventListener('click', function(e) {
                e.stopPropagation();
                hideCanvasNote();
            });
        }
        
        // 自动保存（延迟保存）
        let saveTimeout;
        textarea.addEventListener('input', () => {
            clearTimeout(saveTimeout);
            saveTimeout = setTimeout(() => {
                saveCurrentNote();
                
                // 给用户一个保存成功的视觉反馈
                const content = textarea.value.trim();
                if (content) {
                    textarea.style.borderColor = '#28a745';
                    setTimeout(() => {
                        textarea.style.borderColor = '#1a73e8';
                    }, 500);
                }
            }, 1000);
        });
        
        // 点击外部隐藏笔记
        document.addEventListener('click', function(e) {
            if (canvasNote && canvasNote.classList.contains('active') && 
                !canvasNote.contains(e.target) && 
                !e.target.classList.contains('note-indicator')) {
                hideCanvasNote();
            }
        });
        
        // ESC键隐藏笔记
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && canvasNote && canvasNote.classList.contains('active')) {
                hideCanvasNote();
            }
        });
    }
    
    // 设置画布笔记框拖动功能
    function setupCanvasNoteDragging() {
        if (!canvasNote) return;
        
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
        
        // 获取拖动手柄
        const dragHandle = canvasNote.querySelector('.canvas-note-header');
        if (!dragHandle) return;
        
        // 鼠标按下开始拖动（只在拖动手柄上有效）
        dragHandle.addEventListener('mousedown', function(e) {
            // 如果点击的是关闭按钮，不启动拖动
            if (e.target.classList.contains('canvas-note-close') || 
                e.target.closest('.canvas-note-close')) {
                return;
            }
            
            isDragging = true;
            canvasNote.classList.add('dragging');
            
            const rect = canvasNote.getBoundingClientRect();
            const canvasRect = document.getElementById('productCanvas').getBoundingClientRect();
            
            // 计算相对于画布的偏移
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            
            // 添加拖动时的视觉反馈
            dragHandle.style.background = 'linear-gradient(135deg, #0a52a3, #083d7a)';
            
            e.preventDefault();
            e.stopPropagation();
        });
        
        // 鼠标移动拖动笔记框
        document.addEventListener('mousemove', function(e) {
            if (!isDragging || !canvasNote) return;
            
            const canvas = document.getElementById('productCanvas');
            const canvasRect = canvas.getBoundingClientRect();
            
            // 计算相对于画布的新位置
            let newLeft = e.clientX - canvasRect.left - dragOffset.x + canvas.scrollLeft;
            let newTop = e.clientY - canvasRect.top - dragOffset.y + canvas.scrollTop;
            
            // 边界检查（相对于画布）
            const maxLeft = canvas.scrollWidth - 250;
            const maxTop = canvas.scrollHeight - 170; // 更新为新的笔记框高度
            
            newLeft = Math.max(0, Math.min(newLeft, maxLeft));
            newTop = Math.max(0, Math.min(newTop, maxTop));
            
            canvasNote.style.left = newLeft + 'px';
            canvasNote.style.top = newTop + 'px';
            
            // 拖动时实时更新连接线
            if (currentProductId) {
                const indicator = document.querySelector(`[data-product-id="${currentProductId}"]`);
                if (indicator) {
                    // 延迟更新连接线，避免拖动时频繁重绘
                    clearTimeout(window.dragUpdateTimeout);
                    window.dragUpdateTimeout = setTimeout(() => {
                        drawConnectionLine(indicator, canvasNote);
                    }, 16); // 约60fps的更新频率
                }
            }
            
            e.preventDefault();
        });
        
        // 鼠标释放结束拖动
        document.addEventListener('mouseup', function(e) {
            if (isDragging) {
                isDragging = false;
                canvasNote.classList.remove('dragging');
                
                // 恢复拖动手柄的样式
                const dragHandle = canvasNote.querySelector('.canvas-note-header');
                if (dragHandle) {
                    dragHandle.style.background = 'linear-gradient(135deg, #1a73e8, #0d62cb)';
                }
            }
        });
    }
    
    // 当产品卡片重新创建时，重新初始化
    document.addEventListener('productCardsCreated', function() {
        console.log('产品卡片创建完成，重新初始化笔记功能');
        
        // 隐藏当前笔记框
        if (canvasNote && canvasNote.classList.contains('active')) {
            hideCanvasNote();
        }
        
        // 重新添加角标
        setTimeout(() => {
            addNoteIndicators();
            
            // 重新初始化画布笔记框（如果画布结构发生变化）
            if (!document.getElementById('canvasNote')) {
                initCanvasNote();
            }
            
            // 重新设置监听器
            setupScrollListener();
            setupResizeListener();
        }, 300);
    });

    // ============ 连接线绘制功能 ============
    
    // 绘制连接线
    function drawConnectionLine(indicator, noteBox) {
        if (!indicator || !noteBox) return;
        
        // 清除之前的连接线
        clearConnectionLines();
        
        const canvas = document.getElementById('productCanvas');
        const svgContainer = document.getElementById('noteLinesContainer');
        
        if (!canvas || !svgContainer) return;
        
        // 获取画布的位置信息
        const canvasRect = canvas.getBoundingClientRect();
        const canvasScrollLeft = canvas.scrollLeft;
        const canvasScrollTop = canvas.scrollTop;
        
        // 获取角标的位置（相对于画布）
        const indicatorRect = indicator.getBoundingClientRect();
        const indicatorX = indicatorRect.left - canvasRect.left + canvasScrollLeft + indicatorRect.width / 2;
        const indicatorY = indicatorRect.top - canvasRect.top + canvasScrollTop + indicatorRect.height / 2;
        
        // 获取笔记框的位置（相对于画布）
        const noteRect = noteBox.getBoundingClientRect();
        const noteX = noteRect.left - canvasRect.left + canvasScrollLeft;
        const noteY = noteRect.top - canvasRect.top + canvasScrollTop;
        
        // 计算连接点（笔记框边缘的最近点）
        let connectionX, connectionY;
        
        // 判断角标相对于笔记框的位置，选择最近的连接点
        if (indicatorX < noteX) {
            // 角标在笔记框左侧
            connectionX = noteX;
            connectionY = Math.max(noteY, Math.min(noteY + 170, indicatorY));
        } else if (indicatorX > noteX + 250) {
            // 角标在笔记框右侧
            connectionX = noteX + 250;
            connectionY = Math.max(noteY, Math.min(noteY + 170, indicatorY));
        } else {
            // 角标在笔记框上方或下方
            connectionX = Math.max(noteX, Math.min(noteX + 250, indicatorX));
            if (indicatorY < noteY) {
                // 角标在笔记框上方
                connectionY = noteY;
            } else {
                // 角标在笔记框下方
                connectionY = noteY + 170;
            }
        }
        
        // 更新SVG容器尺寸
        svgContainer.style.width = canvas.scrollWidth + 'px';
        svgContainer.style.height = canvas.scrollHeight + 'px';
        svgContainer.setAttribute('viewBox', `0 0 ${canvas.scrollWidth} ${canvas.scrollHeight}`);
        
        // 创建连接线路径
        const line = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        
        // 使用贝塞尔曲线创建平滑的连接线
        const controlPointOffset = 30;
        let pathData;
        
        if (Math.abs(indicatorX - connectionX) > Math.abs(indicatorY - connectionY)) {
            // 水平方向的曲线
            const cp1X = indicatorX + (connectionX > indicatorX ? controlPointOffset : -controlPointOffset);
            const cp1Y = indicatorY;
            const cp2X = connectionX + (indicatorX > connectionX ? controlPointOffset : -controlPointOffset);
            const cp2Y = connectionY;
            
            pathData = `M ${indicatorX} ${indicatorY} C ${cp1X} ${cp1Y}, ${cp2X} ${cp2Y}, ${connectionX} ${connectionY}`;
        } else {
            // 垂直方向的曲线
            const cp1X = indicatorX;
            const cp1Y = indicatorY + (connectionY > indicatorY ? controlPointOffset : -controlPointOffset);
            const cp2X = connectionX;
            const cp2Y = connectionY + (indicatorY > connectionY ? controlPointOffset : -controlPointOffset);
            
            pathData = `M ${indicatorX} ${indicatorY} C ${cp1X} ${cp1Y}, ${cp2X} ${cp2Y}, ${connectionX} ${connectionY}`;
        }
        
        line.setAttribute('d', pathData);
        line.setAttribute('class', 'note-line');
        line.setAttribute('id', 'connectionLine');
        
        svgContainer.appendChild(line);
        
        // 添加连接点标记
        const startDot = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        startDot.setAttribute('cx', indicatorX);
        startDot.setAttribute('cy', indicatorY);
        startDot.setAttribute('r', '3');
        startDot.setAttribute('fill', '#1a73e8');
        startDot.setAttribute('class', 'connection-dot');
        svgContainer.appendChild(startDot);
        
        const endDot = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
        endDot.setAttribute('cx', connectionX);
        endDot.setAttribute('cy', connectionY);
        endDot.setAttribute('r', '3');
        endDot.setAttribute('fill', '#1a73e8');
        endDot.setAttribute('class', 'connection-dot');
        svgContainer.appendChild(endDot);
    }
    
    // 清除所有连接线
    function clearConnectionLines() {
        const svgContainer = document.getElementById('noteLinesContainer');
        if (svgContainer) {
            // 清除所有连接线和连接点
            const lines = svgContainer.querySelectorAll('.note-line, .connection-dot');
            lines.forEach(line => line.remove());
        }
    }
    
    // 监听画布滚动，更新连接线位置
    function setupScrollListener() {
        const canvas = document.getElementById('productCanvas');
        if (canvas) {
            canvas.addEventListener('scroll', function() {
                if (currentProductId && canvasNote && canvasNote.classList.contains('active')) {
                    const indicator = document.querySelector(`[data-product-id="${currentProductId}"]`);
                    if (indicator) {
                        // 延迟更新，避免滚动时频繁重绘
                        clearTimeout(window.scrollUpdateTimeout);
                        window.scrollUpdateTimeout = setTimeout(() => {
                            drawConnectionLine(indicator, canvasNote);
                        }, 50);
                    }
                }
            });
        }
    }

    // 调试粘性定位功能
    function debugStickyPositioning() {
        console.log('=== 粘性定位调试开始 ===');

        const canvas = document.getElementById('productCanvas');
        const table = canvas ? canvas.querySelector('.product-table') : null;

        if (!canvas) {
            console.error('未找到产品画布');
            return;
        }

        if (!table) {
            console.error('未找到产品表格');
            return;
        }

        // 检查滚动容器
        const canvasStyles = window.getComputedStyle(canvas);
        console.log('画布样式检查:');
        console.log('- overflow:', canvasStyles.overflow);
        console.log('- max-height:', canvasStyles.maxHeight);
        console.log('- position:', canvasStyles.position);

        // 检查表格尺寸
        console.log('尺寸检查:');
        console.log('- 画布尺寸:', canvas.offsetWidth + 'x' + canvas.offsetHeight);
        console.log('- 表格尺寸:', table.offsetWidth + 'x' + table.offsetHeight);
        console.log('- 滚动尺寸:', canvas.scrollWidth + 'x' + canvas.scrollHeight);

        // 检查Y轴元素 - 使用多种选择器确保找到所有元素
        const yMainHeaders = table.querySelectorAll('.y-main-category-header, th.y-main-category-header, tbody th.y-main-category-header');
        const ySubHeaders = table.querySelectorAll('.y-sub-category-header, th.y-sub-category-header, tbody th.y-sub-category-header');

        console.log('Y轴元素检查:');
        console.log('- 主分类数量:', yMainHeaders.length);
        console.log('- 子分类数量:', ySubHeaders.length);

        // 为Y轴元素添加调试样式
        yMainHeaders.forEach((header, index) => {
            header.classList.add('debug-sticky');
            const styles = window.getComputedStyle(header);
            console.log(`主分类${index + 1}样式:`, {
                position: styles.position,
                left: styles.left,
                top: styles.top,
                zIndex: styles.zIndex
            });
        });

        ySubHeaders.forEach((header, index) => {
            header.classList.add('debug-sticky');
            const styles = window.getComputedStyle(header);
            console.log(`子分类${index + 1}样式:`, {
                position: styles.position,
                left: styles.left,
                top: styles.top,
                zIndex: styles.zIndex
            });
        });


    }

    // 监听窗口大小变化，重新绘制连接线和调整笔记框位置
    function setupResizeListener() {
        window.addEventListener('resize', function() {
            if (currentProductId && canvasNote && canvasNote.classList.contains('active')) {
                const indicator = document.querySelector(`[data-product-id="${currentProductId}"]`);
                if (indicator) {
                    // 延迟更新，避免resize时频繁重绘
                    clearTimeout(window.resizeUpdateTimeout);
                    window.resizeUpdateTimeout = setTimeout(() => {
                        // 重新计算笔记框位置
                        positionNoteBoxNearCard(indicator);
                        // 重新绘制连接线
                        drawConnectionLine(indicator, canvasNote);
                    }, 100);
                }
            }
        });
    }

    // 笔记功能初始化已移至主DOMContentLoaded事件中

    // ============ 虚拟产品功能 ============
    //
    // 功能说明：
    // 1. 用户可以在产品画布的空白区域点击添加虚拟产品
    // 2. 虚拟产品支持图片上传和文字描述
    // 3. 虚拟产品会以特殊样式（发光边框）显示在画布上
    // 4. 虚拟产品数据保存在localStorage中，页面刷新后仍然存在
    // 5. 虚拟产品会锁定到特定的X/Y轴位置
    //
    // 当前状态：功能已实现但存在显示问题，暂时停用
    // 问题：虚拟产品创建后不显示在画布上
    // 待修复：需要调试虚拟产品的渲染和显示逻辑
    //
    // 相关函数：
    // - initVirtualProducts(): 初始化虚拟产品功能
    // - openVirtualProductModal(): 打开虚拟产品创建弹窗
    // - saveVirtualProduct(): 保存虚拟产品数据
    // - refreshVirtualProducts(): 刷新虚拟产品显示
    // - addEmptyAreaHoverEffects(): 为空白区域添加悬停效果
    //

    // 清理缓存功能
    function clearAllCache() {
        if (confirm('确定要清理所有缓存数据吗？这将删除所有虚拟产品数据。')) {
            try {
                localStorage.clear();
                sessionStorage.clear();

                // 重新初始化虚拟产品数据
                virtualProducts = {};

                // 移除页面上的所有虚拟产品
                document.querySelectorAll('.virtual-product').forEach(el => el.remove());

                alert('缓存已清理完成！页面将刷新。');
                location.reload();
            } catch (error) {
                console.error('清理缓存时出错:', error);
                alert('清理缓存失败，请手动清理浏览器缓存。');
            }
        }
    }

    // 清理localStorage空间
    function cleanupLocalStorage() {
        try {
            // 检查localStorage使用情况
            let totalSize = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    totalSize += localStorage[key].length;
                }
            }
            console.log('localStorage总大小:', totalSize, '字符');

            // 如果超过4MB，开始清理
            if (totalSize > 4 * 1024 * 1024) {
                console.log('localStorage空间不足，开始清理...');

                // 保留重要的键
                const importantKeys = ['virtualProducts', 'user_preferences'];
                const allKeys = Object.keys(localStorage);

                allKeys.forEach(key => {
                    if (!importantKeys.includes(key)) {
                        localStorage.removeItem(key);
                        console.log('已清理:', key);
                    }
                });

                console.log('localStorage清理完成');
            }
        } catch (error) {
            console.error('清理localStorage时出错:', error);
        }
    }

    // 虚拟产品数据存储
    cleanupLocalStorage(); // 先清理空间
    let virtualProducts = JSON.parse(localStorage.getItem('virtualProducts') || '{}');

    // 调试：打印加载的虚拟产品数据
    console.log('🔍 页面加载时的虚拟产品数据:', virtualProducts);
    console.log('🔍 虚拟产品数据键值:', Object.keys(virtualProducts));
    let currentEditingProduct = null;
    let currentXAxis = null;
    let currentYAxis = null;

    // 打开虚拟产品弹窗
    function openVirtualProductModal(xAxis, yAxis, productId = null) {
        console.log('🚀 打开虚拟产品弹窗:', { xAxis, yAxis, productId });

        // 清理和验证参数
        xAxis = (xAxis || '').toString().trim();
        yAxis = (yAxis || '').toString().trim();

        console.log('🧹 清理后的参数:', { xAxis, yAxis, productId });

        // 验证参数 - 允许X轴为空（某些表格布局下可能出现）
        if (!yAxis) {
            console.error('❌ 无效的Y轴参数:', { xAxis, yAxis });
            alert('无法确定产品位置，请重试。');
            return;
        }

        // 如果X轴为空，使用特殊标识
        if (!xAxis) {
            console.warn('⚠️ X轴为空，使用默认值');
            xAxis = '_EMPTY_X_AXIS_';
        }

        currentXAxis = xAxis;
        currentYAxis = yAxis;
        currentEditingProduct = productId;

        const modal = document.getElementById('virtualProductModal');
        const form = document.getElementById('virtualProductForm');
        const title = document.getElementById('modalTitle');

        // 重置表单
        form.reset();
        clearImagePreview();

        if (productId) {
            // 编辑模式
            title.textContent = '编辑虚拟产品';
            const product = getVirtualProduct(xAxis, yAxis, productId);
            if (product) {
                document.getElementById('virtualProductDescription').value = product.description || '';

                if (product.image) {
                    showImagePreview(product.image);
                }
            }
        } else {
            // 新增模式
            title.textContent = '添加虚拟产品';
        }

        modal.classList.add('active');
        document.body.style.overflow = 'hidden';

        // 聚焦到产品描述输入框
        setTimeout(() => {
            const descInput = document.getElementById('virtualProductDescription');
            if (descInput) {
                descInput.focus();
            }
        }, 300);
    }

    // 关闭虚拟产品弹窗
    function closeVirtualProductModal() {
        console.log('🔒 开始关闭虚拟产品弹窗...');
        const modal = document.getElementById('virtualProductModal');

        if (!modal) {
            console.error('❌ 未找到弹窗元素！');
            return;
        }

        console.log('✅ 找到弹窗元素，移除active类...');
        modal.classList.remove('active');
        document.body.style.overflow = '';

        currentEditingProduct = null;
        console.log('✅ 弹窗已关闭，状态已重置');
        currentXAxis = null;
        currentYAxis = null;
    }

    // 获取虚拟产品
    function getVirtualProduct(xAxis, yAxis, productId) {
        const key = `${xAxis}|${yAxis}`;
        const products = virtualProducts[key] || [];
        return products.find(p => p.id === productId);
    }

    // 保存虚拟产品
    function saveVirtualProduct(productData) {
        console.log('💾 开始保存虚拟产品:', productData);
        console.log('📍 当前位置:', { currentXAxis, currentYAxis });

        // 确保轴值已清理 - 允许X轴为空
        let cleanXAxis = (currentXAxis || '').toString().trim();
        const cleanYAxis = (currentYAxis || '').toString().trim();

        // 处理特殊的空X轴标识
        if (cleanXAxis === '_EMPTY_X_AXIS_') {
            cleanXAxis = '';
        }

        if (!cleanYAxis) {
            console.error('❌ 无效的Y轴值:', { cleanXAxis, cleanYAxis });
            alert('保存失败：无效的位置信息');
            return;
        }

        const key = `${cleanXAxis}|${cleanYAxis}`;
        console.log('🔑 存储键:', key);

        if (!virtualProducts[key]) {
            virtualProducts[key] = [];
            console.log('创建新的产品数组');
        }

        if (currentEditingProduct) {
            // 编辑现有产品
            const index = virtualProducts[key].findIndex(p => p.id === currentEditingProduct);
            if (index !== -1) {
                virtualProducts[key][index] = { ...productData, id: currentEditingProduct };
                console.log('更新现有产品');
            }
        } else {
            // 添加新产品
            const newProduct = {
                ...productData,
                id: generateProductId(),
                createdAt: new Date().toISOString()
            };
            virtualProducts[key].push(newProduct);
            console.log('添加新产品:', newProduct);
        }

        // 保存到localStorage，添加错误处理
        try {
            // 直接保存虚拟产品数据，不进行压缩（避免数据不一致）
            localStorage.setItem('virtualProducts', JSON.stringify(virtualProducts));
            console.log('✅ 虚拟产品已保存到localStorage');
            console.log('📦 保存的数据:', virtualProducts);
            console.log('🔑 保存的键值:', Object.keys(virtualProducts));
        } catch (error) {
            console.error('保存到localStorage失败:', error);
            if (error.name === 'QuotaExceededError') {
                console.warn('localStorage空间不足，尝试清理旧数据...');

                // 方法1：清理其他应用的数据
                const keysToKeep = ['virtualProducts'];
                const allKeys = Object.keys(localStorage);
                allKeys.forEach(key => {
                    if (!keysToKeep.includes(key)) {
                        localStorage.removeItem(key);
                    }
                });

                // 方法2：如果还是不够，限制虚拟产品数量
                const maxProductsPerCell = 3;
                Object.keys(virtualProducts).forEach(key => {
                    if (virtualProducts[key].length > maxProductsPerCell) {
                        // 保留最新的产品
                        virtualProducts[key] = virtualProducts[key]
                            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                            .slice(0, maxProductsPerCell);
                    }
                });

                // 重试保存
                try {
                    localStorage.setItem('virtualProducts', JSON.stringify(virtualProducts));
                    console.log('✅ 清理后重新保存成功');
                } catch (retryError) {
                    console.error('重试保存仍然失败:', retryError);
                    alert('存储空间不足，已自动清理旧数据。如果问题持续，请清理浏览器缓存。');
                }
            }
        }

        // 刷新显示
        console.log('开始刷新显示...');
        refreshVirtualProducts();

        // 延迟再次刷新，确保DOM更新完成
        setTimeout(() => {
            console.log('🔄 延迟刷新虚拟产品显示...');
            refreshVirtualProducts();
        }, 200);
    }

    // 删除虚拟产品
    function deleteVirtualProduct(xAxis, yAxis, productId) {
        const key = `${xAxis}|${yAxis}`;
        if (virtualProducts[key]) {
            virtualProducts[key] = virtualProducts[key].filter(p => p.id !== productId);
            if (virtualProducts[key].length === 0) {
                delete virtualProducts[key];
            }
            try {
                localStorage.setItem('virtualProducts', JSON.stringify(virtualProducts));
            } catch (error) {
                console.error('删除虚拟产品时保存失败:', error);
            }
            refreshVirtualProducts();
        }
    }

    // 生成产品ID
    function generateProductId() {
        return 'virtual_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 刷新虚拟产品显示
    function refreshVirtualProducts() {
        console.log('=== 开始刷新虚拟产品显示 ===');
        console.log('当前虚拟产品数据:', virtualProducts);
        console.log('虚拟产品数据键值:', Object.keys(virtualProducts));

        // 获取所有单元格
        const allCells = document.querySelectorAll('[data-x-axis][data-y-axis]');
        console.log('页面中找到', allCells.length, '个单元格');

        // 打印前几个单元格的数据属性用于调试
        console.log('前5个单元格的数据属性:');
        for (let i = 0; i < Math.min(5, allCells.length); i++) {
            const cell = allCells[i];
            console.log(`单元格${i}: X="${cell.dataset.xAxis}", Y="${cell.dataset.yAxis}"`);
        }

        // 先清除所有现有的虚拟产品卡片
        allCells.forEach(cell => {
            const existingVirtualCards = cell.querySelectorAll('.product-card.virtual-product');
            existingVirtualCards.forEach(card => {
                console.log('移除现有虚拟产品卡片:', card);
                card.remove();
            });
        });

        // 遍历所有虚拟产品数据
        Object.keys(virtualProducts).forEach(key => {
            const [xAxis, yAxis] = key.split('|');
            const products = virtualProducts[key];
            console.log(`\n🎯 处理位置 "${xAxis}"|"${yAxis}"，产品数量:`, products.length);
            console.log('🔍 产品详情:', products);

            // 查找匹配的单元格
            let targetCell = null;

            // 方法1：精确匹配
            allCells.forEach(cell => {
                if (cell.dataset.xAxis === xAxis && cell.dataset.yAxis === yAxis) {
                    targetCell = cell;
                    console.log('✅ 精确匹配找到单元格:', cell);
                }
            });

            // 方法2：如果精确匹配失败，尝试清理空格后匹配（允许X轴为空）
            if (!targetCell) {
                console.log('⚠️ 精确匹配失败，尝试清理空格后匹配...');
                allCells.forEach(cell => {
                    const cellX = (cell.dataset.xAxis || '').trim();
                    const cellY = (cell.dataset.yAxis || '').trim();
                    const targetX = (xAxis || '').trim();
                    const targetY = (yAxis || '').trim();

                    console.log(`🔍 检查单元格: X="${cellX}", Y="${cellY}" vs 目标: X="${targetX}", Y="${targetY}"`);

                    // 允许X轴为空，但Y轴必须匹配
                    if (cellY && targetY && cellY === targetY && cellX === targetX) {
                        targetCell = cell;
                        console.log('✅ 清理空格后匹配找到单元格:', cell);
                    }
                });
            }

            // 方法3：如果还是失败，尝试部分匹配
            if (!targetCell) {
                console.log('⚠️ 清理空格后匹配失败，尝试部分匹配...');
                allCells.forEach(cell => {
                    const cellX = (cell.dataset.xAxis || '').trim();
                    const cellY = (cell.dataset.yAxis || '').trim();
                    const targetX = (xAxis || '').trim();
                    const targetY = (yAxis || '').trim();

                    // 如果Y轴匹配，X轴为空，则可能是表格结构问题
                    if (cellY === targetY && (!targetX || targetX === '')) {
                        targetCell = cell;
                        console.log('✅ 部分匹配找到单元格（Y轴匹配，X轴为空）:', cell);
                    }
                });
            }
            if (targetCell) {
                console.log('✅ 找到目标单元格，开始添加虚拟产品...');

                // 添加虚拟产品卡片
                products.forEach((product, index) => {
                    console.log(`添加虚拟产品 ${index + 1}:`, product);

                    const productCard = createVirtualProductCard(product, xAxis, yAxis);
                    console.log('创建的虚拟产品卡片:', productCard);

                    // 确保卡片有明显的样式用于调试
                    productCard.style.border = '3px solid #4CAF50';
                    productCard.style.backgroundColor = 'rgba(76, 175, 80, 0.1)';
                    productCard.style.minHeight = '200px';
                    productCard.style.margin = '5px';

                    targetCell.appendChild(productCard);
                    console.log('✅ 虚拟产品卡片已添加到单元格');
                });

                // 更新单元格状态
                if (products.length > 0) {
                    targetCell.classList.add('has-products');
                    console.log('✅ 单元格标记为has-products');

                    // 隐藏空白悬停区域
                    const emptyArea = targetCell.querySelector('.empty-hover-area');
                    if (emptyArea) {
                        emptyArea.style.display = 'none';
                        console.log('✅ 隐藏空白悬停区域');
                    }
                } else {
                    const realProducts = targetCell.querySelectorAll('.product-card:not(.virtual-product)');
                    if (realProducts.length === 0) {
                        targetCell.classList.remove('has-products');
                        // 显示空白悬停区域
                        const emptyArea = targetCell.querySelector('.empty-hover-area');
                        if (emptyArea) {
                            emptyArea.style.display = 'block';
                        }
                    }
                }
            } else {
                console.error(`❌ 未找到单元格: X轴="${xAxis}", Y轴="${yAxis}"`);
                console.log('可用的单元格:');
                allCells.forEach((cell, index) => {
                    console.log(`  ${index + 1}. X="${cell.dataset.xAxis}", Y="${cell.dataset.yAxis}"`);
                });
            }
        });

        console.log('=== 虚拟产品显示刷新完成 ===');

        // 最终验证
        const finalVirtualCards = document.querySelectorAll('.product-card.virtual-product');
        console.log('页面中现在有', finalVirtualCards.length, '个虚拟产品卡片');
        finalVirtualCards.forEach((card, index) => {
            console.log(`虚拟产品卡片 ${index + 1}:`, card);
        });

        // 刷新后重新初始化Y轴
        setTimeout(() => {
            initializeYAxis();
        }, 100);
    }

    // 创建虚拟产品卡片
    function createVirtualProductCard(product, xAxis, yAxis) {
        console.log('🎨 创建虚拟产品卡片:', product);

        const card = document.createElement('div');
        card.className = 'product-card virtual-product';
        card.setAttribute('data-product-id', product.id);
        card.setAttribute('data-virtual', 'true');

        // 添加虚拟产品特殊样式 - 使用强制样式确保显示
        card.style.cssText = `
            background: rgba(76, 175, 80, 0.15) !important;
            border: 3px solid #4CAF50 !important;
            border-radius: 8px !important;
            position: relative !important;
            display: block !important;
            z-index: 1000 !important;
            box-shadow: 0 0 15px rgba(76, 175, 80, 0.4) !important;
            min-height: 150px !important;
        `;

        card.innerHTML = `
            <div class="virtual-product-badge" style="
                position: absolute;
                top: -8px;
                right: -8px;
                background: #4CAF50;
                color: white;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: bold;
                z-index: 10;
                border: 2px solid white;
            ">V</div>
            <div class="virtual-product-actions" style="
                position: absolute;
                top: 5px;
                left: 5px;
                display: none;
                z-index: 20;
            ">
                <button class="virtual-action-btn virtual-edit-btn" onclick="openVirtualProductModal('${xAxis}', '${yAxis}', '${product.id}')" title="编辑" style="
                    background: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    padding: 4px 8px;
                    margin-right: 4px;
                    font-size: 12px;
                    cursor: pointer;
                ">✏</button>
                <button class="virtual-action-btn virtual-delete-btn" onclick="confirmDeleteVirtualProduct('${xAxis}', '${yAxis}', '${product.id}')" title="删除" style="
                    background: #f44336;
                    color: white;
                    border: none;
                    border-radius: 3px;
                    padding: 4px 8px;
                    font-size: 12px;
                    cursor: pointer;
                ">×</button>
            </div>
            <div class="product-image-container" style="margin-bottom: 10px;">
                ${product.image ?
                    `<img src="${product.image}" alt="产品图片" class="editable-image" style="width: 100%; height: auto; border-radius: 4px; max-height: 120px; object-fit: cover;">` :
                    '<div class="no-image-placeholder editable-image" style="background: rgba(255,255,255,0.7); padding: 20px; text-align: center; border-radius: 4px; color: #666;">暂无图片</div>'
                }
            </div>
            <div class="product-info-container">
                <div class="product-name" title="${product.description || '虚拟产品'}" style="
                    padding: 8px;
                    font-size: 14px;
                    color: #333;
                    background: rgba(255, 255, 255, 0.9);
                    border-radius: 4px;
                    line-height: 1.4;
                ">${product.description || '虚拟产品'}</div>
            </div>
            <div style="
                font-size: 11px;
                color: #666;
                margin-top: 8px;
                text-align: center;
                opacity: 0.7;
            ">
                📍 ${xAxis} × ${yAxis}
            </div>
        `;

        // 鼠标悬停显示操作按钮
        card.addEventListener('mouseenter', function() {
            const actions = this.querySelector('.virtual-product-actions');
            if (actions) actions.style.display = 'block';
        });

        card.addEventListener('mouseleave', function() {
            const actions = this.querySelector('.virtual-product-actions');
            if (actions) actions.style.display = 'none';
        });

        console.log('✅ 虚拟产品卡片创建完成');
        return card;
    }

    // 确认删除虚拟产品
    function confirmDeleteVirtualProduct(xAxis, yAxis, productId) {
        const product = getVirtualProduct(xAxis, yAxis, productId);
        const productName = product ? product.name : '未知产品';

        if (confirm(`确定要删除虚拟产品"${productName}"吗？`)) {
            deleteVirtualProduct(xAxis, yAxis, productId);
        }
    }

    // 图片处理功能
    function setupImageUpload() {
        const uploadArea = document.getElementById('imageUploadArea');
        const fileInput = document.getElementById('imageFileInput');
        const preview = document.getElementById('imagePreview');
        const removeBtn = document.getElementById('imageRemoveBtn');

        // 点击上传区域触发文件选择
        uploadArea.addEventListener('click', (e) => {
            if (e.target === uploadArea || e.target.classList.contains('image-upload-content') ||
                e.target.classList.contains('image-upload-text') || e.target.classList.contains('image-upload-hint')) {
                fileInput.click();
            }
        });

        // 文件选择处理
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                handleImageFile(file);
            }
        });

        // 拖拽上传
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0 && files[0].type.startsWith('image/')) {
                handleImageFile(files[0]);
            }
        });

        // 移除图片
        removeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            clearImagePreview();
        });

        // 粘贴图片支持
        document.addEventListener('paste', (e) => {
            if (document.getElementById('virtualProductModal').classList.contains('active')) {
                const items = e.clipboardData.items;
                for (let item of items) {
                    if (item.type.startsWith('image/')) {
                        const file = item.getAsFile();
                        if (file) {
                            handleImageFile(file);
                            e.preventDefault();
                        }
                    }
                }
            }
        });
    }

    // 处理图片文件
    function handleImageFile(file) {
        if (!file.type.startsWith('image/')) {
            alert('请选择图片文件');
            return;
        }

        if (file.size > 5 * 1024 * 1024) { // 5MB限制
            alert('图片文件大小不能超过5MB');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            showImagePreview(e.target.result);
        };
        reader.readAsDataURL(file);
    }

    // 显示图片预览
    function showImagePreview(imageSrc) {
        const uploadArea = document.getElementById('imageUploadArea');
        const preview = document.getElementById('imagePreview');
        const removeBtn = document.getElementById('imageRemoveBtn');
        const uploadContent = uploadArea.querySelector('.image-upload-content');

        preview.src = imageSrc;
        preview.style.display = 'block';
        removeBtn.style.display = 'block';
        uploadContent.style.display = 'none';
    }

    // 清除图片预览
    function clearImagePreview() {
        const uploadArea = document.getElementById('imageUploadArea');
        const preview = document.getElementById('imagePreview');
        const removeBtn = document.getElementById('imageRemoveBtn');
        const uploadContent = uploadArea.querySelector('.image-upload-content');
        const fileInput = document.getElementById('imageFileInput');

        preview.style.display = 'none';
        removeBtn.style.display = 'none';
        uploadContent.style.display = 'block';
        preview.src = '';
        fileInput.value = '';
    }

    // 表单提交处理
    function setupVirtualProductForm() {
        const form = document.getElementById('virtualProductForm');

        if (!form) {
            console.error('未找到虚拟产品表单元素');
            return;
        }

        // 移除之前的事件监听器（如果有的话）
        form.removeEventListener('submit', handleFormSubmit);

        // 添加新的事件监听器
        form.addEventListener('submit', handleFormSubmit);
    }

    // 表单提交处理函数
    function handleFormSubmit(e) {
        console.log('📝 表单提交事件触发！', e);
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);
        const preview = document.getElementById('imagePreview');

        const productData = {
            description: formData.get('description').trim(),
            image: preview.style.display === 'block' ? preview.src : null,
            xAxis: currentXAxis,
            yAxis: currentYAxis
        };

        console.log('📦 准备保存的产品数据:', productData);

        // 验证必填字段：图片或描述至少一项
        if (!productData.image && !productData.description) {
            console.log('⚠️ 验证失败：缺少必填字段');
            alert('请至少上传产品图片或填写产品描述');
            document.getElementById('virtualProductDescription').focus();
            return;
        }

        console.log('✅ 验证通过，开始保存产品...');

        try {
            // 保存成功消息文本（在重置变量前获取）
            const successMessage = currentEditingProduct ? '虚拟产品已更新' : '虚拟产品已添加';

            // 保存产品
            saveVirtualProduct(productData);
            console.log('✅ 产品保存成功，准备关闭弹窗...');

            // 显示成功消息
            showSuccessMessage(successMessage);
            console.log('✅ 成功消息已显示');

            // 延迟关闭弹窗，确保保存和刷新完成
            setTimeout(() => {
                closeVirtualProductModal();
                console.log('✅ 弹窗已关闭');

                // 再次刷新确保显示
                setTimeout(() => {
                    console.log('🔄 表单提交后额外刷新虚拟产品显示...');
                    refreshVirtualProducts();
                }, 500);
            }, 100);

        } catch (error) {
            console.error('❌ 保存产品时出错:', error);
            alert('保存失败，请重试');
        }
    }

    // 显示成功消息
    function showSuccessMessage(message) {
        console.log('🎉 显示成功消息:', message);
        // 创建临时提示框
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 10001;
            font-size: 14px;
            animation: slideInRight 0.3s ease;
        `;
        toast.textContent = message;

        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }

    // 初始化虚拟产品功能
    function initVirtualProducts() {
        console.log('初始化虚拟产品功能...');
        setupImageUpload();
        setupVirtualProductForm();

        // 立即尝试刷新
        refreshVirtualProducts();

        // 设置定时器确保页面完全加载后再次刷新
        setTimeout(() => {
            console.log('延迟刷新虚拟产品...');
            refreshVirtualProducts();
        }, 2000);

        // 监听页面变化
        const observer = new MutationObserver((mutations) => {
            let shouldRefresh = false;
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // 检查是否有新的单元格添加
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1 && (
                            node.classList?.contains('grid-cell-content') ||
                            node.querySelector?.('.grid-cell-content')
                        )) {
                            shouldRefresh = true;
                        }
                    });
                }
            });

            if (shouldRefresh) {
                console.log('检测到页面结构变化，刷新虚拟产品...');
                setTimeout(refreshVirtualProducts, 500);
            }
        });

        // 开始观察页面变化
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        console.log('虚拟产品功能初始化完成');

        // 键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            // ESC键关闭弹窗
            if (e.key === 'Escape' && document.getElementById('virtualProductModal').classList.contains('active')) {
                closeVirtualProductModal();
            }
        });

        // 点击弹窗外部关闭
        document.getElementById('virtualProductModal').addEventListener('click', (e) => {
            if (e.target.classList.contains('virtual-product-modal')) {
                closeVirtualProductModal();
            }
        });
    }

    // 全局函数，供调试使用
    window.testVirtualProduct = function() {
        console.log('测试虚拟产品功能...');
        openVirtualProductModal('测试X轴', '测试Y轴');
    };

    // 检查空白悬停区域
    window.checkEmptyAreas = function() {
        const areas = document.querySelectorAll('.empty-hover-area');
        console.log('找到', areas.length, '个空白悬停区域');
        areas.forEach((area, index) => {
            const parent = area.closest('[data-x-axis][data-y-axis]');
            if (parent) {
                console.log(`区域 ${index + 1}: X轴=${parent.dataset.xAxis}, Y轴=${parent.dataset.yAxis}`);
            }
        });
        return areas.length;
    };

    // 测试虚拟产品验证
    window.testValidation = function() {
        console.log('测试表单验证...');
        // 模拟只有描述的情况
        const testData = {
            description: '这是一个测试产品',
            image: null
        };

        if (!testData.image && !testData.description) {
            console.log('验证失败：需要图片或描述');
            return false;
        } else {
            console.log('验证通过：有描述内容');
            return true;
        }
    };

    // 测试添加虚拟产品
    window.testAddVirtualProduct = function(xAxis = '测试X轴', yAxis = '测试Y轴') {
        console.log('🧪 测试添加虚拟产品...');

        // 模拟产品数据
        const testProduct = {
            description: '这是一个测试虚拟产品 - ' + new Date().toLocaleTimeString(),
            image: null,
            xAxis: xAxis,
            yAxis: yAxis,
            id: generateProductId(),
            createdAt: new Date().toISOString()
        };

        console.log('📦 测试产品数据:', testProduct);

        // 保存到虚拟产品数据
        const key = `${xAxis}|${yAxis}`;
        if (!virtualProducts[key]) {
            virtualProducts[key] = [];
        }
        virtualProducts[key].push(testProduct);

        // 保存到localStorage
        localStorage.setItem('virtualProducts', JSON.stringify(virtualProducts));
        console.log('测试产品已保存:', testProduct);

        // 刷新显示
        refreshVirtualProducts();

        return testProduct;
    };

    // 确保函数在全局作用域中可用
    window.openVirtualProductModal = openVirtualProductModal;
    window.closeVirtualProductModal = closeVirtualProductModal;
    window.confirmDeleteVirtualProduct = confirmDeleteVirtualProduct;
    window.initializeYAxis = initializeYAxis;

    // 调试函数：测试Y轴系统
    window.testYAxis = function() {
        console.log('🧪 开始测试Y轴系统...');

        // 重新初始化Y轴
        initializeYAxis();

        console.log('🧪 测试完成');
    };

    // 调试函数：检查所有单元格
    window.debugCells = function() {
        console.log('=== 调试单元格信息 ===');
        const allCells = document.querySelectorAll('[data-x-axis][data-y-axis]');
        console.log('找到', allCells.length, '个单元格');

        allCells.forEach((cell, index) => {
            console.log(`单元格 ${index + 1}:`, {
                xAxis: cell.dataset.xAxis,
                yAxis: cell.dataset.yAxis,
                element: cell,
                hasProducts: cell.classList.contains('has-products')
            });
        });

        console.log('当前虚拟产品数据:', virtualProducts);
        return allCells.length;
    };

    // 调试函数：强制刷新虚拟产品
    window.forceRefreshVirtual = function() {
        console.log('强制刷新虚拟产品...');
        refreshVirtualProducts();
    };

    // 调试函数：检查页面单元格
    window.debugCells = function() {
        console.log('=== 页面单元格调试信息 ===');
        const allCells = document.querySelectorAll('[data-x-axis][data-y-axis]');
        console.log('总单元格数量:', allCells.length);

        allCells.forEach((cell, index) => {
            console.log(`单元格 ${index + 1}:`, {
                xAxis: cell.dataset.xAxis,
                yAxis: cell.dataset.yAxis,
                xAxisLength: (cell.dataset.xAxis || '').length,
                yAxisLength: (cell.dataset.yAxis || '').length,
                element: cell,
                hasProducts: cell.classList.contains('has-products'),
                children: cell.children.length
            });
        });

        console.log('=== 虚拟产品数据 ===');
        console.log('virtualProducts:', virtualProducts);
        console.log('localStorage中的数据:', JSON.parse(localStorage.getItem('virtualProducts') || '{}'));

        console.log('=== 添加按钮检查 ===');
        const addButtons = document.querySelectorAll('.empty-add-button, .add-virtual-product-btn');
        console.log('找到', addButtons.length, '个添加按钮');
        addButtons.forEach((btn, index) => {
            console.log(`按钮 ${index + 1}:`, {
                onclick: btn.getAttribute('onclick'),
                parent: btn.parentElement,
                parentData: btn.closest('[data-x-axis][data-y-axis]')?.dataset
            });
        });
    };

    // 调试函数：直接添加测试虚拟产品
    window.addTestVirtualProduct = function() {
        console.log('添加测试虚拟产品...');

        // 获取第一个空白单元格
        const firstEmptyCell = document.querySelector('.grid-cell-content:not(.has-products)');
        if (!firstEmptyCell) {
            console.error('未找到空白单元格');
            return;
        }

        const xAxis = firstEmptyCell.dataset.xAxis;
        const yAxis = firstEmptyCell.dataset.yAxis;

        console.log('使用单元格:', { xAxis, yAxis, element: firstEmptyCell });

        // 创建测试产品
        const testProduct = {
            description: '这是一个测试虚拟产品 - ' + new Date().toLocaleTimeString(),
            image: null,
            xAxis: xAxis,
            yAxis: yAxis,
            id: generateProductId(),
            createdAt: new Date().toISOString()
        };

        // 保存产品
        const key = `${xAxis}|${yAxis}`;
        if (!virtualProducts[key]) {
            virtualProducts[key] = [];
        }
        virtualProducts[key].push(testProduct);

        // 保存到localStorage
        localStorage.setItem('virtualProducts', JSON.stringify(virtualProducts));
        console.log('测试产品已保存:', testProduct);

        // 刷新显示
        refreshVirtualProducts();

        return testProduct;
    };

    // 简单的虚拟产品显示测试
    window.simpleTest = function() {
        console.log('=== 简单测试开始 ===');

        // 1. 检查页面基本结构
        const allCells = document.querySelectorAll('[data-x-axis][data-y-axis]');
        console.log('1. 找到单元格数量:', allCells.length);

        if (allCells.length === 0) {
            console.error('❌ 页面中没有找到任何单元格！');
            return false;
        }

        // 2. 选择第一个单元格进行测试
        const testCell = allCells[0];
        const xAxis = testCell.dataset.xAxis;
        const yAxis = testCell.dataset.yAxis;

        console.log('2. 测试单元格:', { xAxis, yAxis, element: testCell });

        // 3. 直接在单元格中添加虚拟产品卡片
        const testCard = document.createElement('div');
        testCard.className = 'product-card virtual-product';
        testCard.style.border = '2px solid red';
        testCard.style.padding = '10px';
        testCard.style.margin = '5px';
        testCard.style.background = 'yellow';
        testCard.innerHTML = '<div>🧪 测试虚拟产品</div>';

        console.log('3. 创建测试卡片:', testCard);

        // 4. 添加到单元格
        testCell.appendChild(testCard);
        console.log('4. 卡片已添加到单元格');

        // 5. 验证卡片是否可见
        const addedCard = testCell.querySelector('.virtual-product');
        if (addedCard) {
            console.log('✅ 测试成功！虚拟产品卡片已显示');
            console.log('卡片位置:', addedCard.getBoundingClientRect());
            return true;
        } else {
            console.error('❌ 测试失败！卡片未找到');
            return false;
        }
    };

    // 调试左上角单元格
    function debugTopLeftCells() {
        console.log('🔍 调试左上角单元格...');

        const table = document.querySelector('.product-table');
        if (!table) {
            console.error('❌ 找不到表格');
            return;
        }

        // 查找左上角的单元格
        const firstCell = table.querySelector('thead th:first-child');
        const secondCell = table.querySelector('thead th:nth-child(2)');
        const mainCategoryHeaders = table.querySelectorAll('thead th.main-category-header');
        const mergedCornerCell = table.querySelector('thead th.merged-corner-cell');

        console.log('📊 左上角单元格检查:');
        console.log('- 第一个th:', firstCell);
        console.log('- 第二个th:', secondCell);
        console.log('- 合并的角落单元格:', mergedCornerCell);
        console.log('- main-category-header数量:', mainCategoryHeaders.length);

        // 处理合并的角落单元格
        if (mergedCornerCell) {
            console.log('🔧 设置合并的角落单元格样式...');
            mergedCornerCell.style.cssText = `
                position: sticky !important;
                left: 0px !important;
                top: 0px !important;
                z-index: 999999 !important;
                background-color: #fff3e0 !important;
                border: 1px solid #ffb74d !important;
                max-width: 240px !important;
                width: 240px !important;
                min-width: 240px !important;
                font-size: 28px !important;
                text-align: center !important;
                font-weight: bold !important;
                height: 72px !important;
                max-height: 72px !important;
                min-height: 72px !important;
                line-height: 72px !important;
            `;
        }

        // 检查每个main-category-header的样式
        mainCategoryHeaders.forEach((header, index) => {
            const styles = window.getComputedStyle(header);
            console.log(`📋 main-category-header ${index + 1}:`, {
                element: header,
                textContent: header.textContent.trim(),
                position: styles.position,
                left: styles.left,
                top: styles.top,
                zIndex: styles.zIndex,
                classes: header.className,
                isFirstChild: header === header.parentElement.firstElementChild,
                isSecondChild: header === header.parentElement.children[1]
            });

            // 跳过合并的角落单元格，它已经单独处理了
            if (header.classList.contains('merged-corner-cell')) {
                return;
            }

            // 移除调试边框，使用正常样式
            if (index === 0) {
                console.log('🔧 设置第一个单元格样式...');
                header.style.cssText = `
                    position: static !important;
                    left: auto !important;
                    top: auto !important;
                    z-index: auto !important;
                    background-color: #fff3e0 !important;
                    border: 1px solid #ffb74d !important;
                    max-width: 120px !important;
                    width: 120px !important;
                    min-width: 120px !important;
                    height: 36px !important;
                    max-height: 36px !important;
                    min-height: 36px !important;
                    line-height: 36px !important;
                `;
            } else if (index === 1) {
                console.log('🔧 设置第二个单元格样式...');
                header.style.cssText = `
                    position: static !important;
                    left: auto !important;
                    top: auto !important;
                    z-index: auto !important;
                    background-color: #fff3e0 !important;
                    border: 1px solid #ffb74d !important;
                    max-width: 120px !important;
                    width: 120px !important;
                    min-width: 120px !important;
                    height: 36px !important;
                    max-height: 36px !important;
                    min-height: 36px !important;
                    line-height: 36px !important;
                `;
            } else {
                // 处理其他X轴主分类标题（如"优雅随行"等品牌名称）
                console.log(`🔧 设置第${index + 1}个单元格样式（品牌名称）...`);
                header.style.cssText = `
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
                    color: white !important;
                    font-weight: 700 !important;
                    text-align: center !important;
                    padding: 8px 6px !important;
                    border: 2px solid #5a67d8 !important;
                    position: static !important;
                    top: auto !important;
                    z-index: auto !important;
                    height: 36px !important;
                    max-height: 36px !important;
                    min-height: 36px !important;
                    line-height: 1.2 !important;
                `;
            }
        });

        // 强制设置X轴子分类标题的固定高度
        const xSubHeaders = table.querySelectorAll('.sub-category-header, th.sub-category-header, thead tr.sub-category-row th.sub-category-header');
        console.log('📊 X轴子分类单元格:');
        xSubHeaders.forEach((header, index) => {
            console.log(`🔧 设置X轴子分类 ${index + 1} 固定高度...`);
            header.style.cssText = `
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
                color: white !important;
                font-weight: 600 !important;
                text-align: center !important;
                padding: 6px 4px !important;
                border: 1px solid #e53e3e !important;
                white-space: nowrap !important;
                overflow: hidden !important;
                text-overflow: ellipsis !important;
                position: static !important;
                left: auto !important;
                top: auto !important;
                z-index: auto !important;
                height: 36px !important;
                max-height: 36px !important;
                min-height: 36px !important;
                line-height: 36px !important;
                vertical-align: middle !important;
                box-sizing: border-box !important;
            `;
        });

        // 设置Y轴标签的边界约束，防止超出预期活动范围
        const yMainHeaders = table.querySelectorAll('.y-main-category-header');
        const ySubHeaders = table.querySelectorAll('.y-sub-category-header');
        console.log('📊 Y轴主分类单元格:');
        yMainHeaders.forEach((header, index) => {
            const styles = window.getComputedStyle(header);
            console.log(`Y轴主分类 ${index + 1}:`, {
                position: styles.position,
                left: styles.left,
                top: styles.top,
                zIndex: styles.zIndex
            });

            // 不再强制降低Y轴单元格的z-index，保持正确的粘性定位
            // header.style.zIndex = '50'; // 注释掉这行，避免覆盖粘性定位

            // 手动设置Y轴标签活动范围为150px
            // 注释掉contain属性，因为它会破坏sticky定位
            header.style.cssText += `
                /* contain: layout style !important; 注释掉，因为会破坏sticky定位 */
                /* 手动限制活动范围为150px */
                max-height: 150px !important;
                /* 确保边界约束生效 */
                overflow: hidden !important;
                /* 防止超出150px的活动范围 */
                clip-path: inset(0 0 0 0) !important;
            `;
        });

        console.log('📊 Y轴子分类单元格:');
        ySubHeaders.forEach((header, index) => {
            const styles = window.getComputedStyle(header);
            console.log(`Y轴子分类 ${index + 1}:`, {
                position: styles.position,
                left: styles.left,
                top: styles.top,
                zIndex: styles.zIndex
            });
            // 强制降低Y轴单元格的z-index
            header.style.zIndex = '49';

            // 为Y轴子分类也设置150px活动范围
            header.style.cssText += `
                /* 确保子分类标签也有正确的边界约束 */
                contain: layout style !important;
                /* 手动限制活动范围为150px */
                max-height: 150px !important;
                overflow: hidden !important;
                /* 防止超出150px的活动范围 */
                clip-path: inset(0 0 0 0) !important;
            `;
        });
    }

    // 超级诊断工具
    window.superDiagnosis = function() {
        console.log('🔍 开始超级诊断...');

        // 1. 检查页面基本结构
        const allCells = document.querySelectorAll('[data-x-axis][data-y-axis]');
        console.log('📊 页面统计:', {
            总单元格数: allCells.length,
            虚拟产品卡片数: document.querySelectorAll('.virtual-product').length,
            所有产品卡片数: document.querySelectorAll('.product-card').length
        });

        // 2. 检查第一个单元格的详细信息
        if (allCells.length > 0) {
            const firstCell = allCells[0];
            console.log('🎯 第一个单元格详情:', {
                element: firstCell,
                xAxis: firstCell.dataset.xAxis,
                yAxis: firstCell.dataset.yAxis,
                位置: firstCell.getBoundingClientRect(),
                可见性: firstCell.offsetHeight > 0 && firstCell.offsetWidth > 0,
                子元素数量: firstCell.children.length,
                innerHTML长度: firstCell.innerHTML.length,
                样式: {
                    display: window.getComputedStyle(firstCell).display,
                    visibility: window.getComputedStyle(firstCell).visibility,
                    opacity: window.getComputedStyle(firstCell).opacity,
                    overflow: window.getComputedStyle(firstCell).overflow
                }
            });

            // 3. 在第一个单元格中创建一个极其明显的测试元素
            const testDiv = document.createElement('div');
            testDiv.id = 'super-test-element';
            testDiv.style.cssText = `
                position: fixed !important;
                top: 50px !important;
                left: 50px !important;
                width: 300px !important;
                height: 200px !important;
                background: red !important;
                border: 10px solid blue !important;
                z-index: 999999 !important;
                color: white !important;
                font-size: 24px !important;
                font-weight: bold !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                text-align: center !important;
                box-shadow: 0 0 50px rgba(255,0,0,0.8) !important;
            `;
            testDiv.innerHTML = '🚨 超级测试元素<br>如果你看到这个，<br>说明JS正常工作！';

            // 添加到body而不是单元格
            document.body.appendChild(testDiv);
            console.log('✅ 超级测试元素已添加到body');

            // 4. 同时在单元格中添加一个测试元素
            const cellTestDiv = document.createElement('div');
            cellTestDiv.className = 'cell-test-element';
            cellTestDiv.style.cssText = `
                background: yellow !important;
                border: 5px solid green !important;
                padding: 20px !important;
                margin: 10px !important;
                color: black !important;
                font-size: 18px !important;
                font-weight: bold !important;
                display: block !important;
                position: relative !important;
                z-index: 10000 !important;
                min-height: 100px !important;
                width: 200px !important;
            `;
            cellTestDiv.innerHTML = '🟡 单元格测试元素<br>位置: ' + firstCell.dataset.xAxis + ' × ' + firstCell.dataset.yAxis;

            firstCell.appendChild(cellTestDiv);
            console.log('✅ 单元格测试元素已添加');

            // 5. 检查添加后的状态
            setTimeout(() => {
                const superTest = document.getElementById('super-test-element');
                const cellTest = document.querySelector('.cell-test-element');

                console.log('🔍 测试元素状态检查:', {
                    超级测试元素: {
                        存在: !!superTest,
                        可见: superTest ? (superTest.offsetHeight > 0 && superTest.offsetWidth > 0) : false,
                        位置: superTest ? superTest.getBoundingClientRect() : null
                    },
                    单元格测试元素: {
                        存在: !!cellTest,
                        可见: cellTest ? (cellTest.offsetHeight > 0 && cellTest.offsetWidth > 0) : false,
                        位置: cellTest ? cellTest.getBoundingClientRect() : null,
                        父元素: cellTest ? cellTest.parentElement : null
                    }
                });

                // 6. 如果单元格测试元素不可见，尝试滚动到它
                if (cellTest && cellTest.offsetHeight === 0) {
                    console.log('⚠️ 单元格测试元素不可见，尝试滚动...');
                    cellTest.scrollIntoView({ behavior: 'smooth', block: 'center' });
                }
            }, 1000);

            return true;
        } else {
            console.error('❌ 没有找到任何单元格');
            return false;
        }
    };

    // 清理测试元素
    window.cleanupTests = function() {
        const superTest = document.getElementById('super-test-element');
        const cellTests = document.querySelectorAll('.cell-test-element');

        if (superTest) superTest.remove();
        cellTests.forEach(el => el.remove());

        console.log('🧹 测试元素已清理');
    };

    // ===== 画布导出功能 =====

    // 获取画布的完整尺寸（包括滚动区域）
    function getCanvasFullDimensions() {
        const canvas = document.getElementById('productCanvas');
        if (!canvas) return null;

        return {
            width: canvas.scrollWidth,
            height: canvas.scrollHeight,
            visibleWidth: canvas.clientWidth,
            visibleHeight: canvas.clientHeight
        };
    }

    // 临时展开画布以显示所有内容
    function expandCanvasForExport() {
        const canvas = document.getElementById('productCanvas');
        if (!canvas) return null;

        // 保存原始样式
        const originalStyles = {
            maxHeight: canvas.style.maxHeight,
            overflow: canvas.style.overflow,
            height: canvas.style.height
        };

        // 临时修改样式以显示完整内容
        canvas.style.maxHeight = 'none';
        canvas.style.overflow = 'visible';
        canvas.style.height = 'auto';

        // 确保所有内容都可见
        const table = canvas.querySelector('.product-table');
        if (table) {
            table.style.width = 'auto';
            table.style.minWidth = '100%';
        }

        return originalStyles;
    }

    // 恢复画布原始样式
    function restoreCanvasStyles(originalStyles) {
        const canvas = document.getElementById('productCanvas');
        if (!canvas || !originalStyles) return;

        canvas.style.maxHeight = originalStyles.maxHeight;
        canvas.style.overflow = originalStyles.overflow;
        canvas.style.height = originalStyles.height;

        const table = canvas.querySelector('.product-table');
        if (table) {
            table.style.width = '';
            table.style.minWidth = '';
        }
    }

    // 显示导出进度
    function showExportProgress(message) {
        const btn = document.getElementById('exportCanvasBtn');

        if (btn) {
            const svg = btn.querySelector('svg');
            if (svg) svg.style.display = 'none';
            btn.innerHTML = `<span style="font-size: 12px;">${message}</span>`;
            btn.disabled = true;
            btn.style.background = '#666';
            btn.style.color = 'white';
        }
    }

    // 隐藏导出进度
    function hideExportProgress() {
        const btn = document.getElementById('exportCanvasBtn');

        if (btn) {
            btn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M.5 9.9a.5.5 0 0 1 .5.5v2.5a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-2.5a.5.5 0 0 1 1 0v2.5a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2v-2.5a.5.5 0 0 1 .5-.5z"/>
                    <path d="M7.646 1.146a.5.5 0 0 1 .708 0l3 3a.5.5 0 0 1-.708.708L8.5 2.707V11.5a.5.5 0 0 1-1 0V2.707L5.354 4.854a.5.5 0 1 1-.708-.708l3-3z"/>
                </svg>
            `;
            btn.disabled = false;
            btn.style.background = '';
            btn.style.color = '';
        }
    }

    // 预加载所有图片
    async function preloadImages() {
        const images = document.querySelectorAll('#productCanvas img');
        const imagePromises = [];

        console.log(`🖼️ 开始预加载 ${images.length} 张图片...`);

        images.forEach((img, index) => {
            const promise = new Promise((resolve) => {
                // 增加超时机制，确保不会无限等待
                const timeoutId = setTimeout(() => {
                    console.warn(`⏰ 图片 ${index + 1} 预加载超时，继续处理:`, img.src);
                    resolve();
                }, 15000); // 15秒超时

                if (img.complete && img.naturalHeight !== 0) {
                    clearTimeout(timeoutId);
                    console.log(`✅ 图片 ${index + 1} 已加载:`, img.src);
                    resolve();
                } else {
                    const newImg = new Image();
                    newImg.crossOrigin = 'anonymous';
                    newImg.onload = () => {
                        clearTimeout(timeoutId);
                        console.log(`✅ 图片 ${index + 1} 预加载完成:`, img.src);
                        // 确保原始图片也标记为已加载
                        img.onload = null;
                        img.onerror = null;
                        resolve();
                    };
                    newImg.onerror = () => {
                        clearTimeout(timeoutId);
                        console.warn(`⚠️ 图片 ${index + 1} 预加载失败，但继续处理:`, img.src);
                        resolve();
                    };
                    newImg.src = img.src;
                }
            });
            imagePromises.push(promise);
        });

        await Promise.all(imagePromises);
        console.log('🎉 所有图片预加载完成，等待渲染...');

        // 额外等待确保图片渲染完成
        await new Promise(resolve => setTimeout(resolve, 2000));
        console.log('✅ 图片渲染等待完成');
    }

    // 超级强化的图片预加载函数
    async function superPreloadImages() {
        console.log('🚀 开始超级强化图片预加载...');

        const images = document.querySelectorAll('#productCanvas img');
        console.log(`发现 ${images.length} 张图片需要处理`);

        // 第一轮：强制刷新所有图片
        images.forEach((img, index) => {
            if (img.src) {
                const originalSrc = img.src;
                img.src = '';
                setTimeout(() => {
                    img.src = originalSrc + '?t=' + Date.now(); // 添加时间戳避免缓存
                    console.log(`🔄 强制刷新图片 ${index + 1}:`, originalSrc);
                }, index * 100); // 错开加载时间
            }
        });

        // 等待刷新完成
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 第二轮：验证所有图片都已加载
        const loadPromises = Array.from(images).map((img, index) => {
            return new Promise((resolve) => {
                const timeout = setTimeout(() => {
                    console.warn(`⏰ 图片 ${index + 1} 超时，但继续处理`);
                    resolve();
                }, 30000); // 30秒超时

                const checkLoaded = () => {
                    if (img.complete && img.naturalWidth > 0 && img.naturalHeight > 0) {
                        clearTimeout(timeout);
                        console.log(`✅ 图片 ${index + 1} 确认加载完成:`, img.src);
                        resolve();
                    } else {
                        setTimeout(checkLoaded, 500); // 每500ms检查一次
                    }
                };

                checkLoaded();
            });
        });

        await Promise.all(loadPromises);
        console.log('🎉 超级强化预加载完成！');
    }

    // 超级稳定的导出函数
    async function exportCanvasAsImage() {
        console.log('🖼️ 开始超级稳定导出...');

        let originalStyles = null;

        try {
            showExportProgress('初始化导出...');

            const canvas = document.getElementById('productCanvas');
            if (!canvas) {
                throw new Error('未找到产品画布元素');
            }

            // 强制清理内存
            if (window.gc) {
                window.gc();
            }

            showExportProgress('准备画布...');

            // 临时展开画布
            originalStyles = expandCanvasForExport();

            // 等待DOM完全更新
            await new Promise(resolve => setTimeout(resolve, 1000));

            showExportProgress('强制加载所有图片...');

            // 超级强化的图片预加载
            await superPreloadImages();

            // 更长的等待时间确保一切就绪
            showExportProgress('等待渲染完成...');
            await new Promise(resolve => setTimeout(resolve, 5000));

            showExportProgress('使用最稳定方式生成图片...');

            // 使用最保守稳定的html2canvas配置
            console.log('🎯 使用超级稳定配置生成canvas...');

            const canvasElement = await html2canvas(canvas, {
                // 最保守的设置
                useCORS: false,
                allowTaint: true,
                scale: 0.8, // 适度降低分辨率减少内存压力
                backgroundColor: '#ffffff',
                logging: true,
                imageTimeout: 60000, // 1分钟超时
                removeContainer: false, // 不移除容器
                foreignObjectRendering: false,
                // 忽略可能有问题的元素
                ignoreElements: function(element) {
                    return element.tagName === 'SCRIPT' ||
                           element.tagName === 'STYLE' ||
                           element.classList.contains('note-lines-container');
                },
                onclone: function(clonedDoc) {
                    console.log('📋 开始处理克隆文档...');

                    // 确保所有图片在克隆文档中都有正确的src
                    const images = clonedDoc.querySelectorAll('img');
                    console.log('🖼️ 克隆文档中发现', images.length, '张图片');

                    images.forEach((img, index) => {
                        if (img.src) {
                            // 移除时间戳参数，使用原始URL
                            const cleanSrc = img.src.split('?')[0];
                            img.src = cleanSrc;
                            img.crossOrigin = 'anonymous';
                            console.log(`🔧 处理图片 ${index + 1}:`, cleanSrc);
                        }
                    });

                    // 强制等待克隆文档中的图片加载
                    return new Promise((resolve) => {
                        setTimeout(() => {
                            console.log('✅ 克隆文档处理完成');
                            resolve();
                        }, 3000);
                    });
                }
            });

            // 多重验证生成的canvas
            if (!canvasElement) {
                throw new Error('html2canvas返回null');
            }
            if (canvasElement.width === 0 || canvasElement.height === 0) {
                throw new Error(`生成的canvas尺寸无效: ${canvasElement.width}x${canvasElement.height}`);
            }

            console.log('🎉 Canvas生成成功！尺寸:', canvasElement.width, 'x', canvasElement.height);

            showExportProgress('准备下载...');

            // 使用最简单可靠的下载方法
            try {
                console.log('🔄 开始转换canvas为图片数据...');

                // 分步骤处理，避免内存问题
                let imageData;
                try {
                    // 尝试最高质量
                    imageData = canvasElement.toDataURL('image/png', 1.0);
                } catch (highQualityError) {
                    console.warn('⚠️ 高质量转换失败，尝试标准质量:', highQualityError);
                    try {
                        // 降级到标准质量
                        imageData = canvasElement.toDataURL('image/png', 0.9);
                    } catch (standardQualityError) {
                        console.warn('⚠️ 标准质量转换失败，尝试压缩质量:', standardQualityError);
                        // 最后尝试压缩质量
                        imageData = canvasElement.toDataURL('image/png', 0.7);
                    }
                }

                // 验证数据
                if (!imageData || imageData === 'data:,' || imageData.length < 1000) {
                    throw new Error('生成的图片数据无效或过小');
                }

                const sizeKB = Math.round(imageData.length / 1024);
                console.log('✅ 图片数据转换成功！大小:', sizeKB, 'KB');

                // 调试模式：在新窗口预览
                if (window.location.search.includes('debug=true')) {
                    const previewWindow = window.open();
                    previewWindow.document.write(`
                        <h3>导出预览 (${sizeKB}KB)</h3>
                        <img src="${imageData}" style="max-width: 100%; height: auto; border: 1px solid #ccc;" />
                    `);
                    previewWindow.document.title = '导出预览';
                }

                // 使用最简单的下载方法
                const link = document.createElement('a');
                link.download = `产品画布_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.png`;
                link.href = imageData; // 直接使用dataURL
                link.style.display = 'none';

                // 触发下载
                document.body.appendChild(link);
                console.log('🚀 开始下载文件...');
                link.click();

                // 延迟清理
                setTimeout(() => {
                    document.body.removeChild(link);
                    console.log('🧹 下载链接已清理');
                }, 2000);

            } catch (dataError) {
                console.error('❌ 图片数据处理失败:', dataError);
                throw new Error('图片数据生成失败: ' + dataError.message);
            }

            console.log('✅ 画布导出成功');

        } catch (error) {
            console.error('❌ 导出失败:', error);
            alert('导出失败！\n\n' +
                  '错误信息: ' + error.message + '\n\n' +
                  '🔧 请尝试以下解决方案：\n' +
                  '1. 刷新页面后重新导出\n' +
                  '2. 等待所有产品图片完全加载\n' +
                  '3. 关闭其他浏览器标签页释放内存\n' +
                  '4. 使用Chrome或Edge浏览器\n\n' +
                  '💡 提示：可以在URL后添加 &debug=true 查看详细信息');
        }
        } finally {
            // 恢复原始样式
            restoreCanvasStyles(originalStyles);
            hideExportProgress();
        }
    }



    // 可靠的导出函数 - 只使用经过验证的高质量导出
    async function reliableExportCanvas() {
        try {
            showExportProgress('正在准备导出...');
            console.log('🖼️ 开始可靠导出画布...');

            // 直接调用经过验证的高质量导出函数
            await exportCanvasAsImage();

        } catch (error) {
            console.error('导出失败:', error);
            hideExportProgress();
            alert('导出失败: ' + error.message + '\n\n建议：\n1. 刷新页面后重试\n2. 关闭其他浏览器标签页释放内存');
        }
    }

    // 立即注册导出函数到全局作用域（不等待DOMContentLoaded）
    console.log('🚀 立即注册导出函数到全局作用域...');

    // 检查函数是否在当前作用域中可用
    console.log('当前作用域中的函数类型:', {
        exportCanvasAsImage: typeof exportCanvasAsImage,
        reliableExportCanvas: typeof reliableExportCanvas,
        showExportProgress: typeof showExportProgress,
        hideExportProgress: typeof hideExportProgress
    });

    // 确保函数立即可用
    if (typeof exportCanvasAsImage === 'function') {
        window.exportCanvasAsImage = exportCanvasAsImage;
        console.log('✅ exportCanvasAsImage已注册');
    } else {
        console.error('❌ exportCanvasAsImage函数未定义');
    }

    if (typeof reliableExportCanvas === 'function') {
        window.reliableExportCanvas = reliableExportCanvas;
        console.log('✅ reliableExportCanvas已注册');
    } else {
        console.error('❌ reliableExportCanvas函数未定义');
    }

    if (typeof showExportProgress === 'function') {
        window.showExportProgress = showExportProgress;
    }
    if (typeof hideExportProgress === 'function') {
        window.hideExportProgress = hideExportProgress;
    }
    if (typeof superPreloadImages === 'function') {
        window.superPreloadImages = superPreloadImages;
    }
    if (typeof getCanvasFullDimensions === 'function') {
        window.getCanvasFullDimensions = getCanvasFullDimensions;
    }
    if (typeof expandCanvasForExport === 'function') {
        window.expandCanvasForExport = expandCanvasForExport;
    }
    if (typeof restoreCanvasStyles === 'function') {
        window.restoreCanvasStyles = restoreCanvasStyles;
    }

    console.log('✅ 导出函数注册完成');
    console.log('全局作用域中的导出函数:', {
        exportCanvasAsImage: typeof window.exportCanvasAsImage,
        reliableExportCanvas: typeof window.reliableExportCanvas,
        showExportProgress: typeof window.showExportProgress,
        hideExportProgress: typeof window.hideExportProgress
    });


</script>
{% endblock %}