{% extends "admin/admin_base.html" %}

{% block title %}更新CSV数据源文件{% endblock %}

{% block content %}
    <h2>更新CSV数据源文件: {{ datasource.name }}</h2>
    <p><small>ID: {{ datasource_id }}</small></p>
    <p><small>当前CSV文件: {{ datasource.details.filename if datasource.details and datasource.details.filename else '未知' }}</small></p>

    <form method="POST" action="{{ url_for('admin_update_datasource_csv', datasource_id=datasource_id) }}" enctype="multipart/form-data" style="max-width: 600px;">
        <div class="form-group" style="margin-bottom: 15px;">
            <label for="name" style="display: block; margin-bottom: 5px; font-weight: bold;">数据源名称:</label>
            <input type="text" id="name" name="name" class="form-control" required 
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                   value="{{ datasource.name }}">
            <small style="color: #6c757d;">您可以选择修改数据源的名称。</small>
        </div>

        <div class="form-group" style="margin-bottom: 15px;">
            <label for="csv_file" style="display: block; margin-bottom: 5px; font-weight: bold;">选择新的CSV文件:</label>
            <input type="file" id="csv_file" name="csv_file" class="form-control" accept=".csv" onchange="handleFileSelect(this)">
            <small style="color: #6c757d;">上传新的CSV文件将会替换掉服务器上现有的文件。系统会尝试保留您已有的列配置（索引列、图片列、价格列），但如果新文件缺少这些列，相关配置会被清除，您需要重新编辑指定。</small>
            
            <!-- 上传进度显示 -->
            <div id="upload_progress_container" style="display: none; margin-top: 15px;">
                <div style="margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                        <span id="upload_filename" style="font-weight: bold;"></span>
                        <span id="upload_size" style="color: #666; font-size: 0.9em;"></span>
                    </div>
                    <div style="background-color: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                        <div id="upload_progress_bar" style="background-color: #007bff; height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 10px;"></div>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 5px;">
                        <span id="upload_status" style="color: #666; font-size: 0.9em;">准备上传...</span>
                        <span id="upload_percentage" style="color: #666; font-size: 0.9em;">0%</span>
                    </div>
                </div>
                
                <!-- 上传控制按钮 -->
                <div id="upload_controls" style="text-align: center;">
                    <button type="button" id="start_upload_btn" onclick="startSimpleUpload()"
                            style="background-color: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;">
                        开始上传
                    </button>
                    <button type="button" id="cancel_upload_btn" onclick="cancelUpload()"
                            style="background-color: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        取消
                    </button>
                </div>
            </div>
            
            <!-- 隐藏字段存储上传结果 -->
            <input type="hidden" id="uploaded_filename" name="uploaded_filename" value="">
        </div>

        <div style="margin-top: 20px;">
            <button type="submit" class="btn btn-primary" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">确认更新</button>
            <a href="{{ url_for('admin_datasources') }}" class="btn" style="margin-left: 10px; background-color: #6c757d; color:white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">取消</a>
        </div>
    </form>

    {% if datasource.type == 'local_csv' %}
        <hr style="margin-top: 30px; margin-bottom: 20px;">
        <h4>当前列配置信息 (供参考)</h4>
        <p>如果新上传的CSV文件中缺少以下已配置的列，这些配置将会被自动清除，您需要重新编辑数据源进行设置。</p>
        <ul>
            <li><strong>索引列 (<code>index_column</code>):</strong> {{ datasource.index_column if datasource.index_column else '未配置' }}</li>
            <li><strong>图片列 (<code>image_column_name</code>):</strong> {{ datasource.image_column_name if datasource.image_column_name else '未配置' }}</li>
            <li><strong>产品价格列 (<code>product_price_column</code>):</strong> {{ datasource.product_price_column if datasource.product_price_column else '未配置' }}</li>
        </ul>
    {% endif %}

    <!-- 引入简化上传库 -->
    <script src="{{ url_for('static', filename='js/simple-upload.js') }}"></script>
    
    <script>
        // 全局变量
        let simpleUploader = null;
        let selectedFile = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSimpleUploader();
        });

        // 初始化简化上传器
        function initSimpleUploader() {
            simpleUploader = new SimpleUploader({
                uploadUrl: '/admin/upload_csv',
                maxFileSize: 500 * 1024 * 1024, // 500MB
                allowedTypes: ['csv'],
                onStart: function(file) {
                    document.getElementById('upload_status').textContent = '正在上传...';
                    document.getElementById('upload_status').style.color = '#007bff';
                    document.getElementById('start_upload_btn').style.display = 'none';
                },
                onProgress: function(progress) {
                    updateUploadProgress(progress);
                },
                onSuccess: function(result) {
                    handleUploadSuccess(result);
                },
                onError: function(error) {
                    handleUploadError(error);
                }
            });
        }

        // 处理文件选择
        function handleFileSelect(fileInput) {
            const file = fileInput.files[0];
            if (!file) {
                hideUploadProgress();
                return;
            }

            // 检查文件类型
            if (!file.name.toLowerCase().endsWith('.csv')) {
                alert('只支持CSV文件格式');
                fileInput.value = '';
                return;
            }

            selectedFile = file;
            showUploadProgress(file);
        }

        // 显示上传进度界面
        function showUploadProgress(file) {
            const container = document.getElementById('upload_progress_container');
            const filename = document.getElementById('upload_filename');
            const fileSize = document.getElementById('upload_size');

            filename.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            container.style.display = 'block';

            // 重置进度
            updateUploadProgress({
                progress: 0,
                loaded: 0,
                total: file.size
            });

            // 显示开始上传按钮
            document.getElementById('start_upload_btn').style.display = 'inline-block';

            // 移除表单的required属性，因为我们使用上传
            document.getElementById('csv_file').removeAttribute('required');
        }

        // 隐藏上传进度界面
        function hideUploadProgress() {
            document.getElementById('upload_progress_container').style.display = 'none';
            // 恢复表单的required属性
            document.getElementById('csv_file').setAttribute('required', 'required');
        }

        // 更新上传进度
        function updateUploadProgress(progress) {
            const progressBar = document.getElementById('upload_progress_bar');
            const percentage = document.getElementById('upload_percentage');
            const status = document.getElementById('upload_status');

            // 更新进度条和百分比
            progressBar.style.width = progress.progress + '%';
            percentage.textContent = progress.progress.toFixed(1) + '%';

            // 更新状态信息
            let statusText = '正在上传...';
            if (progress.speed && progress.speed > 0) {
                statusText += ` | 速度: ${formatSpeed(progress.speed)}`;
            }
            if (progress.loaded && progress.total) {
                statusText += ` | ${formatFileSize(progress.loaded)}/${formatFileSize(progress.total)}`;
            }

            status.textContent = statusText;
            status.style.color = '#007bff';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 格式化速度
        function formatSpeed(bytesPerSecond) {
            return formatFileSize(bytesPerSecond) + '/s';
        }

        // 开始简化上传
        async function startSimpleUpload() {
            if (!selectedFile) {
                alert('请先选择文件');
                return;
            }

            if (!simpleUploader) {
                initSimpleUploader();
            }

            try {
                await simpleUploader.upload(selectedFile);
            } catch (error) {
                console.error('上传失败:', error);
                handleUploadError(error.message || error);
            }
        }

        // 取消上传
        async function cancelUpload() {
            if (simpleUploader) {
                simpleUploader.cancel();
            }

            // 重置界面
            hideUploadProgress();
            document.getElementById('csv_file').value = '';
            document.getElementById('uploaded_filename').value = '';
            selectedFile = null;
        }

        // 处理上传成功
        function handleUploadSuccess(result) {
            document.getElementById('uploaded_filename').value = result.filename;
            document.getElementById('upload_status').textContent = '上传完成！' + (result.message || '');
            document.getElementById('upload_status').style.color = '#28a745';

            // 隐藏控制按钮
            document.getElementById('start_upload_btn').style.display = 'none';
        }

        // 处理上传错误
        function handleUploadError(error) {
            document.getElementById('upload_status').textContent = '上传失败: ' + error;
            document.getElementById('upload_status').style.color = '#dc3545';

            // 显示开始上传按钮，允许重试
            document.getElementById('start_upload_btn').style.display = 'inline-block';
        }
    </script>

{% endblock %} 