{% extends "admin/admin_base.html" %}

{% block title %}用户管理{% endblock %}

{% block content %}
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h2>用户管理</h2>
        <a href="{{ url_for('admin_add_user') }}" class="btn btn-success">添加新用户</a> {# 修改链接到添加用户页面 #}
    </div>
    
    {% if users %}
        <table>
            <thead>
                <tr>
                    <th>用户名</th>
                    <th>角色</th>
                    <th>操作</th> {# 占位符，以后可以放编辑/删除等按钮 #}
                </tr>
            </thead>
            <tbody>
                {% for username, data in users.items() %}
                <tr>
                    <td>{{ username }}</td>
                    <td>{{ data.role }}</td>
                    <td>-</td> {# 占位符 #}
                </tr>
                {% endfor %}
            </tbody>
        </table>
    {% else %}
        <p>目前系统中没有用户。</p>
    {% endif %}
{% endblock %} 