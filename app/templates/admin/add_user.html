{% extends "admin/admin_base.html" %}

{% block title %}添加新用户{% endblock %}

{% block content %}
    <h2>添加新用户</h2>

    <form method="POST" action="{{ url_for('admin_add_user') }}" style="max-width: 500px;">
        {# Flask-WTF (如果使用) 会在这里生成 CSRF token #}
        {{ form.hidden_tag() if form else '' }} 

        <div class="form-group" style="margin-bottom: 15px;">
            <label for="username" style="display: block; margin-bottom: 5px; font-weight: bold;">用户名 (账号):</label>
            <input type="text" id="username" name="username" class="form-control" required 
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
        </div>

        <div class="form-group" style="margin-bottom: 15px;">
            <label for="password" style="display: block; margin-bottom: 5px; font-weight: bold;">密码:</label>
            <input type="password" id="password" name="password" class="form-control" required
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
        </div>

        <div class="form-group" style="margin-bottom: 15px;">
            <label for="confirm_password" style="display: block; margin-bottom: 5px; font-weight: bold;">确认密码:</label>
            <input type="password" id="confirm_password" name="confirm_password" class="form-control" required
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
        </div>

        <div class="form-group" style="margin-bottom: 20px;">
            <label for="role" style="display: block; margin-bottom: 5px; font-weight: bold;">用户角色:</label>
            <select id="role" name="role" class="form-control" required 
                    style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
                <option value="user" selected>普通用户</option>
                <option value="admin">管理员</option>
            </select>
        </div>

        <button type="submit" class="btn btn-primary" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">创建用户</button>
        <a href="{{ url_for('admin_users') }}" class="btn" style="margin-left: 10px; background-color: #6c757d; color:white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">取消</a>
    </form>

{% endblock %} 