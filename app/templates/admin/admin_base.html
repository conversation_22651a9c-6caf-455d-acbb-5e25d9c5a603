<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}管理员后台{% endblock %} - productview2</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        body { font-family: sans-serif; margin: 0; background-color: #f8f9fa; color: #212529; }
        .admin-navbar { background-color: #343a40; padding: 10px 20px; color: white; display: flex; justify-content: space-between; align-items: center; }
        .admin-navbar a { color: white; text-decoration: none; margin-right: 15px; }
        .admin-navbar a:hover { text-decoration: underline; }
        .admin-container { margin: 20px; padding: 20px; background-color: #fff; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }
        .admin-content { margin-top: 20px; }
        h1, h2, h3 { color: #343a40; }
        .flash-messages { list-style: none; padding: 0; margin-bottom: 15px; }
        .flash-messages li { padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .flash-success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .flash-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .flash-info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        th, td { border: 1px solid #dee2e6; padding: 8px; text-align: left; }
        th { background-color: #e9ecef; }
        .btn { display: inline-block; font-weight: 400; color: #fff; text-align: center; vertical-align: middle; cursor: pointer; background-color: #007bff; border: 1px solid #007bff; padding: .375rem .75rem; font-size: 1rem; line-height: 1.5; border-radius: .25rem; text-decoration: none; }
        .btn-success { background-color: #28a745; border-color: #28a745; }
        .btn-warning { background-color: #ffc107; border-color: #ffc107; color: #212529; }
        .btn-danger { background-color: #dc3545; border-color: #dc3545; }
        .btn:hover { opacity: 0.9; }
    </style>
</head>
<body>
    <nav class="admin-navbar">
        <div>
            <a href="{{ url_for('admin_dashboard') }}">productview2 管理后台</a>
            <a href="{{ url_for('admin_dashboard') }}">概览</a>
            <a href="{{ url_for('admin_users') }}">用户管理</a>
            <a href="{{ url_for('admin_datasources') }}">数据源管理</a>
        </div>
        <div>
            <span>欢迎, {{ session.get('user_name', '管理员') }}!</span>
            <a href="{{ url_for('logout') }}" style="margin-left: 15px;">退出登录</a>
            <a href="{{ url_for('index') }}" style="margin-left: 15px;">返回用户端</a>
        </div>
    </nav>

    <div class="admin-container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="flash-messages">
                {% for category, message in messages %}
                    <li class="flash-{{ category }}">{{ message }}</li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}

        <div class="admin-content">
            {% block content %}{% endblock %}
        </div>
    </div>

</body>
</html> 