{% extends "admin/admin_base.html" %}

{% block title %}编辑数据源{% endblock %}

{% block content %}
    <h2>编辑数据源</h2>

    <form method="POST" action="{{ url_for('admin_edit_datasource', datasource_id=datasource_id) }}" style="max-width: 600px;">
        <div class="form-group" style="margin-bottom: 15px;">
            <label for="name" style="display: block; margin-bottom: 5px; font-weight: bold;">数据源名称:</label>
            <input type="text" id="name" name="name" class="form-control" required 
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                   value="{{ datasource.name }}">
        </div>

        <div class="form-group" style="margin-bottom: 15px;">
            <label for="type" style="display: block; margin-bottom: 5px; font-weight: bold;">数据源类型:</label>
            <input type="text" id="type_display" class="form-control" disabled
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                   value="{{ '飞书多维表格链接' if datasource.type == 'feishu_lark_sheets_url' else '本地CSV文件' }}">
        </div>

        {% if datasource.type == 'feishu_lark_sheets_url' %}
        <div class="form-group" style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">飞书多维表格分享链接:</label>
            <input type="text" class="form-control" disabled
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                   value="{{ datasource.details.url if datasource.details and 'url' in datasource.details else '未设置' }}">
        </div>
        {% elif datasource.type == 'local_csv' %}
        <div class="form-group" style="margin-bottom: 15px;">
            <label style="display: block; margin-bottom: 5px; font-weight: bold;">CSV文件:</label>
            <input type="text" class="form-control" disabled
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                   value="{{ datasource.details.filename if datasource.details and 'filename' in datasource.details else '未上传' }}">
            <small style="color: #6c757d;">如需更改CSV文件，请删除此数据源并重新创建。</small>
        </div>
        {% endif %}

        <div class="form-group" style="margin-bottom: 15px;">
            <label for="image_column_name" style="display: block; margin-bottom: 5px; font-weight: bold;">图片列名称 (可选):</label>
            {% if datasource.type == 'local_csv' %}
                {% if csv_columns %}
                    <select id="image_column_name" name="image_column_name" class="form-control"
                            style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
                        <option value="">-- 不选择图片列 --</option>
                        {% for column in csv_columns %}
                            <option value="{{ column }}" {% if datasource.image_column_name == column %}selected{% endif %}>{{ column }}</option>
                        {% endfor %}
                    </select>
                {% else %}
                    <input type="text" id="image_column_name" name="image_column_name" class="form-control" 
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                           value="{{ datasource.image_column_name if datasource.image_column_name else '' }}"
                           placeholder="CSV文件无表头或读取失败，请手动输入">
                    <small style="color: #dc3545;">无法读取CSV表头，请手动输入或检查文件。</small>
                {% endif %}
            {% else %} {# For non-CSV types #}
                <input type="text" id="image_column_name" name="image_column_name" class="form-control" 
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                       value="{{ datasource.image_column_name if datasource.image_column_name else '' }}"
                       placeholder="输入包含图片URL或标识符的列名">
            {% endif %}
            <small class="form-text text-muted" style="color: #6c757d;">如果数据中包含图片链接，请选择或指定对应的列名。</small>
        </div>

        {# Product Name Column - Only for CSV #}
        {% if datasource.type == 'local_csv' %}
        <div class="form-group" style="margin-bottom: 15px;">
            <label for="index_column" style="display: block; margin-bottom: 5px; font-weight: bold;">索引列 (可选):</label>
            {% if csv_columns %}
                <select id="index_column" name="index_column" class="form-control"
                        style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
                    <option value="">-- 不选择索引列 --</option>
                    {% for column in csv_columns %}
                        <option value="{{ column }}" {% if datasource.index_column == column %}selected{% endif %}>{{ column }}</option>
                    {% endfor %}
                </select>
            {% else %}
                <input type="text" id="index_column" name="index_column" class="form-control" 
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                       value="{{ datasource.index_column if datasource.index_column else '' }}"
                       placeholder="CSV文件无表头或读取失败，请手动输入">
                <small style="color: #dc3545;">无法读取CSV表头，请手动输入或检查文件。</small>
            {% endif %}
            <small class="form-text text-muted" style="color: #6c757d;">此列将作为产品的唯一标识，用于数据聚合和关联。</small>
        </div>
        {% endif %}

        {# Product Price Column - Only for CSV #}
        {% if datasource.type == 'local_csv' %}
        <div class="form-group" style="margin-bottom: 15px;">
            <label for="product_price_column" style="display: block; margin-bottom: 5px; font-weight: bold;">产品价格列 (可选):</label>
            {% if csv_columns %}
                <select id="product_price_column" name="product_price_column" class="form-control"
                        style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
                    <option value="">-- 不选择产品价格列 --</option>
                    {% for column in csv_columns %}
                        <option value="{{ column }}" {% if datasource.product_price_column == column %}selected{% endif %}>{{ column }}</option>
                    {% endfor %}
                </select>
            {% else %}
                <input type="text" id="product_price_column" name="product_price_column" class="form-control" 
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                       value="{{ datasource.product_price_column if datasource.product_price_column else '' }}"
                       placeholder="CSV文件无表头或读取失败，请手动输入">
                <small style="color: #dc3545;">无法读取CSV表头，请手动输入或检查文件。</small>
            {% endif %}
            <small class="form-text text-muted" style="color: #6c757d;">请选择包含产品价格的列。如果同一产品有多个价格，系统将取其平均值进行展示。</small>
        </div>
        {% endif %}

        {# --- Key Columns Configuration for X/Y Axes (Only for CSV with headers) --- #}
        {% if datasource.type == 'local_csv' and csv_columns %}
            <div style="border: 1px solid #eee; padding: 15px; margin-bottom: 20px; border-radius: 5px; background-color: #f9f9f9;">
                <h5 style="margin-top:0; margin-bottom:15px; border-bottom:1px solid #ddd; padding-bottom:10px;">配置图表可选轴</h5>
                
                <div class="form-group" style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 10px; font-weight: bold;">可选作X轴的列 (可多选):</label>
                    <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background-color: white; border-radius: 4px;">
                        {% for column in csv_columns %}
                            <div class="form-check" style="margin-bottom: 5px;">
                                <input class="form-check-input" type="checkbox" name="available_x_axes" value="{{ column }}" id="x_axis_{{ loop.index }}"
                                       {% if datasource.key_columns_config and column in datasource.key_columns_config.get('available_x_axes', []) %}checked{% endif %}>
                                <label class="form-check-label" for="x_axis_{{ loop.index }}" style="font-weight: normal;">
                                    {{ column }}
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                    <small class="form-text text-muted" style="color: #6c757d;">选择那些适合在图表X轴上展示分类或维度的列。</small>
                </div>

                <div class="form-group" style="margin-bottom: 10px;">
                    <label style="display: block; margin-bottom: 10px; font-weight: bold;">可选作Y轴的列 (可多选):</label>
                    <div style="max-height: 150px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background-color: white; border-radius: 4px;">
                        {% for column in csv_columns %}
                            <div class="form-check" style="margin-bottom: 5px;">
                                <input class="form-check-input" type="checkbox" name="available_y_axes" value="{{ column }}" id="y_axis_{{ loop.index }}"
                                       {% if datasource.key_columns_config and column in datasource.key_columns_config.get('available_y_axes', []) %}checked{% endif %}>
                                <label class="form-check-label" for="y_axis_{{ loop.index }}" style="font-weight: normal;">
                                    {{ column }}
                                </label>
                            </div>
                        {% endfor %}
                    </div>
                    <small class="form-text text-muted" style="color: #6c757d;">选择那些适合在图表Y轴上展示数值或度量的列。</small>
                </div>
            </div>
        {% endif %}
        {# --- End Key Columns Configuration --- #}

        <div class="form-group" style="margin-bottom: 10px;">
            <label for="announcement" style="display: block; margin-bottom: 5px; font-weight: bold;">数据源公告 (可选):</label>
            <textarea id="announcement" name="announcement" class="form-control" rows="3" 
                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">{{ datasource.announcement if datasource.announcement else '' }}</textarea>
        </div>

        <div class="form-group" style="margin-bottom: 20px;">
            <input type="checkbox" id="announcement_enabled" name="announcement_enabled" value="true" 
                   {% if datasource.announcement_enabled %}checked{% endif %}>
            <label for="announcement_enabled" style="font-weight: normal; margin-left: 5px;">启用此公告</label>
        </div>

        <button type="submit" class="btn btn-primary" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">保存更改</button>
        <a href="{{ url_for('admin_datasources') }}" class="btn" style="margin-left: 10px; background-color: #6c757d; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">取消</a>
    </form>
{% endblock %} 