{% extends "admin/admin_base.html" %}

{% block title %}添加新数据源{% endblock %}

{% block content %}
    <h2>添加新数据源</h2>

    <form method="POST" action="{{ url_for('admin_add_datasource') }}" enctype="multipart/form-data" style="max-width: 600px;" id="addDatasourceForm">
        <div class="form-group" style="margin-bottom: 15px;">
            <label for="name" style="display: block; margin-bottom: 5px; font-weight: bold;">数据源名称:</label>
            <input type="text" id="name" name="name" class="form-control" required 
                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                   value="{{ request.form.name if request and request.form else '' }}">
        </div>

        <div class="form-group" style="margin-bottom: 15px;">
            <label for="type" style="display: block; margin-bottom: 5px; font-weight: bold;">数据源类型:</label>
            <select id="type" name="type" class="form-control" required 
                    style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                    onchange="toggleDataSourceDetails(this.value)">
                <option value="" disabled {% if not (request and request.form.type) %}selected{% endif %}>-- 请选择类型 --</option>
                <option value="feishu_lark_sheets_url" {% if request and request.form.type == 'feishu_lark_sheets_url' %}selected{% endif %}>飞书多维表格链接</option>
                <option value="local_csv" {% if request and request.form.type == 'local_csv' %}selected{% endif %}>上传本地CSV文件</option>
            </select>
        </div>

        <!-- 飞书表格配置区域 -->
        <div id="feishu_details" class="form-group" style="margin-bottom: 15px; display: none;">
            <label for="feishu_url" style="display: block; margin-bottom: 5px; font-weight: bold;">飞书多维表格分享链接:</label>
            <div style="display: flex; gap: 10px;">
                <input type="url" id="feishu_url" name="feishu_url" class="form-control" 
                       style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                       value="{{ request.form.feishu_url if request and request.form else '' }}"
                       placeholder="请输入飞书多维表格的分享链接">
                <button type="button" id="test_feishu_btn" onclick="testFeishuConnection()" 
                        style="padding: 10px 15px; background-color: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    测试连接
                </button>
            </div>
            <small style="color: #6c757d;">
                请确保分享链接是公开可访问的。点击"测试连接"可以预览数据结构。
            </small>
        </div>

        <!-- 飞书数据预览区域 -->
        <div id="feishu_preview" style="display: none; margin-bottom: 15px;">
            <h4 style="color: #28a745; margin-bottom: 10px;">📊 数据预览</h4>
            <div id="feishu_preview_content" style="background: #f8f9fa; padding: 15px; border-radius: 4px; border: 1px solid #dee2e6;">
                <!-- 预览内容将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 飞书字段配置区域 -->
        <div id="feishu_column_selectors" style="display: none;">
            <h4 style="color: #1a73e8; margin-bottom: 15px;">🔧 字段配置</h4>
            
            <div class="form-group" style="margin-bottom: 15px;">
                <label for="feishu_image_column" style="display: block; margin-bottom: 5px; font-weight: bold;">图片列名称 (可选):</label>
                <div id="feishu_image_column_container">
                    <input type="text" id="feishu_image_column" name="image_column_name" class="form-control"
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                           placeholder="选择包含图片URL的列">
                </div>
                <small style="color: #6c757d;">如果数据中包含图片链接，请选择对应的列名。</small>
            </div>

            <div class="form-group" style="margin-bottom: 15px;">
                <label for="feishu_index_column" style="display: block; margin-bottom: 5px; font-weight: bold;">索引列 (可选):</label>
                <div id="feishu_index_column_container">
                    <input type="text" id="feishu_index_column" name="index_column" class="form-control"
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                           placeholder="选择作为唯一标识的列">
                </div>
                <small style="color: #6c757d;">此列将作为产品的唯一标识，用于数据聚合和关联。</small>
            </div>

            <div class="form-group" style="margin-bottom: 15px;">
                <label for="feishu_price_column" style="display: block; margin-bottom: 5px; font-weight: bold;">产品价格列 (可选):</label>
                <div id="feishu_price_column_container">
                    <input type="text" id="feishu_price_column" name="product_price_column" class="form-control"
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                           placeholder="选择包含产品价格的列">
                </div>
                <small style="color: #6c757d;">请选择包含产品价格的列。如果同一产品有多个价格，系统将取其平均值。</small>
            </div>

            <div class="form-group" style="margin-bottom: 15px;">
                <label for="available_axes" style="display: block; margin-bottom: 5px; font-weight: bold;">可用坐标轴字段:</label>
                <div id="available_axes_container">
                    <div style="background: #e9ecef; padding: 10px; border-radius: 4px; min-height: 40px;" id="axes_preview">
                        <em style="color: #6c757d;">测试连接后将显示可用字段</em>
                    </div>
                </div>
                <small style="color: #6c757d;">这些字段将可用作X轴和Y轴进行数据分组展示。</small>
            </div>
        </div>

        <!-- CSV文件上传区域 -->
        <div id="csv_details" class="form-group" style="margin-bottom: 15px; display: none;">
            <label for="csv_file" style="display: block; margin-bottom: 5px; font-weight: bold;">选择CSV文件上传:</label>
            <input type="file" id="csv_file" name="csv_file" class="form-control" accept=".csv" onchange="handleFileSelect(this)">
            
            <!-- 上传进度显示 -->
            <div id="upload_progress_container" style="display: none; margin-top: 15px;">
                <div style="margin-bottom: 10px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                        <span id="upload_filename" style="font-weight: bold;"></span>
                        <span id="upload_size" style="color: #666; font-size: 0.9em;"></span>
                    </div>
                    <div style="background-color: #f0f0f0; border-radius: 10px; height: 20px; overflow: hidden;">
                        <div id="upload_progress_bar" style="background-color: #007bff; height: 100%; width: 0%; transition: width 0.3s ease; border-radius: 10px;"></div>
                    </div>
                    <div style="display: flex; justify-content: space-between; margin-top: 5px;">
                        <span id="upload_status" style="color: #666; font-size: 0.9em;">准备上传...</span>
                        <span id="upload_percentage" style="color: #666; font-size: 0.9em;">0%</span>
                    </div>
                </div>
                
                <!-- 上传控制按钮 -->
                <div id="upload_controls" style="text-align: center;">
                    <button type="button" id="start_upload_btn" onclick="startSimpleUpload()"
                            style="background-color: #28a745; color: white; border: none; padding: 8px 16px; border-radius: 4px; margin-right: 10px; cursor: pointer;">
                        开始上传
                    </button>
                    <button type="button" id="cancel_upload_btn" onclick="cancelUpload()"
                            style="background-color: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                        取消
                    </button>
                </div>
            </div>
            
            <!-- 隐藏字段存储上传结果 -->
            <input type="hidden" id="uploaded_filename" name="uploaded_filename" value="">
        </div>

        <div id="csv_column_selectors" style="display: none;">
            <div class="form-group" style="margin-bottom: 15px;">
                 <label for="image_column_name" style="display: block; margin-bottom: 5px; font-weight: bold;">图片列名称 (可选):</label>
                 <div id="image_column_container">
                     <input type="text" id="image_column_name" name="image_column_name" class="form-control" 
                            style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                            value="{{ request.form.image_column_name if request and request.form else '' }}"
                            placeholder="输入包含图片URL或标识符的列名">
                 </div>
                 <small style="color: #6c757d;">如果数据中包含图片链接，请在此处指定对应的列名。</small>
            </div>

            <div class="form-group" style="margin-bottom: 15px;">
                <label for="index_column" style="display: block; margin-bottom: 5px; font-weight: bold;">索引列 (可选):</label>
                <div id="index_column_container">
                    <input type="text" id="index_column" name="index_column" class="form-control"
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                           value="{{ request.form.index_column if request and request.form else '' }}"
                           placeholder="选择作为唯一聚合锚点的列">
                </div>
                <small style="color: #6c757d;">此列将作为产品的唯一标识，用于数据聚合和关联，请确保其值的唯一性。</small>
            </div>

            <div class="form-group" style="margin-bottom: 15px;">
                <label for="product_price_column" style="display: block; margin-bottom: 5px; font-weight: bold;">产品价格列 (可选):</label>
                <div id="product_price_column_container">
                    <input type="text" id="product_price_column" name="product_price_column" class="form-control"
                           style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                           value="{{ request.form.product_price_column if request and request.form else '' }}"
                           placeholder="输入包含产品价格的列名">
                </div>
                <small style="color: #6c757d;">请选择包含产品价格的列。如果同一产品有多个价格，系统将取其平均值进行展示。</small>
            </div>
        </div>

        <div class="form-group" style="margin-bottom: 10px;">
            <label for="announcement" style="display: block; margin-bottom: 5px; font-weight: bold;">数据源公告 (可选):</label>
            <textarea id="announcement" name="announcement" class="form-control" rows="3" 
                      style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                      placeholder="为用户提供关于此数据源的说明信息">{{ request.form.announcement if request and request.form else '' }}</textarea>
        </div>

        <div class="form-group" style="margin-bottom: 20px;">
            <input type="checkbox" id="announcement_enabled" name="announcement_enabled" value="true" 
                   {% if announcement_enabled_val or (request and request.form.announcement_enabled == 'true') %}checked{% endif %}>
            <label for="announcement_enabled" style="font-weight: normal; margin-left: 5px;">启用此公告</label>
        </div>

        <button type="submit" class="btn btn-primary" style="padding: 10px 20px; background-color: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">添加数据源</button>
        <a href="{{ url_for('admin_datasources') }}" class="btn" style="margin-left: 10px; background-color: #6c757d; color:white; padding: 10px 15px; text-decoration: none; border-radius: 4px;">取消</a>
    </form>

    <!-- 引入简化上传库 -->
    <script src="{{ url_for('static', filename='js/simple-upload.js') }}"></script>
    
    <script>
        // 全局变量
        let simpleUploader = null;
        let selectedFile = null;
        let uploadedHeaders = [];

        // 存储飞书数据预览信息
        let feishuPreviewData = null;

        function toggleDataSourceDetails(selectedType) {
            // 隐藏所有详情区域
            document.getElementById('feishu_details').style.display = 'none';
            document.getElementById('feishu_preview').style.display = 'none';
            document.getElementById('feishu_column_selectors').style.display = 'none';
            document.getElementById('csv_details').style.display = 'none';
            document.getElementById('csv_column_selectors').style.display = 'none';

            // 重置必填属性
            document.getElementById('feishu_url').required = false;
            document.getElementById('csv_file').required = false;
            
            // 禁用所有输入
            disableInput('image_column_name');
            disableInput('index_column');
            disableInput('product_price_column');
            disableInput('feishu_image_column');
            disableInput('feishu_index_column');
            disableInput('feishu_price_column');

            if (selectedType === 'feishu_lark_sheets_url') {
                document.getElementById('feishu_details').style.display = 'block';
                document.getElementById('feishu_url').required = true;
                enableInput('feishu_image_column', 'image_column_name');
                enableInput('feishu_index_column', 'index_column');
                enableInput('feishu_price_column', 'product_price_column');

            } else if (selectedType === 'local_csv') {
                document.getElementById('csv_details').style.display = 'block';
                document.getElementById('csv_file').required = true;
                document.getElementById('csv_column_selectors').style.display = 'block';
                enableInput('image_column_name');
                enableInput('index_column');
                enableInput('product_price_column');
            }
        }

        function disableInput(id) {
            const element = document.getElementById(id);
            if (element) {
                element.disabled = true;
                if (element.dataset.originalName) {
                     element.name = '_disabled_' + element.dataset.originalName;
                } else {
                     element.name = '_disabled_' + id;
                }
            }
        }

        function enableInput(id, nameAttr) {
            const element = document.getElementById(id);
            if (element) {
                element.disabled = false;
                element.name = nameAttr || id;
                element.dataset.originalName = nameAttr || id;
            }
        }

        // 测试飞书连接
        async function testFeishuConnection() {
            const urlInput = document.getElementById('feishu_url');
            const testBtn = document.getElementById('test_feishu_btn');
            const previewDiv = document.getElementById('feishu_preview');
            const previewContent = document.getElementById('feishu_preview_content');
            const columnsDiv = document.getElementById('feishu_column_selectors');
            
            const shareUrl = urlInput.value.trim();
            
            if (!shareUrl) {
                alert('请先输入飞书分享链接');
                return;
            }

            // 更新按钮状态
            testBtn.textContent = '连接中...';
            testBtn.disabled = true;
            testBtn.style.backgroundColor = '#6c757d';

            try {
                const response = await fetch('/admin/test_feishu_connection', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ share_url: shareUrl })
                });

                const result = await response.json();

                if (result.success) {
                    feishuPreviewData = result.data;
                    displayFeishuPreview(result.data, result.headers);
                    setupFeishuColumnSelectors(result.headers);
                    previewDiv.style.display = 'block';
                    columnsDiv.style.display = 'block';
                } else {
                    alert('连接失败: ' + result.error);
                    previewDiv.style.display = 'none';
                    columnsDiv.style.display = 'none';
                }

            } catch (error) {
                console.error('测试连接失败:', error);
                alert('连接测试失败: ' + error.message);
                previewDiv.style.display = 'none';
                columnsDiv.style.display = 'none';
            } finally {
                // 恢复按钮状态
                testBtn.textContent = '测试连接';
                testBtn.disabled = false;
                testBtn.style.backgroundColor = '#28a745';
            }
        }

        function displayFeishuPreview(data, headers) {
            const previewContent = document.getElementById('feishu_preview_content');
            
            if (!data || data.length === 0) {
                previewContent.innerHTML = '<p style="color: #dc3545;">未获取到数据</p>';
                return;
            }

            let html = '<div style="margin-bottom: 10px;"><strong>数据预览 (前3行):</strong></div>';
            html += '<table style="width: 100%; border-collapse: collapse; background: white; border-radius: 4px; overflow: hidden;">';
            
            // 表头
            html += '<thead><tr style="background: #f8f9fa;">';
            headers.forEach(header => {
                html += `<th style="padding: 8px; border: 1px solid #dee2e6; text-align: left; font-weight: bold;">${header}</th>`;
            });
            html += '</tr></thead>';

            // 数据行
            html += '<tbody>';
            const displayData = data.slice(0, 3); // 只显示前3行
            displayData.forEach(row => {
                html += '<tr>';
                headers.forEach(header => {
                    const value = row[header] || '';
                    const displayValue = value.toString().length > 20 ? 
                        value.toString().substring(0, 20) + '...' : value;
                    html += `<td style="padding: 8px; border: 1px solid #dee2e6;">${displayValue}</td>`;
                });
                html += '</tr>';
            });
            html += '</tbody></table>';

            html += `<div style="margin-top: 10px; color: #6c757d; font-size: 0.9em;">
                        <strong>总计:</strong> ${data.length} 行数据，${headers.length} 个字段
                     </div>`;

            previewContent.innerHTML = html;
        }

        function setupFeishuColumnSelectors(headers) {
            if (!headers || headers.length === 0) return;

            // 设置图片列选择器
            populateSelectWithOptions('feishu_image_column_container', 'feishu_image_column', headers, '-- 不选择图片列 --', 'image_column_name');
            
            // 设置索引列选择器  
            populateSelectWithOptions('feishu_index_column_container', 'feishu_index_column', headers, '-- 不选择索引列 --', 'index_column');
            
            // 设置价格列选择器
            populateSelectWithOptions('feishu_price_column_container', 'feishu_price_column', headers, '-- 不选择价格列 --', 'product_price_column');

            // 显示可用坐标轴字段
            const axesPreview = document.getElementById('axes_preview');
            const axesHtml = headers.map(header => 
                `<span style="display: inline-block; background: #e7f3ff; color: #1a73e8; padding: 4px 8px; margin: 2px; border-radius: 3px; font-size: 0.9em;">${header}</span>`
            ).join('');
            axesPreview.innerHTML = axesHtml;
        }

        // 初始化简化上传器
        function initSimpleUploader() {
            console.log('initSimpleUploader 被调用');
            simpleUploader = new SimpleUploader({
                uploadUrl: '/admin/upload_csv',
                maxFileSize: 500 * 1024 * 1024, // 500MB
                allowedTypes: ['csv'],
                onStart: function(file) {
                    console.log('上传开始:', file.name);
                    document.getElementById('upload_status').textContent = '正在上传...';
                    document.getElementById('upload_status').style.color = '#007bff';
                    document.getElementById('start_upload_btn').style.display = 'none';
                },
                onProgress: function(progress) {
                    console.log('上传进度:', progress.progress + '%');
                    updateUploadProgress(progress);
                },
                onSuccess: function(result) {
                    console.log('上传成功:', result);
                    handleUploadSuccess(result);
                },
                onError: function(error) {
                    console.log('上传失败:', error);
                    handleUploadError(error);
                }
            });
            console.log('SimpleUploader 初始化完成');
        }

        // 处理文件选择
        function handleFileSelect(fileInput) {
            console.log('handleFileSelect 被调用');
            const file = fileInput.files[0];
            if (!file) {
                console.log('没有选择文件');
                hideUploadProgress();
                resetColumnSelectorsToText();
                return;
            }

            console.log('选择的文件:', file.name, '大小:', file.size);

            // 检查文件类型
            if (!file.name.toLowerCase().endsWith('.csv')) {
                alert('只支持CSV文件格式');
                fileInput.value = '';
                return;
            }

            selectedFile = file;
            console.log('文件选择成功，显示上传进度界面');
            showUploadProgress(file);
            resetColumnSelectorsToText();
        }

        // 显示上传进度界面
        function showUploadProgress(file) {
            const container = document.getElementById('upload_progress_container');
            const filename = document.getElementById('upload_filename');
            const fileSize = document.getElementById('upload_size');
            
            filename.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            container.style.display = 'block';
            
            // 重置进度
            updateUploadProgress({
                progress: 0,
                uploadedChunks: 0,
                totalChunks: 0,
                uploadedSize: 0,
                totalSize: file.size
            });
            
            // 显示开始上传按钮
            document.getElementById('start_upload_btn').style.display = 'inline-block';
        }

        // 隐藏上传进度界面
        function hideUploadProgress() {
            document.getElementById('upload_progress_container').style.display = 'none';
        }

        // 更新上传进度
        function updateUploadProgress(progress) {
            const progressBar = document.getElementById('upload_progress_bar');
            const percentage = document.getElementById('upload_percentage');
            const status = document.getElementById('upload_status');

            // 更新进度条和百分比
            progressBar.style.width = progress.progress + '%';
            percentage.textContent = progress.progress.toFixed(1) + '%';

            // 更新状态信息
            let statusText = '正在上传...';
            if (progress.speed && progress.speed > 0) {
                statusText += ` | 速度: ${formatSpeed(progress.speed)}`;
            }
            if (progress.loaded && progress.total) {
                statusText += ` | ${formatFileSize(progress.loaded)}/${formatFileSize(progress.total)}`;
            }

            status.textContent = statusText;
            status.style.color = '#007bff';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 格式化速度
        function formatSpeed(bytesPerSecond) {
            return formatFileSize(bytesPerSecond) + '/s';
        }

        // 开始简化上传
        async function startSimpleUpload() {
            console.log('startSimpleUpload 被调用');
            if (!selectedFile) {
                alert('请先选择文件');
                return;
            }

            console.log('开始上传文件:', selectedFile.name);

            if (!simpleUploader) {
                console.log('初始化简化上传器');
                initSimpleUploader();
            }

            try {
                console.log('调用 simpleUploader.upload()');
                await simpleUploader.upload(selectedFile);
                console.log('simpleUploader.upload() 完成');
            } catch (error) {
                console.error('上传失败:', error);
                handleUploadError(error.message || error);
            }
        }

        // 取消上传
        async function cancelUpload() {
            if (simpleUploader) {
                simpleUploader.cancel();
            }

            // 重置界面
            hideUploadProgress();
            document.getElementById('csv_file').value = '';
            document.getElementById('uploaded_filename').value = '';
            selectedFile = null;
            uploadedHeaders = [];
            resetColumnSelectorsToText();
        }

        // 处理上传成功
        function handleUploadSuccess(result) {
            console.log('handleUploadSuccess 被调用，结果:', result);
            
            document.getElementById('uploaded_filename').value = result.filename;
            uploadedHeaders = result.headers || [];
            
            console.log('设置文件名:', result.filename);
            console.log('headers数量:', uploadedHeaders.length);
            
            // 更新列选择器
            if (uploadedHeaders.length > 0) {
                // 如果有列头信息，创建下拉选择框
                console.log('使用获取到的headers创建下拉框');
                populateSelectWithOptions('image_column_container', 'image_column_name', uploadedHeaders, '-- 不选择图片列 --');
                populateSelectWithOptions('index_column_container', 'index_column', uploadedHeaders, '-- 不选择索引列 --');
                populateSelectWithOptions('product_price_column_container', 'product_price_column', uploadedHeaders, '-- 不选择产品价格列 --');
                console.log('下拉框创建完成');
            } else {
                // 如果没有列头信息，尝试备用方案
                if (result.filename) {
                    console.log('没有headers，尝试备用方案获取CSV headers');
                    fetchCSVHeaders(result.filename).then(() => {
                        // 获取成功后再次检查
                        if (uploadedHeaders.length > 0) {
                            console.log('异步获取headers成功，上传完全成功');
                            document.getElementById('upload_status').textContent = '文件上传完成！';
                        }
                    });
                } else {
                    console.log('没有headers也没有文件名');
                }
            }
            
            document.getElementById('upload_status').textContent = '上传完成！' + (result.message || '');
            document.getElementById('upload_status').style.color = '#28a745';
            
            // 隐藏控制按钮
            document.getElementById('start_upload_btn').style.display = 'none';
            
            console.log('handleUploadSuccess 处理完成');
        }

        // 处理上传错误
        function handleUploadError(error) {
            console.log('handleUploadError 被调用，错误信息:', error);
            document.getElementById('upload_status').textContent = '上传失败: ' + error;
            document.getElementById('upload_status').style.color = '#dc3545';

            // 显示开始上传按钮，允许重试
            document.getElementById('start_upload_btn').style.display = 'inline-block';
        }

        
        function populateSelectWithOptions(containerId, selectIdAndName, headers, defaultOptionText, actualName) {
            const container = document.getElementById(containerId);
            if (!container) {
                console.error(`错误：找不到容器元素 ${containerId}`);
                return;
            }
            
            const realName = actualName || selectIdAndName;
            let selectHtml = `
                <select id="${selectIdAndName}" name="${realName}" class="form-control"
                        style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
                    <option value="">${defaultOptionText}</option>
            `;
            headers.forEach(header => {
                if (header) {
                    selectHtml += `<option value="${header}">${header}</option>`;
                }
            });
            selectHtml += `</select>`;
            container.innerHTML = selectHtml;
            enableInput(selectIdAndName, realName);
        }

        // 格式化文件大小的辅助函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 异步获取CSV列头信息的备用方案
        async function fetchCSVHeaders(filename) {
            console.log('fetchCSVHeaders 开始，文件名:', filename);
            try {
                const response = await fetch('/admin/get_csv_headers', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ filename: filename })
                });
                
                console.log('fetch 响应状态:', response.status);
                
                if (response.ok) {
                    const result = await response.json();
                    console.log('fetch 响应结果:', result);
                    
                    if (result.success && result.headers && result.headers.length > 0) {
                        console.log('异步获取到列头信息:', result.headers.length, '个列');
                        uploadedHeaders = result.headers;
                        
                        // 更新列选择器
                        populateSelectWithOptions('image_column_container', 'image_column_name', uploadedHeaders, '-- 不选择图片列 --');
                        populateSelectWithOptions('index_column_container', 'index_column', uploadedHeaders, '-- 不选择索引列 --');
                        populateSelectWithOptions('product_price_column_container', 'product_price_column', uploadedHeaders, '-- 不选择产品价格列 --');
                        
                        console.log('备用方案成功：列选择器已更新为下拉框');
                        
                        // 更新状态显示
                        document.getElementById('upload_status').textContent = '文件上传完成，列信息已获取！';
                        document.getElementById('upload_status').style.color = '#28a745';
                        
                        return { success: true, headers: result.headers }; // 返回成功结果
                    } else {
                        console.log('获取headers失败或没有headers:', result);
                        return { success: false, error: '获取列信息失败' };
                    }
                } else {
                    console.log('HTTP请求失败:', response.status, response.statusText);
                    return { success: false, error: `HTTP ${response.status}` };
                }
            } catch (error) {
                console.error('异步获取CSV列头失败:', error);
                return { success: false, error: error.message };
            }
        }

        function resetColumnSelectorsToText() {
            document.getElementById('image_column_container').innerHTML = `
                <input type="text" id="image_column_name" name="image_column_name" class="form-control" 
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                       value="" placeholder="输入包含图片URL或标识符的列名">`;
            document.getElementById('index_column_container').innerHTML = `
                <input type="text" id="index_column" name="index_column" class="form-control"
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                       value="" placeholder="选择作为唯一聚合锚点的列">`;
            document.getElementById('product_price_column_container').innerHTML = `
                <input type="text" id="product_price_column" name="product_price_column" class="form-control"
                       style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;"
                       value="" placeholder="输入包含产品价格的列名">`;
            enableInput('image_column_name');
            enableInput('index_column');
            enableInput('product_price_column');
        }

        // 页面加载时根据现有选择初始化
        document.addEventListener('DOMContentLoaded', function() {
            var initialType = document.getElementById('type').value;
            toggleDataSourceDetails(initialType);
            
            // 初始化简化上传器
            initSimpleUploader();
        });
    </script>

{% endblock %} 