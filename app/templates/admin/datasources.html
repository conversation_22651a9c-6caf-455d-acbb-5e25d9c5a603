{% extends "admin/admin_base.html" %}

{% block title %}数据源管理{% endblock %}

{% block content %}
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <h2>数据源管理</h2>
        <a href="{{ url_for('admin_add_datasource') }}" class="btn btn-success">添加新数据源</a>
    </div>
    
    {% if datasources and datasources is mapping and datasources|length > 0 %}
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>类型</th>
                    <th>详情</th>
                    <th>图片列</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                {% for ds_id, ds_data in datasources.items() %}
                <tr>
                    <td style="max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;" title="{{ ds_id }}">{{ ds_id[:8] }}...</td>
                    <td><strong>{{ ds_data.name }}</strong></td>
                    <td>
                        {% if ds_data.type == 'local_csv' %}
                            <span style="background: #e3f2fd; color: #1976d2; padding: 2px 8px; border-radius: 12px; font-size: 0.85em;">📄 CSV文件</span>
                        {% elif ds_data.type == 'feishu_lark_sheets_url' %}
                            <span style="background: #f3e5f5; color: #7b1fa2; padding: 2px 8px; border-radius: 12px; font-size: 0.85em;">🚀 飞书表格</span>
                        {% else %}
                            <span style="background: #f5f5f5; color: #757575; padding: 2px 8px; border-radius: 12px; font-size: 0.85em;">{{ ds_data.type }}</span>
                        {% endif %}
                    </td>
                    <td style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                        {% if ds_data.type == 'local_csv' %}
                            <small style="color: #666;">{{ ds_data.details.filename or '文件信息缺失' }}</small>
                        {% elif ds_data.type == 'feishu_lark_sheets_url' %}
                            <small style="color: #666;">{{ ds_data.details.url[:50] + '...' if ds_data.details.url and ds_data.details.url|length > 50 else ds_data.details.url or '链接信息缺失' }}</small>
                        {% endif %}
                    </td>
                    <td>{{ ds_data.get('image_column_name', '-') }}</td>
                    <td>{{ ds_data.created_at.split('T')[0] if ds_data.created_at else '-' }}</td>
                    <td>
                        <a href="{{ url_for('admin_edit_datasource', datasource_id=ds_id) }}" class="btn btn-sm btn-warning">编辑</a>
                        {% if ds_data.type == 'local_csv' %}
                        <a href="{{ url_for('admin_update_datasource_csv', datasource_id=ds_id) }}" class="btn btn-sm btn-info" style="margin-left: 5px;">更新文件</a>
                        {% endif %}
                        <a href="{{ url_for('admin_delete_datasource', datasource_id=ds_id) }}" 
                           class="btn btn-sm btn-danger" style="margin-left: 5px;" 
                           data-name="{{ ds_data.name|escape }}"
                           data-type="{{ ds_data.type }}"
                           onclick="return confirmDelete(this)">删除</a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #17a2b8;">
            <h4 style="color: #17a2b8; margin-bottom: 10px;">💡 使用说明</h4>
            <ul style="margin: 0; padding-left: 20px; color: #495057;">
                <li><strong>CSV文件</strong>：上传本地CSV文件作为数据源，支持数据聚合和价格计算</li>
                <li><strong>飞书表格</strong>：通过分享链接连接飞书多维表格，实时获取数据</li>
                <li>点击 <span style="background: #ffc107; color: #212529; padding: 1px 4px; border-radius: 3px;">编辑</span> 可以修改数据源的配置信息</li>
                <li>CSV类型支持 <span style="background: #17a2b8; color: white; padding: 1px 4px; border-radius: 3px;">更新文件</span> 功能</li>
            </ul>
        </div>
    {% else %}
        <div style="text-align: center; padding: 60px 20px; background: #f8f9fa; border-radius: 8px; margin-top: 20px;">
            <div style="font-size: 3em; margin-bottom: 20px; color: #dee2e6;">📊</div>
            <h3 style="color: #6c757d; margin-bottom: 15px;">还没有配置任何数据源</h3>
            <p style="color: #6c757d; margin-bottom: 25px;">
                开始添加数据源来为用户提供产品数据分析功能
            </p>
            <a href="{{ url_for('admin_add_datasource') }}" class="btn btn-primary" style="padding: 12px 24px; font-size: 1.1em;">
                🚀 添加第一个数据源
            </a>
        </div>
    {% endif %}

    <script>
        function confirmDelete(element) {
            const name = element.getAttribute('data-name');
            const type = element.getAttribute('data-type');
            
            let message = `确定要删除数据源 '${name}' 吗？\n\n`;
            
            if (type === 'local_csv') {
                message += '此操作不可恢复，关联的CSV文件也会被删除。';
            } else if (type === 'feishu_lark_sheets_url') {
                message += '此操作不可恢复，但不会影响原始的飞书表格。';
            }
            
            return confirm(message);
        }
    </script>
{% endblock %} 