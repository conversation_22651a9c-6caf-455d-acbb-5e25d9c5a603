<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>登录 - productview2</title>
    <style>
        body { font-family: sans-serif; margin: 20px; background-color: #f4f4f4; color: #333; }
        .container { background-color: #fff; padding: 20px; border-radius: 8px; box-shadow: 0 0 10px rgba(0,0,0,0.1); max-width: 400px; margin: 40px auto; }
        h2 { text-align: center; color: #333; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="text"], input[type="password"] { width: calc(100% - 22px); padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .button-group { text-align: center; margin-top: 20px; }
        button { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin: 0 5px; }
        .user-login-btn { background-color: #007bff; color: white; }
        .admin-login-btn { background-color: #28a745; color: white; }
        .flash-messages { list-style: none; padding: 0; margin-bottom: 15px; }
        .flash-messages li { padding: 10px; border-radius: 4px; margin-bottom: 10px; }
        .flash-error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <h2>登录 productview2</h2>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="flash-messages">
                {% for category, message in messages %}
                    <li class="flash-{{ category }}">{{ message }}</li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('login') }}">
            <div class="form-group">
                <label for="username">账号:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <div class="button-group">
                <button type="submit" name="login_type" value="user" class="user-login-btn">普通用户登录</button>
                <button type="submit" name="login_type" value="admin" class="admin-login-btn">管理员登录</button>
            </div>
        </form>
        <p style="text-align: center; margin-top: 20px;">
            <a href="{{ url_for('index') }}">返回首页</a>
        </p>
    </div>
</body>
</html> 