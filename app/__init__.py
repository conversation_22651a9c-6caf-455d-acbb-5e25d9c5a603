from flask import Flask
import time
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your_very_secret_and_hard_to_guess_key'

# 优化大文件上传配置
app.config['MAX_CONTENT_LENGTH'] = 2 * 1024 * 1024 * 1024  # 2GB最大文件大小
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用静态文件缓存
app.config['UPLOAD_EXTENSIONS'] = ['.csv']  # 允许的文件类型

# 🔥 强化缓存禁用配置，确保部署更新立即生效
app.jinja_env.auto_reload = True
app.config['TEMPLATES_AUTO_RELOAD'] = True

# 🚫 强制禁用所有缓存（确保更新能立即生效）
@app.after_request
def add_no_cache_headers(response):
    """强制禁用浏览器和代理缓存"""
    # 对所有响应禁用缓存
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'
    response.headers['Last-Modified'] = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())

    return response

from app import routes