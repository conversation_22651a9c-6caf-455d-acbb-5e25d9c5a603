// Y轴静态定位验证脚本
// 在浏览器控制台中运行此脚本来验证Y轴粘性定位已被移除

console.log('🔍 开始验证Y轴静态定位...');

function verifyYAxisStaticPositioning() {
    console.log('\n📌 验证Y轴静态定位');
    
    // 查找所有Y轴标签
    const yMainHeaders = document.querySelectorAll('tbody th:first-child');
    const ySubHeaders = document.querySelectorAll('tbody th:nth-child(2)');
    const allYHeaders = [...yMainHeaders, ...ySubHeaders];
    
    console.log(`找到 ${yMainHeaders.length} 个主分类标签`);
    console.log(`找到 ${ySubHeaders.length} 个子分类标签`);
    console.log(`总计 ${allYHeaders.length} 个Y轴标签`);
    
    if (allYHeaders.length === 0) {
        console.log('❌ 未找到Y轴标签');
        return false;
    }
    
    let staticCount = 0;
    let issues = [];
    
    allYHeaders.forEach((header, index) => {
        const styles = window.getComputedStyle(header);
        const position = styles.position;
        const left = styles.left;
        const top = styles.top;
        const zIndex = styles.zIndex;
        
        console.log(`Y轴标签 ${index + 1}:`, {
            element: header.textContent.trim().substring(0, 10) + '...',
            position: position,
            left: left,
            top: top,
            zIndex: zIndex,
            cellIndex: header.cellIndex
        });
        
        // 检查是否为静态定位
        if (position === 'static') {
            staticCount++;
        } else {
            issues.push(`标签 ${index + 1}: position=${position} (应为static)`);
        }
        
        // 检查left值应该为auto
        if (left !== 'auto' && left !== '0px') {
            issues.push(`标签 ${index + 1}: left=${left} (应为auto)`);
        }
        
        // 检查top值应该为auto
        if (top !== 'auto' && top !== '0px') {
            issues.push(`标签 ${index + 1}: top=${top} (应为auto)`);
        }
        
        // 检查z-index应该为auto
        if (zIndex !== 'auto' && zIndex !== '0') {
            issues.push(`标签 ${index + 1}: z-index=${zIndex} (应为auto)`);
        }
    });
    
    console.log(`\n📊 验证结果:`);
    console.log(`静态定位标签: ${staticCount}/${allYHeaders.length}`);
    
    if (issues.length === 0) {
        console.log('✅ Y轴静态定位验证通过');
        console.log('✅ 所有Y轴标签都使用静态定位');
        return true;
    } else {
        console.log('❌ Y轴静态定位验证失败:');
        issues.forEach(issue => console.log(`  - ${issue}`));
        return false;
    }
}

function verifyYAxisFunctionality() {
    console.log('\n🔧 验证Y轴功能');
    
    // 检查初始化函数
    if (typeof window.initializeYAxis === 'function') {
        console.log('✅ initializeYAxis 函数存在');
        
        try {
            // 测试调用初始化函数
            window.initializeYAxis();
            console.log('✅ initializeYAxis 函数调用成功');
        } catch (error) {
            console.log(`❌ initializeYAxis 函数调用失败: ${error.message}`);
        }
    } else {
        console.log('❌ initializeYAxis 函数不存在');
    }
    
    // 检查滚动监听函数
    if (typeof window.setupYAxisScrollListener === 'function') {
        console.log('✅ setupYAxisScrollListener 函数存在（已禁用）');
    } else {
        console.log('❌ setupYAxisScrollListener 函数不存在');
    }
}

function verifyYAxisStyles() {
    console.log('\n🎨 验证Y轴样式');
    
    const yHeaders = document.querySelectorAll('tbody th:first-child, tbody th:nth-child(2)');
    let styleIssues = [];
    
    yHeaders.forEach((header, index) => {
        const styles = window.getComputedStyle(header);
        
        // 检查文字方向
        if (styles.writingMode !== 'vertical-rl') {
            styleIssues.push(`标签 ${index + 1}: writing-mode=${styles.writingMode} (应为vertical-rl)`);
        }
        
        // 检查文字朝向
        if (styles.textOrientation !== 'upright') {
            styleIssues.push(`标签 ${index + 1}: text-orientation=${styles.textOrientation} (应为upright)`);
        }
        
        // 检查宽度
        const width = parseInt(styles.width);
        if (width !== 120) {
            styleIssues.push(`标签 ${index + 1}: width=${styles.width} (应为120px)`);
        }
    });
    
    if (styleIssues.length === 0) {
        console.log('✅ Y轴样式验证通过');
        console.log('✅ 文字方向、朝向和尺寸都正确');
        return true;
    } else {
        console.log('❌ Y轴样式验证失败:');
        styleIssues.forEach(issue => console.log(`  - ${issue}`));
        return false;
    }
}

function verifyXAxisStickyPreserved() {
    console.log('\n📋 验证X轴粘性定位保留');
    
    // 检查表头粘性定位
    const thead = document.querySelector('.product-table thead');
    if (thead) {
        const styles = window.getComputedStyle(thead);
        if (styles.position === 'sticky') {
            console.log('✅ X轴表头粘性定位已保留');
        } else {
            console.log(`❌ X轴表头粘性定位丢失: position=${styles.position}`);
        }
    }
    
    // 检查左上角单元格粘性定位
    const cornerCell = document.querySelector('.merged-corner-cell');
    if (cornerCell) {
        const styles = window.getComputedStyle(cornerCell);
        if (styles.position === 'sticky') {
            console.log('✅ 左上角单元格粘性定位已保留');
        } else {
            console.log(`❌ 左上角单元格粘性定位丢失: position=${styles.position}`);
        }
    }
}

// 运行所有验证
function runAllVerifications() {
    console.log('🧪 开始运行Y轴静态定位验证...');
    console.log('=' * 50);
    
    const verifications = [
        { name: 'Y轴静态定位', func: verifyYAxisStaticPositioning },
        { name: 'Y轴功能', func: verifyYAxisFunctionality },
        { name: 'Y轴样式', func: verifyYAxisStyles },
        { name: 'X轴粘性定位保留', func: verifyXAxisStickyPreserved }
    ];
    
    let passedVerifications = 0;
    
    verifications.forEach(verification => {
        try {
            const result = verification.func();
            if (result !== false) {
                passedVerifications++;
            }
        } catch (error) {
            console.log(`❌ ${verification.name} 验证出错: ${error.message}`);
        }
    });
    
    console.log('\n' + '=' * 50);
    console.log('📊 验证结果总结');
    console.log('=' * 50);
    console.log(`通过验证: ${passedVerifications}/${verifications.length}`);
    console.log(`成功率: ${(passedVerifications/verifications.length*100).toFixed(1)}%`);
    
    if (passedVerifications === verifications.length) {
        console.log('\n🎉 所有验证通过！');
        console.log('✅ Y轴粘性定位已成功移除');
        console.log('✅ Y轴现在使用静态定位');
        console.log('✅ X轴粘性定位已保留');
    } else {
        console.log(`\n⚠️ 还有 ${verifications.length - passedVerifications} 项验证未通过`);
    }
    
    console.log('=' * 50);
}

// 自动运行验证
runAllVerifications();
