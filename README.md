# ProductView2 - 智能产品数据分析平台

## 🚀 项目概述

ProductView2 是一个智能的产品数据分析和可视化平台，专为珠宝、奢侈品等零售行业设计。支持CSV文件上传和飞书多维表格集成，提供强大的数据筛选、分组展示和智能价格处理功能。

## ✨ 核心功能

### 📊 数据可视化
- **二维分布视图**：按X/Y轴字段将产品数据分布在网格表格中
- **智能卡片展示**：每个产品以图片卡片形式展示，包含价格、图片等关键信息
- **动态筛选**：支持价格范围、分类条件等多维度筛选
- **实时聚合**：自动按索引列聚合相同产品，计算平均价格
- **画布图片导出**：一键导出完整产品画布为高质量图片，自动展开所有内容

### 📁 数据源管理
- **CSV文件上传**：支持大文件分片上传，自动解析表头和数据类型
- **飞书表格集成**：直接连接飞书多维表格，实时同步数据
- **智能字段识别**：自动识别图片列、价格列、索引列等关键字段

### 🏷️ 产品标签系统
- **多模式显示**：支持角标和底部标签两种显示模式
- **智能内容**：支持字段值、排名、自定义文本等多种标签内容
- **动态颜色**：根据条件自动设置标签颜色

### 👥 用户权限管理
- **角色分离**：管理员和普通用户权限分离
- **习惯保存**：用户可保存常用的筛选和展示配置
- **安全认证**：基于会话的用户认证系统

## 🛠️ 技术架构

### 后端技术
- **Flask 2.3.3**：轻量级Web框架
- **Python 3.8+**：核心编程语言
- **JSON文件存储**：用户、数据源配置存储
- **智能文件处理**：CSV解析、分片上传、文件管理

### 前端技术
- **Jinja2模板**：服务端渲染
- **原生CSS + JavaScript**：无依赖的轻量级前端
- **响应式设计**：适配桌面和移动设备
- **AJAX异步交互**：流畅的用户体验

### 数据处理
- **智能价格处理**：支持多种价格格式转换和清洗
- **实时数据聚合**：按产品索引自动分组统计
- **容错机制**：自动处理数据格式问题和异常情况

## 📋 功能使用指南



## 🔧 核心算法

### 智能价格处理引擎

系统具备强大的价格识别和转换能力：

```python
支持格式示例：
- 纯数字：58, 58.5, 1234.56
- 带符号：￥58, ¥58.5, $58, 58元
- 千分位：1,234.56, ￥1,234.56  
- 混合格式：58.99￥, ￥ 1,234.56 元
```

**处理策略**：
1. 智能清理价格符号和格式字符
2. 验证数字有效性，拒绝负数和无效值
3. 容错处理，跳过问题数据继续处理

### 产品数据匹配系统

**关键修复 (2025-01-15)**：解决了产品卡片与数据不匹配的问题

**问题原因**：
- 模板中产品卡片按分组数据渲染顺序排列
- JavaScript按DOM顺序匹配原始数据数组
- 两者顺序不一致导致图片、标签显示错乱

**解决方案**：
1. **在模板中添加产品索引**：每个卡片添加`data-product-index`属性
2. **精确匹配算法**：JavaScript优先使用索引列精确匹配产品数据
3. **多重备用机制**：价格匹配作为备用方案，确保匹配准确性

```javascript
// 修复前：不可靠的匹配
productData = window.product_data[cardIndex]; // 错误！

// 修复后：精确匹配  
productData = window.product_data.find(item => {
    return String(item[indexColumnName]) === String(productIndex);
});
```

### 界面布局优化系统

**重要更新 (2025-06-13)**：最大化产品画布显示空间

**优化内容**：
1. **移除冗余标题文字**：删除"当前数据源"、"产品分布视图"等标题，释放垂直空间
2. **导航栏整合**：将X/Y轴选择器移至顶部导航栏，与数据源选择器并排显示
3. **响应式布局**：优化小屏幕下的显示，自动调整布局结构
4. **冻结标题功能**：表格标题行和Y轴标题列在滚动时保持固定，提升大数据集浏览体验

**布局变更**：
- **移除内容**：产品画布上方的所有描述性文字
- **新增位置**：X/Y轴选择器现在位于顶部导航栏的数据源选择器右侧
- **空间优化**：产品展示区域获得更多垂直空间，提升可视化效果
- **冻结标题**：表格标题始终可见，方便在大量数据中导航

**技术实现**：
```html
<!-- 优化前：分离的轴选择器 -->
<h3>当前数据源: ...</h3>
<form class="axis-selection-form">...</form>

<!-- 优化后：集成的导航栏布局 -->
<div class="datasource-axis-container">
    <form>数据源选择</form>
    <form>X/Y轴选择</form>
</div>
```

**冻结标题CSS关键代码**：
```css
.product-canvas {
    max-height: calc(100vh - 120px);
    overflow: auto;
}

.product-table thead th {
    position: sticky;
    top: 0;
    z-index: 10;
}

.product-table tbody th {
    position: sticky;
    left: 0;
    z-index: 9;
}
```

## 📈 部署指南

### 本地开发环境

```bash
# 1. 克隆项目
git clone <repository-url>
cd productview2

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动应用
python run.py

# 4. 访问应用
http://localhost:5001
```

### 生产环境部署

#### 方式1：宝塔面板部署（推荐）

1. **上传代码**
   ```bash
   # 压缩项目文件
   zip -r productview2.zip . -x "*.git*" "__pycache__/*" "*.pyc"
   
   # 上传到服务器并解压
   ```

2. **配置Python环境**
   ```bash
   # 在宝塔面板Python项目管理中：
   # - 创建项目：productview2
   # - 入口文件：run.py
   # - 端口：5001
   ```

3. **安装依赖**
   ```bash
   cd /www/wwwroot/productview2
   pip install -r requirements.txt
   ```

4. **启动应用**
   - 在宝塔面板中点击"启动"
   - 或手动运行：`python run.py`

#### 方式2：传统Linux部署

```bash
# 1. 上传代码到服务器
scp -r productview2/ user@server:/opt/

# 2. 安装依赖
cd /opt/productview2
pip install -r requirements.txt

# 3. 后台运行
nohup python run.py > app.log 2>&1 &

# 4. 配置反向代理（Nginx）
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:5001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 部署验证

部署完成后，验证以下功能：

- [ ] 用户登录功能正常
- [ ] 管理员可以访问后台
- [ ] CSV文件上传功能正常
- [ ] 产品卡片图片显示正确
- [ ] 产品标签显示正确的产品信息
- [ ] 飞书表格连接正常（如适用）
- [ ] 数据筛选和可视化正常

## 🐛 故障排除

### 常见问题

**1. 产品图片显示错误**
- **现象**：A产品显示B产品的图片
- **原因**：产品数据匹配错误
- **解决**：确保使用最新版本（已修复此问题）

**2. 标签显示错误产品信息**
- **现象**：产品标签显示其他产品的数据
- **原因**：与图片问题相同，数据匹配错误
- **解决**：确保使用最新版本（已修复此问题）

**3. CSV上传失败**
- **检查**：文件格式是否为UTF-8编码
- **检查**：文件大小是否超出限制
- **检查**：服务器磁盘空间是否足够

**4. 飞书表格连接失败**
- **检查**：链接是否为公开分享链接
- **检查**：网络连接是否正常
- **检查**：API配置是否正确

### 日志检查

```bash
# 查看应用日志
tail -f app.log

# 查看Python错误
python -c "import app; print('导入成功')"

# 检查端口占用
lsof -i :5001
```

## 📋 更新日志

### v2.1.0 (2025-01-15)
- **🐛 重大修复**：解决产品卡片数据匹配错误问题
- **✨ 优化**：改进产品索引匹配算法，确保图片和标签显示正确
- **🧹 清理**：移除调试信息和版本号显示，界面更简洁
- **📚 文档**：完善部署指南和故障排除说明

### v2.0.0 (2024-12-XX)
- **🎉 新功能**：CSV文件分片上传，支持大文件
- **🏷️ 新功能**：产品标签系统，支持多种显示模式
- **🔧 优化**：智能价格处理引擎
- **�� 新功能**：用户权限管理系统

## 最新更新

### 2025年1月 - 标签映射问题修复
**问题描述**：用户反馈标签映射存在问题，标签显示不正确或无法显示。

**根本原因分析**：
1. **索引列配置缺失**：部分数据源的`index_column`设置为`null`，导致产品卡片的`data-product-index`属性为空
2. **标签配置验证不足**：存在字段名为空或不存在于数据源中的无效标签配置
3. **匹配逻辑缺乏调试信息**：难以诊断标签映射失败的具体原因

**修复措施**：
1. **修复索引列配置**：为"咩哥数据"数据源设置`index_column`为"货号SPU"
2. **增强标签配置验证**：
   - 添加详细的配置验证逻辑
   - 自动清理无效的标签配置
   - 提供清晰的错误提示和修复建议
3. **改进匹配算法**：
   - 增加详细的调试日志
   - 统计匹配成功/失败的数量
   - 提供具体的失败原因分析
4. **自动修复机制**：
   - 页面加载时自动清理无效标签配置
   - 保留有效配置，移除无效配置
   - 提供配置状态的实时反馈

**技术改进**：
- 在`updateProductTagsDisplay()`函数中添加完整的数据验证和错误处理
- 实现`cleanupTagConfigurations()`函数自动清理无效配置
- 增强日志输出，便于问题诊断和调试
- 改进用户体验，提供清晰的状态反馈

**验证方法**：
1. 检查浏览器控制台，确认标签映射统计信息
2. 验证所有有效标签配置都能正确显示
3. 确认无效配置被自动清理且有相应提示

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 详情请见 [LICENSE](LICENSE) 文件

## 📞 技术支持

如有问题，请提供以下信息：
1. 错误描述和重现步骤
2. 浏览器控制台错误信息
3. 服务器日志内容
4. 系统环境信息

---

**ProductView2** - 让产品数据分析变得简单高效 🚀
