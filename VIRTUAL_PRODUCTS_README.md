# 虚拟产品功能使用指南

## 功能概述

虚拟产品功能允许用户在产品画布中添加临时的虚拟产品，用于填补产品分布中的空白区域或进行产品规划。虚拟产品具有发光边框效果，与真实产品明显区分。

## 主要特性

### 1. 添加虚拟产品
- **空白单元格**：界面保持简洁，无明显视觉元素
  - 鼠标悬停0.5秒后显示绿色圆形"+"按钮
  - 点击"+"按钮即可添加虚拟产品
  - 避免界面混乱，只在需要时显示操作入口
- **有产品单元格**：鼠标悬停时会显示绿色的"+"按钮
- 简洁的设计让界面更加清爽，减少视觉干扰

### 2. 虚拟产品编辑弹窗（极简设计）
- **产品图片**：支持多种方式上传（与产品描述至少填写一项）
  - 点击上传区域选择文件
  - 拖拽图片文件到上传区域
  - 直接粘贴图片（Ctrl+V）
  - 支持 JPG、PNG、GIF 格式，最大5MB
- **产品描述**：产品的详细描述（与产品图片至少填写一项）

**注意**：为了保持界面简洁和操作高效，已移除产品名称、价格、品牌等字段，只保留最核心的图片和描述功能。

### 3. 虚拟产品卡片特征
- **发光边框**：绿色边框带有呼吸动画效果
- **虚拟标签**：右上角显示"虚拟"标签
- **操作按钮**：鼠标悬停时显示编辑和删除按钮
  - ✏ 编辑按钮：修改虚拟产品信息
  - × 删除按钮：删除虚拟产品（需确认）

### 4. 数据持久化
- 虚拟产品数据保存在浏览器的localStorage中
- 刷新页面后虚拟产品仍然存在
- 数据按X轴和Y轴位置进行组织存储

## 使用场景

### 1. 产品规划
当发现某个品牌×店铺组合缺少产品时，可以添加虚拟产品来：
- 标记需要采购的产品类型
- 记录产品想法和规划
- 可视化完整的产品布局

### 2. 市场分析
- 标记竞争对手的产品
- 记录市场机会点
- 分析产品分布的完整性

### 3. 团队协作
- 不同团队成员可以添加各自的产品想法
- 通过虚拟产品进行讨论和规划
- 明确的视觉区分避免混淆

## 操作指南

### 添加虚拟产品
1. 在产品画布中找到目标位置（X轴×Y轴交叉点）
2. 在空白单元格中，鼠标悬停0.5秒
3. 绿色圆形"+"按钮会出现在单元格中央
4. 点击"+"按钮打开虚拟产品编辑弹窗
5. 至少填写以下一项：
   - 上传产品图片，或
   - 填写产品描述
6. 点击"保存产品"按钮
7. 虚拟产品会立即显示在对应的X轴Y轴位置，带有发光边框效果

### 编辑虚拟产品
1. 鼠标悬停在虚拟产品卡片上
2. 点击编辑按钮（✏）
3. 修改产品信息
4. 点击"保存产品"按钮

### 删除虚拟产品
1. 鼠标悬停在虚拟产品卡片上
2. 点击删除按钮（×）
3. 在确认对话框中点击"确定"

### 快捷键
- **ESC键**：关闭虚拟产品编辑弹窗
- **Ctrl+V**：在弹窗中粘贴图片

## 简洁交互设计

### 悬停延迟显示
- **0.5秒延迟**：鼠标悬停0.5秒后才显示添加按钮
- **避免误触**：防止鼠标快速移动时意外显示按钮
- **界面简洁**：保持产品画布的视觉清爽
- **按需显示**：只在用户有明确意图时显示操作入口

### 灵活的内容要求
- **降低门槛**：不强制要求产品名称
- **实用导向**：图片或描述至少一项，满足实际使用需求
- **快速添加**：减少必填字段，提高添加效率

## 技术特性

### 响应式设计
- 在移动设备上自动调整弹窗大小
- 按钮和操作区域适配触摸操作

### 图片处理
- 自动压缩和优化图片显示
- 支持多种图片格式
- 安全的文件大小限制

### 数据安全
- 所有数据存储在本地浏览器中
- 不会上传到服务器
- 清除浏览器数据会删除虚拟产品

## 注意事项

1. **数据备份**：虚拟产品数据存储在浏览器本地，建议定期备份重要数据
2. **浏览器兼容性**：建议使用现代浏览器（Chrome、Firefox、Safari、Edge）
3. **图片大小**：上传的图片会转换为Base64格式存储，大图片可能影响性能
4. **存储限制**：浏览器localStorage有大小限制，避免添加过多大图片

## 故障排除

### 虚拟产品不显示
- 检查浏览器控制台是否有JavaScript错误
- 确认localStorage功能正常
- 尝试刷新页面

### 图片上传失败
- 检查图片格式是否支持
- 确认图片大小不超过5MB
- 尝试使用其他图片

### 弹窗无法打开
- 检查是否有弹窗拦截器
- 确认JavaScript功能正常
- 尝试使用测试函数：在控制台输入 `testVirtualProduct()`

## 开发者信息

虚拟产品功能完全在前端实现，主要文件：
- `app/templates/index.html`：包含所有HTML、CSS和JavaScript代码
- 使用localStorage进行数据持久化
- 支持图片的Base64编码存储
