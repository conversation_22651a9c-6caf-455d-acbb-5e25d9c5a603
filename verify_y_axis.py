#!/usr/bin/env python3
"""
Y轴重写验证脚本
检查Y轴相关的CSS和JavaScript是否正确实现
"""

import re
import os

def check_css_styles():
    """检查CSS样式是否正确实现"""
    print("🎨 检查CSS样式...")
    
    with open('app/templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = {
        "新Y轴主分类样式": r"\.product-table tbody th:first-child",
        "新Y轴子分类样式": r"\.product-table tbody th:nth-child\(2\)",
        "Sticky定位": r"position:\s*sticky",
        "Left定位": r"left:\s*0|left:\s*120px",
        "Top定位": r"top:\s*108px",
        "Z-index设置": r"z-index:\s*100|z-index:\s*99",
        "产品画布容器": r"\.product-canvas",
    }
    
    results = {}
    for check_name, pattern in checks.items():
        matches = re.findall(pattern, content, re.IGNORECASE)
        results[check_name] = len(matches) > 0
        print(f"  {'✅' if results[check_name] else '❌'} {check_name}: {'找到' if results[check_name] else '未找到'}")
    
    return results

def check_javascript_functions():
    """检查JavaScript函数是否正确实现"""
    print("\n🔧 检查JavaScript函数...")
    
    with open('app/templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = {
        "initializeYAxis函数": r"function initializeYAxis\(\)",
        "setupYAxisScrollListener函数": r"function setupYAxisScrollListener\(\)",
        "processYAxisTextReverse函数": r"function processYAxisTextReverse\(",
        "ensureYAxisStyles函数": r"function ensureYAxisStyles\(",
        "validateYAxisPosition函数": r"function validateYAxisPosition\(\)",
        "Y轴初始化调用": r"initializeYAxis\(\)",
        "滚动监听设置": r"setupYAxisScrollListener\(\)",
    }
    
    results = {}
    for check_name, pattern in checks.items():
        matches = re.findall(pattern, content, re.IGNORECASE)
        results[check_name] = len(matches) > 0
        print(f"  {'✅' if results[check_name] else '❌'} {check_name}: {'找到' if results[check_name] else '未找到'}")
    
    return results

def check_removed_old_code():
    """检查是否移除了旧的有问题的代码"""
    print("\n🧹 检查旧代码移除情况...")
    
    with open('app/templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    old_patterns = {
        "旧reverseYAxisText函数": r"function reverseYAxisText\(\)",
        "旧fixYAxisTextOrientation函数": r"function fixYAxisTextOrientation\(\)",
        "旧fixYAxisStickyPosition函数": r"function fixYAxisStickyPosition\(\)",
        "旧debugYAxisElements函数": r"function debugYAxisElements\(\)",
        "旧cleanupStrayNumbers函数": r"function cleanupStrayNumbers\(\)",
        "复杂的!important声明": r"!important.*!important",
        "注释掉的contain属性": r"/\*.*contain:.*\*/",
    }
    
    results = {}
    for check_name, pattern in old_patterns.items():
        matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
        results[check_name] = len(matches) == 0  # 应该为0（已移除）
        print(f"  {'✅' if results[check_name] else '❌'} {check_name}: {'已移除' if results[check_name] else '仍存在'}")
    
    return results

def check_html_structure():
    """检查HTML结构是否正确"""
    print("\n📄 检查HTML结构...")
    
    with open('app/templates/index.html', 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = {
        "Y轴主分类class": r'class="y-main-category-header"',
        "Y轴子分类class": r'class="y-sub-category-header"',
        "产品画布ID": r'id="productCanvas"',
        "产品表格class": r'class="product-table"',
        "tbody结构": r"<tbody>",
        "th标签": r"<th",
    }
    
    results = {}
    for check_name, pattern in checks.items():
        matches = re.findall(pattern, content, re.IGNORECASE)
        results[check_name] = len(matches) > 0
        print(f"  {'✅' if results[check_name] else '❌'} {check_name}: {'找到' if results[check_name] else '未找到'}")
    
    return results

def main():
    """主函数"""
    print("=" * 60)
    print("🔍 Y轴重写验证报告")
    print("=" * 60)
    
    css_results = check_css_styles()
    js_results = check_javascript_functions()
    cleanup_results = check_removed_old_code()
    html_results = check_html_structure()
    
    # 统计结果
    total_checks = len(css_results) + len(js_results) + len(cleanup_results) + len(html_results)
    passed_checks = sum([
        sum(css_results.values()),
        sum(js_results.values()),
        sum(cleanup_results.values()),
        sum(html_results.values())
    ])
    
    print("\n" + "=" * 60)
    print("📊 总结报告")
    print("=" * 60)
    print(f"总检查项: {total_checks}")
    print(f"通过检查: {passed_checks}")
    print(f"失败检查: {total_checks - passed_checks}")
    print(f"成功率: {passed_checks/total_checks*100:.1f}%")
    
    if passed_checks == total_checks:
        print("\n🎉 Y轴重写验证完全通过！")
        print("✅ 所有新功能已正确实现")
        print("✅ 所有旧代码已成功移除")
        print("✅ HTML结构完整正确")
    else:
        print(f"\n⚠️  还有 {total_checks - passed_checks} 项需要修复")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
