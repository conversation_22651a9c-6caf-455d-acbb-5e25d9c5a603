1. 系统架构
- 系统分为两个主要入口：
  1. 用户端: 普通用户使用，主要用于数据查看、分析和个性化配置。
  2. 后台: 管理员使用，主要用于数据源配置、用户管理和系统级设置。
2. 功能需求
2.1. 认证系统 1. 登录功能: * 普通用户登录（账号、密码、登录按钮） * 管理员登录（账号、密码、管理员登录按钮） * 权限分级控制，确保不同角色用户访问对应的功能模块。
CopyInsert
  
2.2. 用户端功能

1.  
**数据源选择**
:
    *   用户可从顶部导航栏选择管理员预配置的数据源列表。
    *   清晰展示每个数据源的名称（由管理员设置）。
    *   用户选择数据源后，可查看该数据源的公告信息。
    *   支持在不同数据源间快速切换，动态加载对应数据。
2.  
**产品展示与核心交互区 (X/Y轴视图)**
:
    *   
**坐标轴自定义**
: 用户可在左侧操作区或通过快捷设置，选择X轴和Y轴展示的数据字段。
    *   
**产品卡片展示**
:
        *   在X/Y轴构成的网格中以卡片形式展示产品。
        *   产品卡片全量展示，不限制数量（继承现有优化）。
        *   （见UI/UX部分对卡片设计的进一步描述）
    *   
**产品标签系统**
:
        *   支持在产品卡片上显示标签。
        *   标签配置遵循简洁UI设计原则（配置入口见左侧操作区）。
    *   
**视图模式调整**
: 支持在不同视图模式间切换（例如：网格视图、列表视图）。
    *   
**排序规则**
: 用户可通过顶部操作栏选择预设或自定义的排序规则，对产品数据进行排序。
    *   
**便签/备注操作**
:
        *   顶部操作栏提供“展开所有便签”功能，方便快速浏览所有产品的备注信息。
        *   产品卡片上应有明显入口进行备注的添加/编辑。
    *   
**边框高亮字段**
: 顶部操作栏提供选项，允许用户选择一个字段，使其在产品卡片上以边框高亮等形式突出显示。
    *   
**全屏查看**
: 顶部操作栏提供“导出为图片”和“全屏查看”功能。
3.  
**筛选与习惯配置 (左侧操作区)**
:
    *   
**筛选器**
:
        *   提供多维度筛选器（如：款式、品牌、价格区间、品类等）。
        *   支持高级筛选逻辑（例如AND/OR组合）和自定义筛选项。
    *   
**习惯配置 (分析视图保存与加载)**
:
        *   用户可以将当前的筛选条件、XY轴设置、排序规则等保存为一个“习惯”。
        *   
**习惯名称与描述**
: 用户可以为每个“习惯”命名并添加描述。
        *   
**保存内容**
: “习惯”应保存筛选条件、默认XY轴字段、默认排序条件。
        *   用户可以加载已保存的“习惯”，快速恢复分析场景。
        *   左侧操作区提供“习惯配置”入口，管理已保存的习惯。
        *   鼠标悬停在已设置的习惯名称上时，应有提示（Tooltip）显示该习惯的完整描述。
4.  
**产品详情与备注**
:
    *   
**产品详细信息查看**
: 点击产品卡片或特定按钮，可查看产品完整详情。
    *   
**备注添加与编辑**
:
        *   支持为单个产品添加或编辑备注。
        *   备注功能与飞书多维表格集成，实现数据同步（主要为应用端到飞书的同步）。
5.  
**数据可视化 (除核心X/Y轴网格视图外)**
:
    *   考虑支持其他辅助图表展示方式（如统计摘要图表）。
    *   数据网格/列表视图下，支持自定义显示列和列宽调整。
    *   可视化参数自定义（如颜色区分、图表元素显隐等）。

2.3. 管理员功能 (后台)

1.  
**数据源管理**
:
    *   
**飞书链接配置**
: 通过提供飞书多维表格的分享链接或API必要信息来添加数据源。
    *   
**本地文件上传**
: 支持上传CSV等标准格式的本地文件作为数据源。
    *   
**数据源命名**
: 为每个配置的数据源设置易于用户理解和选择的名称。
    *   
**数据源公告**
: 为每个数据源设置公告信息，用户在选择该数据源时可见。
    *   
**数据源权限**
: （可选，若需）设置特定数据源对特定用户组的可见性。
    *   管理已配置数据源（编辑、删除、更新）。
2.  
**用户管理**
:
    *   用户账户创建、编辑、禁用。
    *   用户角色与权限分配。
    *   查看用户活动日志。
3.  
**系统设置**
:
    *   飞书集成参数配置（如App ID, App Secret等）。
    *   标签系统全局配置（如预定义标签、颜色等）。
    *   分析维度字段管理（定义哪些字段可用于X/Y轴、筛选等）。
3. UI/UX设计
3.1. 用户端界面 * 整体布局: * 顶部导航/操作栏: 包括Logo/产品名称、数据源选择器、展开所有便签、边框高亮字段选择、排序规则、导出为图片/全屏按钮、用户信息/登出。 * 左侧可收起操作面板: * 提供“一键收起/展开”功能。 * 包含筛选器区域。 * 包含“习惯配置”区域 (习惯名称输入、习惯描述输入、设置展示默认XY轴、设置展示排序条件、设置展示筛选条件按钮、已保存习惯列表)。 * 包含“标签配置”区域。 * 中间主要内容展示区: 展示产品数据的X/Y轴网格视图或其他选定视图模式。
CopyInsert

3.2. 管理员后台界面

*   标准后台管理界面风格，左侧导航菜单，右侧内容区。
*   主要模块包括：数据源管理面板、用户管理面板、系统配置面板。

3.3. 产品卡片设计

*   优化信息展示密度，确保关键信息清晰可见。
*   
**可自定义展示内容**
: （建议）管理员可配置不同数据源的默认卡片展示字段，或用户可个性化选择卡片上显示哪些字段。
*   **快速操作按钮**: 在卡片上或悬浮时显示，例如“查看详情”、“添加/编辑备注”。
4. 技术实现
4.1. 数据源管理机制 * 飞书API集成：安全高效地读取飞书多维表格数据。 * 本地文件解析系统：支持CSV等常见数据格式的解析与校验。 * 数据缓存机制：对不常变动的数据源进行缓存，提高加载速度和用户体验。
CopyInsert

4.2. 性能优化

*   大数据量下的前端渲染性能优化（虚拟滚动、懒加载等）。
*   数据查询和筛选操作的后端或前端优化。
*   组件化开发，按需加载。

4.3. 数据同步

*   
**备注同步**
: 确保用户在看板中添加/编辑的备注能准确同步到对应的飞书多维表格中。
*   
**数据源更新**
: 管理员更新数据源后，用户端应能获取到最新数据（可采用手动刷新或定时检查更新机制）。
*   确保本地缓存数据（如有）与远程数据源的一致性。
5. 数据流程
5.1. 数据源配置流程 (管理员) 1. 管理员登录后台。 2. 进入数据源管理模块。 3. 选择添加数据源方式（飞书链接 / 本地文件）。 4. 填写数据源名称、公告信息、并完成相应配置（链接/上传文件）。 5. （可选）配置数据源访问权限。 6. 保存配置，数据源对用户端可见。
CopyInsert

5.2. 用户数据访问与分析流程 (用户端)

1.  用户登录系统。
2.  从顶部导航栏选择一个数据源。
3.  系统加载并展示对应数据源的产品数据，并显示公告。
4.  用户使用左侧面板的筛选器、习惯配置、标签配置等功能进行数据分析。
5.  用户在主展示区与产品卡片交互，查看详情，添加备注。
6.  用户使用顶部操作栏功能进行排序、高亮、导出等操作。
