from app import app
import os
import time
import shutil
import signal
import psutil
import subprocess
import sys
from datetime import datetime

def find_existing_process():
    """查找现有的Flask进程（排除当前进程）"""
    current_pid = os.getpid()
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['pid'] == current_pid:
                continue
            cmdline = ' '.join(proc.info['cmdline'])
            if ('python' in proc.info['name'].lower() and 
                ('run.py' in cmdline or ':5001' in cmdline)):
                return proc.info['pid']
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    return None

def auto_update_and_restart():
    """自动更新和重启处理"""
    print("=" * 50)
    print("🚀 ProductView2 智能启动中...")
    
    try:
        project_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 1. 检查是否有旧进程在运行
        old_pid = find_existing_process()
        if old_pid:
            print(f"⏹️  发现旧进程 PID: {old_pid}，正在停止...")
            try:
                os.kill(old_pid, signal.SIGTERM)
                time.sleep(2)
                # 检查是否已停止
                if find_existing_process():
                    print("强制停止旧进程...")
                    os.kill(old_pid, signal.SIGKILL)
                    time.sleep(1)
                print("✅ 旧进程已停止")
            except:
                print("✅ 旧进程已停止")
        
        # 2. 清理Python缓存
        print("🧹 清理Python缓存...")
        cache_cleaned = 0
        for root, dirs, files in os.walk(project_dir):
            # 删除 __pycache__ 目录
            if '__pycache__' in dirs:
                pycache_dir = os.path.join(root, '__pycache__')
                try:
                    shutil.rmtree(pycache_dir)
                    cache_cleaned += 1
                except:
                    pass
            
            # 删除 .pyc 文件
            for file in files:
                if file.endswith('.pyc'):
                    try:
                        os.remove(os.path.join(root, file))
                        cache_cleaned += 1
                    except:
                        pass
        
        if cache_cleaned > 0:
            print(f"   ✅ 清理了 {cache_cleaned} 个缓存文件/目录")
        else:
            print("   ✅ 缓存已是最新")
        
        # 3. 启动信息
        print("🎉 应用已更新并启动！")
        print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌐 访问地址: http://0.0.0.0:5001")
        print("💡 提示: 应用已启动完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"⚠️  自动更新过程中出现问题: {e}")
        print("继续启动应用...")
        print("=" * 50)

if __name__ == '__main__':
    # 🔥 关键：启动时自动处理一切更新逻辑
    auto_update_and_restart()

    # 检查是否在生产环境
    debug_mode = os.environ.get('FLASK_ENV') != 'production'

    # 检查命令行参数中的端口
    port = 5001
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效的端口号: {sys.argv[1]}，使用默认端口 5001")

    # 即使在生产环境也启用模板自动重载，确保部署更新能立即生效
    app.jinja_env.auto_reload = True
    app.config['TEMPLATES_AUTO_RELOAD'] = True

    print(f"🌐 启动服务器在端口: {port}")
    app.run(debug=debug_mode, port=port, host='0.0.0.0')