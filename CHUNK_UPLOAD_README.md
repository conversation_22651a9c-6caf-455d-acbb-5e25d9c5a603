# 分片上传功能优化说明

## 概述

为了解决超大型CSV文件上传时浏览器卡顿和崩溃的问题，我们实现了分片上传功能。该功能将大文件分割成小块进行上传，提供了更好的用户体验和更高的可靠性。

## 主要特性

### 1. 分片上传
- **分片大小**: 默认1MB每片，可配置
- **并发上传**: 支持最多3个分片同时上传
- **断点续传**: 支持暂停和恢复上传
- **自动重试**: 失败的分片会自动重试，最多3次

### 2. 进度显示
- **实时进度条**: 显示上传百分比
- **详细状态**: 显示已上传分片数量和文件大小
- **速度显示**: 显示当前上传速度
- **文件信息**: 显示文件名和总大小

### 3. 用户控制
- **开始上传**: 手动开始分片上传过程
- **暂停/恢复**: 可以随时暂停和恢复上传
- **取消上传**: 取消上传并清理临时文件
- **错误重试**: 上传失败时可以重新尝试

## 技术实现

### 前端 (JavaScript)
- **ChunkUploader类**: 核心分片上传逻辑
- **文件分片**: 使用File.slice()方法分割文件
- **并发控制**: 使用Promise.all()控制并发数量
- **状态管理**: 跟踪上传状态和进度

### 后端 (Flask)
- **分片接收**: `/upload_chunk` 路由处理分片上传
- **文件合并**: 所有分片上传完成后自动合并
- **临时存储**: 分片临时存储在 `uploads/temp/` 目录
- **状态检查**: `/check_upload_status` 路由检查上传状态
- **取消处理**: `/cancel_upload` 路由清理临时文件

## 文件结构

```
app/
├── static/js/
│   └── chunk-upload.js          # 分片上传JavaScript库
├── templates/admin/
│   ├── add_datasource.html      # 添加数据源页面（已更新）
│   └── update_datasource_csv.html # 更新CSV页面（已更新）
└── routes.py                    # 后端路由（已更新）
uploads/
├── csv/                         # 最终CSV文件存储
└── temp/                        # 临时分片存储
```

## 配置参数

在 `app/routes.py` 中可以调整以下配置：

```python
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB 最大文件大小
app.config['CHUNK_SIZE'] = 1024 * 1024  # 1MB 分片大小
```

在 `chunk-upload.js` 中可以调整：

```javascript
chunkSize: 1024 * 1024,  // 分片大小
maxRetries: 3,           // 最大重试次数
concurrency: 3           // 并发上传数量
```

## 使用方法

### 管理员添加数据源
1. 选择"本地CSV"数据源类型
2. 点击"选择CSV文件上传"选择文件
3. 系统显示上传进度界面
4. 点击"开始上传"按钮
5. 等待上传完成，系统自动识别列名
6. 配置其他选项并保存

### 更新CSV数据源
1. 在数据源管理页面点击"更新CSV"
2. 选择新的CSV文件
3. 使用分片上传功能上传文件
4. 系统自动处理列配置兼容性
5. 确认更新

## 优势对比

### 传统上传 vs 分片上传

| 特性 | 传统上传 | 分片上传 |
|------|----------|----------|
| 大文件支持 | 容易超时/崩溃 | 稳定可靠 |
| 进度显示 | 无或不准确 | 实时精确 |
| 断点续传 | 不支持 | 支持 |
| 错误恢复 | 需要重新开始 | 只重传失败分片 |
| 浏览器占用 | 高内存占用 | 低内存占用 |
| 网络适应性 | 对网络要求高 | 适应不稳定网络 |

## 错误处理

### 常见错误及解决方案

1. **文件格式错误**
   - 错误: "只支持CSV文件格式"
   - 解决: 确保上传的文件扩展名为.csv

2. **网络中断**
   - 错误: "分片上传失败"
   - 解决: 点击"继续"按钮恢复上传

3. **服务器空间不足**
   - 错误: "保存文件时出错"
   - 解决: 联系管理员清理服务器空间

4. **文件过大**
   - 错误: "文件大小超过限制"
   - 解决: 分割文件或联系管理员调整限制

## 性能优化建议

1. **服务器端**
   - 定期清理临时文件目录
   - 监控磁盘空间使用
   - 调整并发处理能力

2. **客户端**
   - 使用稳定的网络连接
   - 避免在上传过程中关闭浏览器
   - 大文件建议在网络空闲时上传

## 兼容性

- **浏览器**: 支持现代浏览器（Chrome 60+, Firefox 55+, Safari 11+）
- **文件大小**: 理论上无限制，建议单文件不超过2GB
- **并发**: 自动根据网络状况调整
- **向后兼容**: 保留传统上传方式作为备选

## 监控和日志

系统会记录以下信息：
- 上传开始和完成时间
- 文件大小和分片数量
- 错误和重试次数
- 用户操作日志

管理员可以通过Flask日志查看详细的上传过程信息。 