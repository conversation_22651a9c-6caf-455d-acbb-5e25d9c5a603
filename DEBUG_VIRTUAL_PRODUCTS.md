# 虚拟产品调试指南

## 问题描述
虚拟产品添加后不显示在产品画布中。

## 调试步骤

### 1. 打开浏览器开发者工具
- 按 F12 或右键点击页面选择"检查"
- 切换到 Console（控制台）标签

### 2. 运行调试命令

#### 检查页面中的所有单元格
```javascript
debugCells()
```
这会显示页面中所有的单元格信息，包括X轴和Y轴值。

#### 检查当前虚拟产品数据
```javascript
console.log('当前虚拟产品:', JSON.parse(localStorage.getItem('virtualProducts') || '{}'))
```

#### 添加测试虚拟产品
```javascript
addTestVirtualProduct()
```
这会自动在第一个空白单元格中添加一个测试虚拟产品。

#### 强制刷新虚拟产品显示
```javascript
forceRefreshVirtual()
```

#### 清除所有虚拟产品数据
```javascript
localStorage.removeItem('virtualProducts')
console.log('虚拟产品数据已清除')
```

### 3. 手动测试添加流程

#### 测试弹窗功能
```javascript
testVirtualProduct()
```

#### 检查空白悬停区域
```javascript
checkEmptyAreas()
```

### 4. 常见问题排查

#### 问题1：找不到单元格
如果控制台显示"未找到单元格"，可能的原因：
- X轴或Y轴值包含特殊字符
- 页面结构发生变化
- 选择器不匹配

**解决方法：**
```javascript
// 检查所有单元格的实际属性值
document.querySelectorAll('[data-x-axis][data-y-axis]').forEach((cell, index) => {
    console.log(`单元格 ${index}:`, {
        xAxis: cell.dataset.xAxis,
        yAxis: cell.dataset.yAxis,
        innerHTML: cell.innerHTML.substring(0, 100) + '...'
    });
});
```

#### 问题2：虚拟产品卡片创建失败
检查虚拟产品卡片的创建过程：
```javascript
// 手动创建虚拟产品卡片
const testCard = createVirtualProductCard({
    id: 'test-123',
    description: '测试产品',
    image: null
}, '测试X轴', '测试Y轴');
console.log('创建的卡片:', testCard);
```

#### 问题3：CSS样式问题
检查虚拟产品卡片的样式：
```javascript
// 检查虚拟产品相关的CSS类
const virtualCards = document.querySelectorAll('.virtual-product');
console.log('找到的虚拟产品卡片:', virtualCards);
virtualCards.forEach(card => {
    console.log('卡片样式:', window.getComputedStyle(card));
});
```

### 5. 完整的调试流程

```javascript
// 1. 清除现有数据
localStorage.removeItem('virtualProducts');

// 2. 检查页面结构
debugCells();

// 3. 添加测试产品
addTestVirtualProduct();

// 4. 检查结果
console.log('虚拟产品数据:', JSON.parse(localStorage.getItem('virtualProducts') || '{}'));
console.log('页面中的虚拟产品卡片:', document.querySelectorAll('.virtual-product'));
```

### 6. 预期结果

正常情况下，你应该看到：
1. `debugCells()` 显示多个单元格信息
2. `addTestVirtualProduct()` 成功创建并保存产品
3. 页面中出现带有绿色发光边框的虚拟产品卡片
4. 控制台显示详细的调试信息

### 7. 如果问题仍然存在

请将以下信息提供给开发者：

```javascript
// 收集调试信息
const debugInfo = {
    cells: document.querySelectorAll('[data-x-axis][data-y-axis]').length,
    virtualProducts: JSON.parse(localStorage.getItem('virtualProducts') || '{}'),
    virtualCards: document.querySelectorAll('.virtual-product').length,
    emptyAreas: document.querySelectorAll('.empty-hover-area').length,
    userAgent: navigator.userAgent,
    currentUrl: window.location.href
};
console.log('调试信息:', JSON.stringify(debugInfo, null, 2));
```

## 常见解决方案

### 方案1：页面刷新后重新初始化
```javascript
// 页面加载完成后强制刷新虚拟产品
setTimeout(() => {
    forceRefreshVirtual();
}, 1000);
```

### 方案2：手动触发初始化
```javascript
// 重新初始化虚拟产品功能
initVirtualProducts();
```

### 方案3：检查页面加载状态
```javascript
// 确保页面完全加载
if (document.readyState === 'complete') {
    forceRefreshVirtual();
} else {
    window.addEventListener('load', () => {
        setTimeout(forceRefreshVirtual, 500);
    });
}
```
